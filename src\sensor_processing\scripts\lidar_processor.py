#!/usr/bin/env python3
"""
LiDAR Processor Node
Demonstrates real-time LiDAR processing with object detection and ground segmentation
"""

import rclpy
from rclpy.node import Node
import yaml
import os
from sensor_processing.lidar_processing import LiDARProcessor


def main(args=None):
    """Main function for LiDAR processor node"""
    rclpy.init(args=args)
    
    # Load configuration
    config_path = os.path.join(
        os.path.dirname(__file__), 
        '..', 'config', 'lidar_config.yaml'
    )
    
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        # Default configuration
        config = {
            'processing_rate': 30.0,
            'max_range': 100.0,
            'min_range': 0.5,
            'ground_threshold': 0.2,
            'cluster_tolerance': 0.5,
            'min_cluster_size': 10,
            'max_cluster_size': 5000,
            'voxel_size': 0.1,
            'use_voxel_grid': True,
            'use_roi_filtering': True,
            'enable_threading': True
        }
    
    # Create and run processor
    try:
        processor = LiDARProcessor(config)
        
        processor.get_logger().info("LiDAR processor started")
        processor.get_logger().info(f"Configuration: {config}")
        
        # Spin the node
        rclpy.spin(processor)
        
    except KeyboardInterrupt:
        processor.get_logger().info("LiDAR processor interrupted by user")
    except Exception as e:
        processor.get_logger().error(f"Error in LiDAR processor: {e}")
    finally:
        if 'processor' in locals():
            processor.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
