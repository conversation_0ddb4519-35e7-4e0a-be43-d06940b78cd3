"""
Radar Processing Module
Demonstrates radar signal processing, target detection, and Doppler analysis
"""

import numpy as np
from typing import List, Tuple, Dict, Any, Optional
import time
from dataclasses import dataclass
from scipy import signal
from scipy.fft import fft, fftfreq
import rclpy
from sensor_msgs.msg import PointCloud2
from geometry_msgs.msg import Point, Vector3
from visualization_msgs.msg import Marker, MarkerArray
from std_msgs.msg import Header, ColorRGBA

from .sensor_base import SensorProcessor
from .math_utils import MathUtils, CoordinateTransforms


@dataclass
class RadarTarget:
    """Represents a radar target detection"""
    range: float           # Distance in meters
    azimuth: float        # Azimuth angle in radians
    elevation: float      # Elevation angle in radians
    velocity: float       # Radial velocity in m/s (positive = approaching)
    rcs: float           # Radar cross section in dBsm
    snr: float           # Signal-to-noise ratio in dB
    confidence: float    # Detection confidence [0-1]
    timestamp: float     # Detection timestamp


@dataclass
class RadarFrame:
    """Represents a complete radar frame"""
    timestamp: float
    targets: List[RadarTarget]
    range_doppler_map: np.ndarray
    noise_floor: float
    temperature: float


class RadarProcessor(SensorProcessor):
    """
    Advanced radar processing with CFAR detection and Doppler analysis
    Includes range-Doppler processing, target detection, and tracking
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__('radar_processor', config)
        
        # Radar parameters
        self.frequency = config.get('frequency', 77e9)  # 77 GHz
        self.bandwidth = config.get('bandwidth', 4e9)   # 4 GHz
        self.max_range = config.get('max_range', 200.0)  # meters
        self.range_resolution = config.get('range_resolution', 0.0375)  # meters
        self.max_velocity = config.get('max_velocity', 50.0)  # m/s
        self.velocity_resolution = config.get('velocity_resolution', 0.1)  # m/s
        
        # Processing parameters
        self.num_range_bins = int(self.max_range / self.range_resolution)
        self.num_doppler_bins = config.get('num_doppler_bins', 256)
        self.cfar_guard_cells = config.get('cfar_guard_cells', 4)
        self.cfar_training_cells = config.get('cfar_training_cells', 16)
        self.cfar_threshold_factor = config.get('cfar_threshold_factor', 10.0)
        
        # Windowing and filtering
        self.range_window = signal.windows.hann(self.num_range_bins)
        self.doppler_window = signal.windows.hann(self.num_doppler_bins)
        
        # Calibration
        self.noise_floor = -80.0  # dBm
        self.calibration_offset = 0.0  # dB
        
        # Target tracking
        self.previous_targets = []
        self.target_id_counter = 0
        
    def initialize_sensor(self):
        """Initialize radar-specific components"""
        # Publishers
        self.targets_pub = self.create_publisher(
            MarkerArray,
            'radar_targets',
            self.qos_profile
        )
        
        self.range_doppler_pub = self.create_publisher(
            PointCloud2,
            'range_doppler_map',
            self.qos_profile
        )
        
        # Subscribers
        self.radar_data_sub = self.create_subscription(
            PointCloud2,  # Using PointCloud2 as generic data container
            'raw_radar_data',
            self.radar_data_callback,
            self.qos_profile
        )
        
        self.get_logger().info("Radar processor initialized")
    
    def radar_data_callback(self, msg):
        """Handle incoming radar data"""
        self.add_data_to_queue(msg)
    
    def process_data(self, radar_msg) -> Optional[Dict[str, Any]]:
        """
        Main radar processing pipeline
        Returns processed radar data with detected targets
        """
        try:
            # Generate synthetic radar data for demonstration
            raw_data = self.generate_synthetic_radar_data()
            
            # Step 1: Range processing (FFT)
            range_profile = self.range_processing(raw_data)
            
            # Step 2: Doppler processing (FFT)
            range_doppler_map = self.doppler_processing(range_profile)
            
            # Step 3: CFAR detection
            detections = self.cfar_detection(range_doppler_map)
            
            # Step 4: Target extraction
            targets = self.extract_targets(detections, range_doppler_map)
            
            # Step 5: Target tracking and association
            tracked_targets = self.track_targets(targets)
            
            # Step 6: Coordinate transformation
            cartesian_targets = self.polar_to_cartesian(tracked_targets)
            
            return {
                'timestamp': time.time(),
                'targets': cartesian_targets,
                'range_doppler_map': range_doppler_map,
                'raw_detections': detections,
                'noise_floor': self.noise_floor,
                'header': radar_msg.header if hasattr(radar_msg, 'header') else None
            }
            
        except Exception as e:
            self.get_logger().error(f"Error processing radar data: {e}")
            return None
    
    def generate_synthetic_radar_data(self) -> np.ndarray:
        """Generate synthetic radar data for demonstration"""
        # Create synthetic targets
        targets = [
            {'range': 50.0, 'velocity': -10.0, 'rcs': 20.0},  # Approaching vehicle
            {'range': 75.0, 'velocity': 5.0, 'rcs': 15.0},   # Receding vehicle
            {'range': 120.0, 'velocity': 0.0, 'rcs': 10.0},  # Stationary object
        ]
        
        # Generate complex baseband signal
        num_samples = self.num_range_bins
        num_chirps = self.num_doppler_bins
        
        # Initialize with noise
        data = (np.random.randn(num_chirps, num_samples) + 
                1j * np.random.randn(num_chirps, num_samples)) * 0.1
        
        # Add target signals
        for target in targets:
            range_bin = int(target['range'] / self.range_resolution)
            doppler_bin = int((target['velocity'] / self.velocity_resolution) + num_chirps // 2)
            
            if 0 <= range_bin < num_samples and 0 <= doppler_bin < num_chirps:
                # Add target signal with appropriate amplitude
                amplitude = 10**(target['rcs'] / 20.0)  # Convert dBsm to linear
                phase = np.random.uniform(0, 2*np.pi)
                data[doppler_bin, range_bin] += amplitude * np.exp(1j * phase)
        
        return data
    
    def range_processing(self, raw_data: np.ndarray) -> np.ndarray:
        """
        Perform range processing using FFT
        
        Args:
            raw_data: Complex baseband data [num_chirps, num_samples]
        
        Returns:
            Range profiles [num_chirps, num_range_bins]
        """
        # Apply range window
        windowed_data = raw_data * self.range_window[np.newaxis, :]
        
        # Range FFT
        range_fft = fft(windowed_data, n=self.num_range_bins, axis=1)
        
        # Take magnitude
        range_profile = np.abs(range_fft)
        
        return range_profile
    
    def doppler_processing(self, range_profile: np.ndarray) -> np.ndarray:
        """
        Perform Doppler processing using FFT
        
        Args:
            range_profile: Range profiles [num_chirps, num_range_bins]
        
        Returns:
            Range-Doppler map [num_doppler_bins, num_range_bins]
        """
        # Apply Doppler window
        windowed_profile = range_profile * self.doppler_window[:, np.newaxis]
        
        # Doppler FFT
        doppler_fft = fft(windowed_profile, n=self.num_doppler_bins, axis=0)
        
        # Take magnitude and convert to dB
        range_doppler_map = 20 * np.log10(np.abs(doppler_fft) + 1e-10)
        
        # Shift zero Doppler to center
        range_doppler_map = np.fft.fftshift(range_doppler_map, axes=0)
        
        return range_doppler_map
    
    def cfar_detection(self, range_doppler_map: np.ndarray) -> List[Tuple[int, int]]:
        """
        Constant False Alarm Rate (CFAR) detection
        
        Args:
            range_doppler_map: Range-Doppler map in dB
        
        Returns:
            List of (doppler_bin, range_bin) detections
        """
        detections = []
        
        guard = self.cfar_guard_cells
        training = self.cfar_training_cells
        threshold_factor = self.cfar_threshold_factor
        
        # Process each cell in the range-Doppler map
        for d_idx in range(guard + training, range_doppler_map.shape[0] - guard - training):
            for r_idx in range(guard + training, range_doppler_map.shape[1] - guard - training):
                
                # Cell under test
                cut_value = range_doppler_map[d_idx, r_idx]
                
                # Training cells (excluding guard cells)
                training_cells = []
                
                # Left training cells
                for i in range(d_idx - guard - training, d_idx - guard):
                    for j in range(r_idx - guard - training, r_idx + guard + training + 1):
                        if 0 <= i < range_doppler_map.shape[0] and 0 <= j < range_doppler_map.shape[1]:
                            training_cells.append(range_doppler_map[i, j])
                
                # Right training cells
                for i in range(d_idx + guard + 1, d_idx + guard + training + 1):
                    for j in range(r_idx - guard - training, r_idx + guard + training + 1):
                        if 0 <= i < range_doppler_map.shape[0] and 0 <= j < range_doppler_map.shape[1]:
                            training_cells.append(range_doppler_map[i, j])
                
                # Top training cells
                for i in range(d_idx - guard, d_idx + guard + 1):
                    for j in range(r_idx - guard - training, r_idx - guard):
                        if 0 <= i < range_doppler_map.shape[0] and 0 <= j < range_doppler_map.shape[1]:
                            training_cells.append(range_doppler_map[i, j])
                
                # Bottom training cells
                for i in range(d_idx - guard, d_idx + guard + 1):
                    for j in range(r_idx + guard + 1, r_idx + guard + training + 1):
                        if 0 <= i < range_doppler_map.shape[0] and 0 <= j < range_doppler_map.shape[1]:
                            training_cells.append(range_doppler_map[i, j])
                
                if training_cells:
                    # Calculate threshold
                    noise_level = np.mean(training_cells)
                    threshold = noise_level + threshold_factor
                    
                    # Check for detection
                    if cut_value > threshold:
                        detections.append((d_idx, r_idx))
        
        return detections
    
    def extract_targets(self, detections: List[Tuple[int, int]], 
                       range_doppler_map: np.ndarray) -> List[RadarTarget]:
        """
        Extract target parameters from detections
        
        Args:
            detections: List of (doppler_bin, range_bin) detections
            range_doppler_map: Range-Doppler map in dB
        
        Returns:
            List of RadarTarget objects
        """
        targets = []
        
        for d_bin, r_bin in detections:
            # Convert bin indices to physical units
            range_val = r_bin * self.range_resolution
            doppler_bin_centered = d_bin - self.num_doppler_bins // 2
            velocity = doppler_bin_centered * self.velocity_resolution
            
            # Extract signal parameters
            signal_power = range_doppler_map[d_bin, r_bin]
            
            # Estimate noise floor around detection
            noise_samples = []
            for di in range(max(0, d_bin - 5), min(range_doppler_map.shape[0], d_bin + 6)):
                for ri in range(max(0, r_bin - 5), min(range_doppler_map.shape[1], r_bin + 6)):
                    if abs(di - d_bin) > 2 or abs(ri - r_bin) > 2:  # Exclude signal area
                        noise_samples.append(range_doppler_map[di, ri])
            
            noise_floor = np.mean(noise_samples) if noise_samples else self.noise_floor
            snr = signal_power - noise_floor
            
            # Estimate RCS (simplified)
            rcs = signal_power - self.calibration_offset
            
            # Calculate confidence based on SNR
            confidence = min(1.0, max(0.0, (snr - 10.0) / 20.0))
            
            # For demonstration, assume azimuth = 0, elevation = 0
            azimuth = 0.0
            elevation = 0.0
            
            target = RadarTarget(
                range=range_val,
                azimuth=azimuth,
                elevation=elevation,
                velocity=velocity,
                rcs=rcs,
                snr=snr,
                confidence=confidence,
                timestamp=time.time()
            )
            
            targets.append(target)
        
        return targets
    
    def track_targets(self, current_targets: List[RadarTarget]) -> List[RadarTarget]:
        """
        Simple target tracking using nearest neighbor association
        
        Args:
            current_targets: Current frame targets
        
        Returns:
            Tracked targets with updated confidence
        """
        # For demonstration, just return current targets
        # In practice, you would implement Kalman filtering or particle filtering
        return current_targets
    
    def polar_to_cartesian(self, targets: List[RadarTarget]) -> List[Dict[str, Any]]:
        """
        Convert radar targets from polar to Cartesian coordinates
        
        Args:
            targets: List of RadarTarget objects
        
        Returns:
            List of targets in Cartesian coordinates
        """
        cartesian_targets = []
        
        for target in targets:
            x, y, z = CoordinateTransforms.spherical_to_cartesian(
                target.range, target.azimuth, target.elevation
            )
            
            cartesian_target = {
                'position': [x, y, z],
                'velocity': target.velocity,
                'rcs': target.rcs,
                'snr': target.snr,
                'confidence': target.confidence,
                'timestamp': target.timestamp
            }
            
            cartesian_targets.append(cartesian_target)
        
        return cartesian_targets
    
    def publish_processed_data(self, data: Dict[str, Any]):
        """Publish processed radar data"""
        try:
            # Publish targets as markers
            if 'targets' in data:
                marker_array = self.targets_to_markers(data['targets'])
                self.targets_pub.publish(marker_array)
            
            # Publish range-Doppler map (simplified)
            # In practice, you would convert the 2D map to a proper message format
            
        except Exception as e:
            self.get_logger().error(f"Error publishing radar data: {e}")
    
    def targets_to_markers(self, targets: List[Dict[str, Any]]) -> MarkerArray:
        """Convert radar targets to visualization markers"""
        marker_array = MarkerArray()
        
        for i, target in enumerate(targets):
            marker = Marker()
            marker.header.frame_id = "radar_frame"
            marker.header.stamp = self.get_clock().now().to_msg()
            marker.ns = "radar_targets"
            marker.id = i
            marker.type = Marker.SPHERE
            marker.action = Marker.ADD
            
            # Position
            marker.pose.position.x = target['position'][0]
            marker.pose.position.y = target['position'][1]
            marker.pose.position.z = target['position'][2]
            
            # Scale based on RCS
            scale = max(0.5, min(2.0, target['rcs'] / 20.0))
            marker.scale.x = scale
            marker.scale.y = scale
            marker.scale.z = scale
            
            # Color based on velocity (red = approaching, blue = receding)
            if target['velocity'] < -1.0:  # Approaching
                marker.color = ColorRGBA(r=1.0, g=0.0, b=0.0, a=0.8)
            elif target['velocity'] > 1.0:  # Receding
                marker.color = ColorRGBA(r=0.0, g=0.0, b=1.0, a=0.8)
            else:  # Stationary
                marker.color = ColorRGBA(r=0.0, g=1.0, b=0.0, a=0.8)
            
            marker_array.markers.append(marker)
        
        return marker_array
