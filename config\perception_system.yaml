# Perception System Configuration
# Master configuration file for the complete perception pipeline

# System-wide parameters
system:
  processing_rate: 30.0        # Main processing loop frequency
  enable_logging: true         # Enable detailed logging
  log_level: "INFO"           # Logging level: DEBUG, INFO, WARN, ERROR
  enable_profiling: false     # Enable performance profiling
  
# Sensor configurations
sensors:
  lidar:
    enabled: true
    topic: "/raw_pointcloud"
    frame_id: "lidar_frame"
    max_range: 100.0
    min_range: 0.5
    processing_rate: 30.0
    
  radar:
    enabled: true
    topic: "/raw_radar_data"
    frame_id: "radar_frame"
    frequency: 77e9           # 77 GHz
    bandwidth: 4e9            # 4 GHz
    max_range: 200.0
    max_velocity: 50.0
    processing_rate: 20.0
    
  camera:
    enabled: true
    topic: "/camera/image_raw"
    frame_id: "camera_frame"
    image_width: 1920
    image_height: 1080
    fx: 1000.0               # Focal length x
    fy: 1000.0               # Focal length y
    cx: 960.0                # Principal point x
    cy: 540.0                # Principal point y
    processing_rate: 30.0

# Sensor fusion configuration
fusion:
  enabled: true
  fusion_rate: 20.0           # Fusion processing frequency
  max_association_distance: 5.0  # Maximum distance for data association
  track_timeout: 2.0          # Track timeout in seconds
  min_sensor_agreement: 2     # Minimum sensors required for new track
  
  # Sensor weights for fusion
  sensor_weights:
    lidar: 0.4
    radar: 0.3
    camera: 0.3
    
  # Kalman filter parameters
  kalman:
    process_noise: 0.1
    measurement_noise: 1.0
    initial_covariance: 1000.0

# Object detection and tracking
detection:
  # Object types to detect
  object_types:
    - "vehicle"
    - "person"
    - "bicycle"
    - "motorcycle"
    - "truck"
    - "bus"
    
  # Detection thresholds
  confidence_threshold: 0.5
  nms_threshold: 0.4
  
  # Tracking parameters
  tracking:
    max_age: 30             # Maximum frames to keep lost tracks
    min_hits: 3             # Minimum detections to confirm track
    iou_threshold: 0.3      # IoU threshold for association

# Communication protocols
communication:
  enable_udp: true
  enable_can: false
  enable_serial: false
  
  udp:
    local_ip: "127.0.0.1"
    local_port: 8888
    remote_ip: "127.0.0.1"
    remote_port: 8889
    buffer_size: 65536
    
  can:
    interface: "vcan0"
    bitrate: 500000
    node_id: 0x123
    
  serial:
    port: "/dev/ttyUSB0"
    baudrate: 115200
    timeout: 1.0

# Performance monitoring
performance:
  enable_monitoring: true
  stats_publish_rate: 1.0    # Statistics publishing rate
  max_processing_time: 0.1   # Maximum allowed processing time per frame
  memory_limit_mb: 1000      # Memory usage limit in MB
  
# Visualization
visualization:
  enable_rviz: true
  publish_markers: true
  publish_point_clouds: true
  publish_images: true
  marker_lifetime: 0.1       # Marker lifetime in seconds
  
# Data recording
recording:
  enable_recording: false
  output_directory: "/tmp/perception_data"
  record_raw_data: true
  record_processed_data: true
  max_file_size_mb: 1000

# Safety and validation
safety:
  enable_safety_checks: true
  max_detection_distance: 150.0  # Maximum valid detection distance
  min_object_size: 0.1           # Minimum object size in meters
  max_object_size: 20.0          # Maximum object size in meters
  velocity_sanity_check: 100.0   # Maximum reasonable velocity in m/s

# Coordinate transformations
transforms:
  # Static transforms between sensor frames
  lidar_to_base:
    translation: [0.0, 0.0, 2.0]
    rotation: [0.0, 0.0, 0.0, 1.0]  # quaternion [x, y, z, w]
    
  radar_to_base:
    translation: [3.0, 0.0, 1.0]
    rotation: [0.0, 0.0, 0.0, 1.0]
    
  camera_to_base:
    translation: [2.0, 0.0, 1.5]
    rotation: [0.0, 0.0, 0.0, 1.0]

# Algorithm-specific parameters
algorithms:
  # RANSAC parameters
  ransac:
    max_iterations: 1000
    distance_threshold: 0.1
    min_inliers_ratio: 0.6
    
  # DBSCAN clustering
  dbscan:
    eps: 0.5
    min_samples: 10
    
  # Kalman filter
  kalman_filter:
    dt: 0.05                # Time step
    process_noise_std: 0.1
    measurement_noise_std: 1.0
    
  # Feature extraction
  features:
    sift:
      n_features: 1000
      contrast_threshold: 0.04
      edge_threshold: 10
      
    orb:
      n_features: 1000
      scale_factor: 1.2
      n_levels: 8

# Development and testing
development:
  enable_synthetic_data: true  # Use synthetic data for testing
  enable_debug_output: false   # Enable debug visualizations
  save_debug_images: false     # Save debug images to disk
  test_mode: false            # Enable test mode with known scenarios
