{"title": "Kernels", "description": "Kernels and kernel sessions settings", "jupyter.lab.setting-icon": "ui-components:kernel", "jupyter.lab.setting-icon-label": "<PERSON><PERSON>", "additionalProperties": false, "properties": {"showStatusBarItem": {"type": "boolean", "title": "Show the status bar item", "description": "Whether to show the running kernels item in the status bar", "default": true}, "commsOverSubshells": {"type": "string", "title": "Kernel Comms over subshells", "description": "Whether comm messages should be sent to kernel subshells, if the kernel supports it.", "default": "perCommTarget", "oneOf": [{"const": "disabled", "title": "Disabled"}, {"const": "perComm", "title": "One subshell per comm"}, {"const": "perCommTarget", "title": "One subshell per comm-target"}]}}, "type": "object"}