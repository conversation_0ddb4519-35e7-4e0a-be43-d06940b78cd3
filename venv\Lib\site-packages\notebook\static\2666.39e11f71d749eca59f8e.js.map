{"version": 3, "file": "2666.39e11f71d749eca59f8e.js?v=39e11f71d749eca59f8e", "mappings": ";;;;;;AAAa;AACb;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,WAAW;AACX,mBAAmB,mBAAO,CAAC,KAAc;AACzC,gBAAgB,mBAAO,CAAC,IAAoB;AAC5C,cAAc,mBAAO,CAAC,KAAkB;AACxC,cAAc,mBAAO,CAAC,KAAkB;AACxC,cAAc,mBAAO,CAAC,KAAkB;AACxC,iBAAiB,mBAAO,CAAC,KAAqB;AAC9C,kBAAkB,mBAAO,CAAC,KAAsB;AAChD,cAAc,mBAAO,CAAC,KAAkB;AACxC,gBAAgB,mBAAO,CAAC,KAAoB;AAC5C,iBAAiB,mBAAO,CAAC,KAAqB;AAC9C,iBAAiB,mBAAO,CAAC,KAAqB;AAC9C,iBAAiB,mBAAO,CAAC,KAAqB;AAC9C,kBAAkB,mBAAO,CAAC,KAAsB;AAChD,kBAAkB,mBAAO,CAAC,KAAsB;AAChD,mBAAmB,mBAAO,CAAC,KAAuB;AAClD,oBAAoB,mBAAO,CAAC,KAAwB;AACpD,mBAAmB,mBAAO,CAAC,KAAuB;AAClD,oBAAoB,mBAAO,CAAC,KAAwB;AACpD,mBAAmB,mBAAO,CAAC,KAAuB;AAClD,mBAAmB,mBAAO,CAAC,KAAuB;AAClD,sBAAsB,mBAAO,CAAC,KAA0B;AACxD,yBAAyB,mBAAO,CAAC,KAA6B;AAC9D,kBAAkB,mBAAO,CAAC,KAAsB;AAChD,eAAe,mBAAO,CAAC,KAAmB;AAC1C,eAAe,mBAAO,CAAC,KAAmB;AAC1C,uBAAuB,mBAAO,CAAC,KAA2B;AAC1D,sBAAsB,mBAAO,CAAC,KAA0B;AACxD,kBAAkB,mBAAO,CAAC,KAAsB;AAChD,qBAAqB,mBAAO,CAAC,KAAyB;AACtD,mBAAmB,mBAAO,CAAC,KAAuB;AAClD,sBAAsB,mBAAO,CAAC,KAA0B;AACxD,WAAW,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC/Ea;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,uBAAuB,mBAAO,CAAC,KAAwB;AACvD,eAAe,mBAAO,CAAC,KAAU;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;;;;;;;ACpCa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,sBAAsB;AACtB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C,sBAAsB,mBAAO,CAAC,KAAkB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,kDAAkD,kDAAkD,qCAAqC;AACzI;AACA,CAAC;AACD,sBAAsB;AACtB;;;;;;;AC1Da;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB;AACrB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,iDAAiD,4CAA4C,cAAc;AAC3G;AACA,CAAC;AACD,qBAAqB;AACrB;;;;;;;AC5Da;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA,CAAC;AACD,kBAAkB;AAClB;;;;;;;AClEa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,oCAAoC;AACpC;AACA,CAAC;AACD,iBAAiB;AACjB;;;;;;;AC9Da;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,sCAAsC;AACtC;AACA,CAAC;AACD,mBAAmB;AACnB;;;;;;;AChDa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C,sBAAsB,mBAAO,CAAC,KAAkB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,kDAAkD,qRAAqR;AACpX;AACA,CAAC;AACD,iBAAiB;AACjB;;;;;;;AC9Ea;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B;AAC3B,mBAAmB,mBAAO,CAAC,KAAc;AACzC;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC,mCAAmC;AACnC;AACA;AACA;AACA,CAAC;AACD,2BAA2B;AAC3B", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MML.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlFactory.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/maligngroup.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/malignmark.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mathchoice.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/merror.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mphantom.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mstyle.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/Tree/NodeFactory.js"], "sourcesContent": ["\"use strict\";\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MML = void 0;\nvar MmlNode_js_1 = require(\"./MmlNode.js\");\nvar math_js_1 = require(\"./MmlNodes/math.js\");\nvar mi_js_1 = require(\"./MmlNodes/mi.js\");\nvar mn_js_1 = require(\"./MmlNodes/mn.js\");\nvar mo_js_1 = require(\"./MmlNodes/mo.js\");\nvar mtext_js_1 = require(\"./MmlNodes/mtext.js\");\nvar mspace_js_1 = require(\"./MmlNodes/mspace.js\");\nvar ms_js_1 = require(\"./MmlNodes/ms.js\");\nvar mrow_js_1 = require(\"./MmlNodes/mrow.js\");\nvar mfrac_js_1 = require(\"./MmlNodes/mfrac.js\");\nvar msqrt_js_1 = require(\"./MmlNodes/msqrt.js\");\nvar mroot_js_1 = require(\"./MmlNodes/mroot.js\");\nvar mstyle_js_1 = require(\"./MmlNodes/mstyle.js\");\nvar merror_js_1 = require(\"./MmlNodes/merror.js\");\nvar mpadded_js_1 = require(\"./MmlNodes/mpadded.js\");\nvar mphantom_js_1 = require(\"./MmlNodes/mphantom.js\");\nvar mfenced_js_1 = require(\"./MmlNodes/mfenced.js\");\nvar menclose_js_1 = require(\"./MmlNodes/menclose.js\");\nvar maction_js_1 = require(\"./MmlNodes/maction.js\");\nvar msubsup_js_1 = require(\"./MmlNodes/msubsup.js\");\nvar munderover_js_1 = require(\"./MmlNodes/munderover.js\");\nvar mmultiscripts_js_1 = require(\"./MmlNodes/mmultiscripts.js\");\nvar mtable_js_1 = require(\"./MmlNodes/mtable.js\");\nvar mtr_js_1 = require(\"./MmlNodes/mtr.js\");\nvar mtd_js_1 = require(\"./MmlNodes/mtd.js\");\nvar maligngroup_js_1 = require(\"./MmlNodes/maligngroup.js\");\nvar malignmark_js_1 = require(\"./MmlNodes/malignmark.js\");\nvar mglyph_js_1 = require(\"./MmlNodes/mglyph.js\");\nvar semantics_js_1 = require(\"./MmlNodes/semantics.js\");\nvar TeXAtom_js_1 = require(\"./MmlNodes/TeXAtom.js\");\nvar mathchoice_js_1 = require(\"./MmlNodes/mathchoice.js\");\nexports.MML = (_a = {},\n    _a[math_js_1.MmlMath.prototype.kind] = math_js_1.MmlMath,\n    _a[mi_js_1.MmlMi.prototype.kind] = mi_js_1.MmlMi,\n    _a[mn_js_1.MmlMn.prototype.kind] = mn_js_1.MmlMn,\n    _a[mo_js_1.MmlMo.prototype.kind] = mo_js_1.MmlMo,\n    _a[mtext_js_1.MmlMtext.prototype.kind] = mtext_js_1.MmlMtext,\n    _a[mspace_js_1.MmlMspace.prototype.kind] = mspace_js_1.MmlMspace,\n    _a[ms_js_1.MmlMs.prototype.kind] = ms_js_1.MmlMs,\n    _a[mrow_js_1.MmlMrow.prototype.kind] = mrow_js_1.MmlMrow,\n    _a[mrow_js_1.MmlInferredMrow.prototype.kind] = mrow_js_1.MmlInferredMrow,\n    _a[mfrac_js_1.MmlMfrac.prototype.kind] = mfrac_js_1.MmlMfrac,\n    _a[msqrt_js_1.MmlMsqrt.prototype.kind] = msqrt_js_1.MmlMsqrt,\n    _a[mroot_js_1.MmlMroot.prototype.kind] = mroot_js_1.MmlMroot,\n    _a[mstyle_js_1.MmlMstyle.prototype.kind] = mstyle_js_1.MmlMstyle,\n    _a[merror_js_1.MmlMerror.prototype.kind] = merror_js_1.MmlMerror,\n    _a[mpadded_js_1.MmlMpadded.prototype.kind] = mpadded_js_1.MmlMpadded,\n    _a[mphantom_js_1.MmlMphantom.prototype.kind] = mphantom_js_1.MmlMphantom,\n    _a[mfenced_js_1.MmlMfenced.prototype.kind] = mfenced_js_1.MmlMfenced,\n    _a[menclose_js_1.MmlMenclose.prototype.kind] = menclose_js_1.MmlMenclose,\n    _a[maction_js_1.MmlMaction.prototype.kind] = maction_js_1.MmlMaction,\n    _a[msubsup_js_1.MmlMsub.prototype.kind] = msubsup_js_1.MmlMsub,\n    _a[msubsup_js_1.MmlMsup.prototype.kind] = msubsup_js_1.MmlMsup,\n    _a[msubsup_js_1.MmlMsubsup.prototype.kind] = msubsup_js_1.MmlMsubsup,\n    _a[munderover_js_1.MmlMunder.prototype.kind] = munderover_js_1.MmlMunder,\n    _a[munderover_js_1.MmlMover.prototype.kind] = munderover_js_1.MmlMover,\n    _a[munderover_js_1.MmlMunderover.prototype.kind] = munderover_js_1.MmlMunderover,\n    _a[mmultiscripts_js_1.MmlMmultiscripts.prototype.kind] = mmultiscripts_js_1.MmlMmultiscripts,\n    _a[mmultiscripts_js_1.MmlMprescripts.prototype.kind] = mmultiscripts_js_1.MmlMprescripts,\n    _a[mmultiscripts_js_1.MmlNone.prototype.kind] = mmultiscripts_js_1.MmlNone,\n    _a[mtable_js_1.MmlMtable.prototype.kind] = mtable_js_1.MmlMtable,\n    _a[mtr_js_1.MmlMlabeledtr.prototype.kind] = mtr_js_1.MmlMlabeledtr,\n    _a[mtr_js_1.MmlMtr.prototype.kind] = mtr_js_1.MmlMtr,\n    _a[mtd_js_1.MmlMtd.prototype.kind] = mtd_js_1.MmlMtd,\n    _a[maligngroup_js_1.MmlMaligngroup.prototype.kind] = maligngroup_js_1.MmlMaligngroup,\n    _a[malignmark_js_1.MmlMalignmark.prototype.kind] = malignmark_js_1.MmlMalignmark,\n    _a[mglyph_js_1.MmlMglyph.prototype.kind] = mglyph_js_1.MmlMglyph,\n    _a[semantics_js_1.MmlSemantics.prototype.kind] = semantics_js_1.MmlSemantics,\n    _a[semantics_js_1.MmlAnnotation.prototype.kind] = semantics_js_1.MmlAnnotation,\n    _a[semantics_js_1.MmlAnnotationXML.prototype.kind] = semantics_js_1.MmlAnnotationXML,\n    _a[TeXAtom_js_1.TeXAtom.prototype.kind] = TeXAtom_js_1.TeXAtom,\n    _a[mathchoice_js_1.MathChoice.prototype.kind] = mathchoice_js_1.MathChoice,\n    _a[MmlNode_js_1.TextNode.prototype.kind] = MmlNode_js_1.TextNode,\n    _a[MmlNode_js_1.XMLNode.prototype.kind] = MmlNode_js_1.XMLNode,\n    _a);\n//# sourceMappingURL=MML.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlFactory = void 0;\nvar NodeFactory_js_1 = require(\"../Tree/NodeFactory.js\");\nvar MML_js_1 = require(\"./MML.js\");\nvar MmlFactory = (function (_super) {\n    __extends(MmlFactory, _super);\n    function MmlFactory() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlFactory.prototype, \"MML\", {\n        get: function () {\n            return this.node;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlFactory.defaultNodes = MML_js_1.MML;\n    return MmlFactory;\n}(NodeFactory_js_1.AbstractNodeFactory));\nexports.MmlFactory = MmlFactory;\n//# sourceMappingURL=MmlFactory.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMaligngroup = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar Attributes_js_1 = require(\"../Attributes.js\");\nvar MmlMaligngroup = (function (_super) {\n    __extends(MmlMaligngroup, _super);\n    function MmlMaligngroup() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMaligngroup.prototype, \"kind\", {\n        get: function () {\n            return 'maligngroup';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMaligngroup.prototype, \"isSpacelike\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMaligngroup.prototype.setChildInheritedAttributes = function (attributes, display, level, prime) {\n        attributes = this.addInheritedAttributes(attributes, this.attributes.getAllAttributes());\n        _super.prototype.setChildInheritedAttributes.call(this, attributes, display, level, prime);\n    };\n    MmlMaligngroup.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlLayoutNode.defaults), { groupalign: Attributes_js_1.INHERIT });\n    return MmlMaligngroup;\n}(MmlNode_js_1.AbstractMmlLayoutNode));\nexports.MmlMaligngroup = MmlMaligngroup;\n//# sourceMappingURL=maligngroup.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMalignmark = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMalignmark = (function (_super) {\n    __extends(MmlMalignmark, _super);\n    function MmlMalignmark() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMalignmark.prototype, \"kind\", {\n        get: function () {\n            return 'malignmark';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMalignmark.prototype, \"arity\", {\n        get: function () {\n            return 0;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMalignmark.prototype, \"isSpacelike\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMalignmark.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlNode.defaults), { edge: 'left' });\n    return MmlMalignmark;\n}(MmlNode_js_1.AbstractMmlNode));\nexports.MmlMalignmark = MmlMalignmark;\n//# sourceMappingURL=malignmark.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MathChoice = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MathChoice = (function (_super) {\n    __extends(MathChoice, _super);\n    function MathChoice() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MathChoice.prototype, \"kind\", {\n        get: function () {\n            return 'MathChoice';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MathChoice.prototype, \"arity\", {\n        get: function () {\n            return 4;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MathChoice.prototype, \"notParent\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MathChoice.prototype.setInheritedAttributes = function (attributes, display, level, prime) {\n        var selection = (display ? 0 : Math.max(0, Math.min(level, 2)) + 1);\n        var child = this.childNodes[selection] || this.factory.create('mrow');\n        this.parent.replaceChild(child, this);\n        child.setInheritedAttributes(attributes, display, level, prime);\n    };\n    MathChoice.defaults = __assign({}, MmlNode_js_1.AbstractMmlBaseNode.defaults);\n    return MathChoice;\n}(MmlNode_js_1.AbstractMmlBaseNode));\nexports.MathChoice = MathChoice;\n//# sourceMappingURL=mathchoice.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMerror = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMerror = (function (_super) {\n    __extends(MmlMerror, _super);\n    function MmlMerror() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.texclass = MmlNode_js_1.TEXCLASS.ORD;\n        return _this;\n    }\n    Object.defineProperty(MmlMerror.prototype, \"kind\", {\n        get: function () {\n            return 'merror';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMerror.prototype, \"arity\", {\n        get: function () {\n            return -1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMerror.prototype, \"linebreakContainer\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMerror.defaults = __assign({}, MmlNode_js_1.AbstractMmlNode.defaults);\n    return MmlMerror;\n}(MmlNode_js_1.AbstractMmlNode));\nexports.MmlMerror = MmlMerror;\n//# sourceMappingURL=merror.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMphantom = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMphantom = (function (_super) {\n    __extends(MmlMphantom, _super);\n    function MmlMphantom() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.texclass = MmlNode_js_1.TEXCLASS.ORD;\n        return _this;\n    }\n    Object.defineProperty(MmlMphantom.prototype, \"kind\", {\n        get: function () {\n            return 'mphantom';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMphantom.defaults = __assign({}, MmlNode_js_1.AbstractMmlLayoutNode.defaults);\n    return MmlMphantom;\n}(MmlNode_js_1.AbstractMmlLayoutNode));\nexports.MmlMphantom = MmlMphantom;\n//# sourceMappingURL=mphantom.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMstyle = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar Attributes_js_1 = require(\"../Attributes.js\");\nvar MmlMstyle = (function (_super) {\n    __extends(MmlMstyle, _super);\n    function MmlMstyle() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMstyle.prototype, \"kind\", {\n        get: function () {\n            return 'mstyle';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMstyle.prototype, \"notParent\", {\n        get: function () {\n            return this.childNodes[0] && this.childNodes[0].childNodes.length === 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMstyle.prototype.setChildInheritedAttributes = function (attributes, display, level, prime) {\n        var scriptlevel = this.attributes.getExplicit('scriptlevel');\n        if (scriptlevel != null) {\n            scriptlevel = scriptlevel.toString();\n            if (scriptlevel.match(/^\\s*[-+]/)) {\n                level += parseInt(scriptlevel);\n            }\n            else {\n                level = parseInt(scriptlevel);\n            }\n            prime = false;\n        }\n        var displaystyle = this.attributes.getExplicit('displaystyle');\n        if (displaystyle != null) {\n            display = (displaystyle === true);\n            prime = false;\n        }\n        var cramped = this.attributes.getExplicit('data-cramped');\n        if (cramped != null) {\n            prime = cramped;\n        }\n        attributes = this.addInheritedAttributes(attributes, this.attributes.getAllAttributes());\n        this.childNodes[0].setInheritedAttributes(attributes, display, level, prime);\n    };\n    MmlMstyle.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlLayoutNode.defaults), { scriptlevel: Attributes_js_1.INHERIT, displaystyle: Attributes_js_1.INHERIT, scriptsizemultiplier: 1 / Math.sqrt(2), scriptminsize: '8px', mathbackground: Attributes_js_1.INHERIT, mathcolor: Attributes_js_1.INHERIT, dir: Attributes_js_1.INHERIT, infixlinebreakstyle: 'before' });\n    return MmlMstyle;\n}(MmlNode_js_1.AbstractMmlLayoutNode));\nexports.MmlMstyle = MmlMstyle;\n//# sourceMappingURL=mstyle.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractNodeFactory = void 0;\nvar Factory_js_1 = require(\"./Factory.js\");\nvar AbstractNodeFactory = (function (_super) {\n    __extends(AbstractNodeFactory, _super);\n    function AbstractNodeFactory() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AbstractNodeFactory.prototype.create = function (kind, properties, children) {\n        if (properties === void 0) { properties = {}; }\n        if (children === void 0) { children = []; }\n        return this.node[kind](properties, children);\n    };\n    return AbstractNodeFactory;\n}(Factory_js_1.AbstractFactory));\nexports.AbstractNodeFactory = AbstractNodeFactory;\n//# sourceMappingURL=NodeFactory.js.map"], "names": [], "sourceRoot": ""}