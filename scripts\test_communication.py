#!/usr/bin/env python3
"""
Communication Protocol Testing Script
Tests UDP, CAN, and Serial communication protocols
"""

import sys
import os
import time
import numpy as np
import threading
from typing import Dict, Any

# Add the communication module to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'communication'))

from communication.protocols import (
    UDPCommunication, 
    CANCommunication, 
    SerialCommunication,
    CommunicationManager,
    Message,
    MessageType
)


class CommunicationTester:
    """
    Comprehensive communication protocol tester
    Demonstrates all communication features
    """
    
    def __init__(self):
        self.test_results = {}
        self.test_data = self.generate_test_data()
    
    def generate_test_data(self) -> Dict[str, Any]:
        """Generate test data for communication protocols"""
        return {
            'sensor_data': np.random.randn(1000, 4),  # Simulated point cloud
            'small_message': b'Hello, World!',
            'large_message': b'X' * 10000,  # 10KB message
            'structured_data': {
                'timestamp': time.time(),
                'sensor_id': 123,
                'values': [1.0, 2.0, 3.0, 4.0]
            }
        }
    
    def test_udp_communication(self) -> bool:
        """Test UDP communication protocol"""
        print("\n=== Testing UDP Communication ===")
        
        try:
            # Configuration for UDP test
            config = {
                'local_ip': '127.0.0.1',
                'local_port': 8888,
                'remote_ip': '127.0.0.1',
                'remote_port': 8889,
                'buffer_size': 65536
            }
            
            # Create UDP communication instances
            sender = UDPCommunication(config)
            
            receiver_config = config.copy()
            receiver_config['local_port'] = 8889
            receiver_config['remote_port'] = 8888
            receiver = UDPCommunication(receiver_config)
            
            # Message reception tracking
            received_messages = []
            
            def message_handler(message: Message):
                received_messages.append(message)
                print(f"Received UDP message: Type={message.message_type}, "
                      f"Source={message.source_id}, Size={len(message.data)} bytes")
            
            receiver.message_callback = message_handler
            
            # Start communication
            sender.start()
            receiver.start()
            
            print("UDP communication started")
            time.sleep(0.5)  # Allow initialization
            
            # Test 1: Send sensor data
            print("Test 1: Sending sensor data...")
            success1 = sender.send_sensor_data(1, self.test_data['sensor_data'])
            
            # Test 2: Send custom message
            print("Test 2: Sending custom message...")
            message = Message(
                message_type=MessageType.COMMAND,
                timestamp=time.time(),
                source_id=100,
                data=self.test_data['small_message']
            )
            success2 = sender.send_message(message)
            
            # Test 3: Send large message
            print("Test 3: Sending large message...")
            large_message = Message(
                message_type=MessageType.SENSOR_DATA,
                timestamp=time.time(),
                source_id=200,
                data=self.test_data['large_message']
            )
            success3 = sender.send_message(large_message)
            
            # Wait for messages to be received
            time.sleep(1.0)
            
            # Check results
            print(f"Messages sent: 3, Messages received: {len(received_messages)}")
            
            # Get statistics
            sender_stats = sender.get_statistics()
            receiver_stats = receiver.get_statistics()
            
            print(f"Sender stats: {sender_stats}")
            print(f"Receiver stats: {receiver_stats}")
            
            # Stop communication
            sender.stop()
            receiver.stop()
            
            # Evaluate test
            test_passed = (
                success1 and success2 and success3 and
                len(received_messages) >= 1 and  # At least some messages received
                sender_stats['messages_sent'] >= 3
            )
            
            print(f"UDP Test Result: {'PASSED' if test_passed else 'FAILED'}")
            self.test_results['udp'] = test_passed
            return test_passed
            
        except Exception as e:
            print(f"UDP test failed with error: {e}")
            self.test_results['udp'] = False
            return False
    
    def test_can_communication(self) -> bool:
        """Test CAN communication protocol"""
        print("\n=== Testing CAN Communication ===")
        
        try:
            # Configuration for CAN test
            config = {
                'interface': 'vcan0',
                'bitrate': 500000,
                'node_id': 0x123
            }
            
            # Create CAN communication instance
            can_comm = CANCommunication(config)
            
            # Message reception tracking
            received_frames = []
            
            def can_message_handler(can_id: int, data: bytes):
                received_frames.append((can_id, data))
                print(f"Received CAN frame: ID=0x{can_id:03X}, Data={data.hex()}")
            
            can_comm.register_callback(0x100, can_message_handler)
            can_comm.register_callback(0x200, can_message_handler)
            
            # Start communication
            can_comm.start()
            print("CAN communication started")
            time.sleep(0.5)
            
            # Test 1: Send sensor data
            print("Test 1: Sending CAN sensor data...")
            success1 = can_comm.send_sensor_data(1, 25.5)  # Temperature sensor
            
            # Test 2: Send raw CAN frame
            print("Test 2: Sending raw CAN frame...")
            success2 = can_comm.send_frame(0x200, b'\x01\x02\x03\x04')
            
            # Test 3: Send multiple frames
            print("Test 3: Sending multiple frames...")
            success3 = True
            for i in range(5):
                success3 &= can_comm.send_frame(0x300 + i, f"Data{i}".encode())
            
            # Wait for processing
            time.sleep(2.0)
            
            # Get statistics
            stats = can_comm.get_statistics()
            print(f"CAN stats: {stats}")
            
            # Stop communication
            can_comm.stop()
            
            # Evaluate test
            test_passed = (
                success1 and success2 and success3 and
                stats['frames_sent'] >= 7  # At least 7 frames sent
            )
            
            print(f"CAN Test Result: {'PASSED' if test_passed else 'FAILED'}")
            self.test_results['can'] = test_passed
            return test_passed
            
        except Exception as e:
            print(f"CAN test failed with error: {e}")
            self.test_results['can'] = False
            return False
    
    def test_serial_communication(self) -> bool:
        """Test Serial communication protocol"""
        print("\n=== Testing Serial Communication ===")
        
        try:
            # Configuration for Serial test
            config = {
                'port': '/dev/ttyUSB0',  # This would be a real serial port
                'baudrate': 115200,
                'timeout': 1.0
            }
            
            # Create Serial communication instance
            serial_comm = SerialCommunication(config)
            
            # Message reception tracking
            received_data = []
            
            def serial_message_handler(data: bytes):
                received_data.append(data)
                print(f"Received serial data: {data.decode('ascii', errors='ignore').strip()}")
            
            serial_comm.message_callback = serial_message_handler
            
            # Start communication
            serial_comm.start()
            print("Serial communication started")
            time.sleep(0.5)
            
            # Test 1: Send command
            print("Test 1: Sending serial command...")
            success1 = serial_comm.send_command("GET_STATUS")
            
            # Test 2: Send raw data
            print("Test 2: Sending raw serial data...")
            success2 = serial_comm.send_data(b'\x01\x02\x03\x04\x05')
            
            # Test 3: Send text data
            print("Test 3: Sending text data...")
            success3 = serial_comm.send_command("SET_PARAM:123.45")
            
            # Wait for processing
            time.sleep(2.0)
            
            # Get statistics
            stats = serial_comm.get_statistics()
            print(f"Serial stats: {stats}")
            
            # Stop communication
            serial_comm.stop()
            
            # Evaluate test (more lenient since we don't have real hardware)
            test_passed = (
                success1 and success2 and success3 and
                stats['bytes_sent'] > 0
            )
            
            print(f"Serial Test Result: {'PASSED' if test_passed else 'FAILED'}")
            self.test_results['serial'] = test_passed
            return test_passed
            
        except Exception as e:
            print(f"Serial test failed with error: {e}")
            self.test_results['serial'] = False
            return False
    
    def test_communication_manager(self) -> bool:
        """Test unified communication manager"""
        print("\n=== Testing Communication Manager ===")
        
        try:
            # Configuration for communication manager
            config = {
                'enable_udp': True,
                'enable_can': True,
                'enable_serial': True,
                'udp': {
                    'local_ip': '127.0.0.1',
                    'local_port': 9000,
                    'remote_ip': '127.0.0.1',
                    'remote_port': 9001
                },
                'can': {
                    'interface': 'vcan0',
                    'bitrate': 500000,
                    'node_id': 0x456
                },
                'serial': {
                    'port': '/dev/ttyUSB1',
                    'baudrate': 115200
                }
            }
            
            # Create communication manager
            comm_manager = CommunicationManager(config)
            
            # Message handling
            received_messages = {'udp': [], 'serial': []}
            
            def udp_handler(message):
                received_messages['udp'].append(message)
                print(f"Manager received UDP message: {message.message_type}")
            
            def serial_handler(data):
                received_messages['serial'].append(data)
                print(f"Manager received serial data: {len(data)} bytes")
            
            comm_manager.register_handler('udp', udp_handler)
            comm_manager.register_handler('serial', serial_handler)
            
            # Start all protocols
            comm_manager.start_all()
            print("Communication manager started")
            time.sleep(1.0)
            
            # Test broadcasting sensor data
            print("Test: Broadcasting sensor data...")
            test_data = np.array([1.0, 2.0, 3.0, 4.0])
            comm_manager.broadcast_sensor_data(42, test_data)
            
            # Wait for processing
            time.sleep(2.0)
            
            # Get statistics from all protocols
            all_stats = comm_manager.get_all_statistics()
            print(f"All protocol stats: {all_stats}")
            
            # Stop all protocols
            comm_manager.stop_all()
            
            # Evaluate test
            test_passed = len(all_stats) >= 2  # At least UDP and CAN should be present
            
            print(f"Communication Manager Test Result: {'PASSED' if test_passed else 'FAILED'}")
            self.test_results['manager'] = test_passed
            return test_passed
            
        except Exception as e:
            print(f"Communication manager test failed with error: {e}")
            self.test_results['manager'] = False
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all communication tests"""
        print("Starting Communication Protocol Tests")
        print("=" * 50)
        
        # Run individual protocol tests
        self.test_udp_communication()
        self.test_can_communication()
        self.test_serial_communication()
        self.test_communication_manager()
        
        # Print summary
        print("\n" + "=" * 50)
        print("TEST SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        
        for test_name, result in self.test_results.items():
            status = "PASSED" if result else "FAILED"
            print(f"{test_name.upper():20} : {status}")
        
        print("-" * 50)
        print(f"Total: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("🎉 All communication tests PASSED!")
        else:
            print("⚠️  Some communication tests FAILED!")
        
        return self.test_results


def main():
    """Main function for communication testing"""
    print("Perception Engineering - Communication Protocol Testing")
    print("=" * 60)
    print()
    print("This script tests all communication protocols:")
    print("- UDP: High-speed sensor data transmission")
    print("- CAN: Automotive communication protocol")
    print("- Serial: Low-level device communication")
    print("- Manager: Unified multi-protocol communication")
    print()
    
    # Create and run tester
    tester = CommunicationTester()
    results = tester.run_all_tests()
    
    # Exit with appropriate code
    if all(results.values()):
        print("\n✅ All tests completed successfully!")
        return 0
    else:
        print("\n❌ Some tests failed!")
        return 1


if __name__ == '__main__':
    exit_code = main()
    exit(exit_code)
