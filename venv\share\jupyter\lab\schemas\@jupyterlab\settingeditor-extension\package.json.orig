{"name": "@jupyterlab/settingeditor-extension", "version": "4.4.3", "description": "JupyterLab - Setting Editor Extension", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/*.css", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/*.d.ts", "lib/*.js.map", "lib/*.js", "schema/*.json", "style/**/*.css", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "watch": "tsc -b --watch"}, "dependencies": {"@jupyterlab/application": "^4.4.3", "@jupyterlab/apputils": "^4.5.3", "@jupyterlab/codeeditor": "^4.4.3", "@jupyterlab/pluginmanager": "^4.4.3", "@jupyterlab/rendermime": "^4.4.3", "@jupyterlab/settingeditor": "^4.4.3", "@jupyterlab/settingregistry": "^4.4.3", "@jupyterlab/statedb": "^4.4.3", "@jupyterlab/translation": "^4.4.3", "@jupyterlab/ui-components": "^4.4.3", "@lumino/disposable": "^2.1.4", "react": "^18.2.0"}, "devDependencies": {"rimraf": "~5.0.5", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}