"""
Mathematical Utilities for Sensor Processing
Demonstrates linear algebra, least squares, and optimization algorithms
"""

import numpy as np
from typing import <PERSON>ple, List, Optional, Union
from scipy.optimize import least_squares
from scipy.spatial.transform import Rotation
import math


class MathUtils:
    """
    Collection of mathematical utilities for perception engineering
    Includes linear algebra, coordinate transformations, and optimization
    """
    
    @staticmethod
    def homogeneous_transform(translation: np.ndarray, rotation: np.ndarray) -> np.ndarray:
        """
        Create 4x4 homogeneous transformation matrix
        
        Args:
            translation: 3D translation vector [x, y, z]
            rotation: 3x3 rotation matrix or quaternion [w, x, y, z]
        
        Returns:
            4x4 transformation matrix
        """
        T = np.eye(4)
        T[:3, 3] = translation
        
        if rotation.shape == (3, 3):
            T[:3, :3] = rotation
        elif rotation.shape == (4,):
            # Convert quaternion to rotation matrix
            r = Rotation.from_quat([rotation[1], rotation[2], rotation[3], rotation[0]])
            T[:3, :3] = r.as_matrix()
        else:
            raise ValueError("Rotation must be 3x3 matrix or quaternion [w,x,y,z]")
        
        return T
    
    @staticmethod
    def transform_points(points: np.ndarray, transform: np.ndarray) -> np.ndarray:
        """
        Transform 3D points using homogeneous transformation matrix
        
        Args:
            points: Nx3 array of 3D points
            transform: 4x4 transformation matrix
        
        Returns:
            Nx3 array of transformed points
        """
        # Convert to homogeneous coordinates
        points_homo = np.hstack([points, np.ones((points.shape[0], 1))])
        
        # Apply transformation
        transformed_homo = (transform @ points_homo.T).T
        
        # Convert back to 3D coordinates
        return transformed_homo[:, :3]
    
    @staticmethod
    def least_squares_plane_fit(points: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        Fit plane to 3D points using least squares
        
        Args:
            points: Nx3 array of 3D points
        
        Returns:
            Tuple of (normal_vector, d_coefficient) where plane equation is ax + by + cz + d = 0
        """
        if points.shape[0] < 3:
            raise ValueError("Need at least 3 points to fit a plane")
        
        # Center the points
        centroid = np.mean(points, axis=0)
        centered_points = points - centroid
        
        # SVD to find normal vector
        _, _, V = np.linalg.svd(centered_points)
        normal = V[-1, :]  # Last row of V is the normal vector
        
        # Calculate d coefficient
        d = -np.dot(normal, centroid)
        
        return normal, d
    
    @staticmethod
    def least_squares_line_fit(points: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Fit line to 2D or 3D points using least squares
        
        Args:
            points: Nx2 or Nx3 array of points
        
        Returns:
            Tuple of (point_on_line, direction_vector)
        """
        if points.shape[0] < 2:
            raise ValueError("Need at least 2 points to fit a line")
        
        # Center the points
        centroid = np.mean(points, axis=0)
        centered_points = points - centroid
        
        # SVD to find direction vector
        _, _, V = np.linalg.svd(centered_points)
        direction = V[0, :]  # First row of V is the direction vector
        
        return centroid, direction
    
    @staticmethod
    def kalman_filter_predict(state: np.ndarray, P: np.ndarray, F: np.ndarray, 
                            Q: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Kalman filter prediction step
        
        Args:
            state: Current state vector
            P: Current covariance matrix
            F: State transition matrix
            Q: Process noise covariance
        
        Returns:
            Tuple of (predicted_state, predicted_covariance)
        """
        # Predict state
        predicted_state = F @ state
        
        # Predict covariance
        predicted_P = F @ P @ F.T + Q
        
        return predicted_state, predicted_P
    
    @staticmethod
    def kalman_filter_update(predicted_state: np.ndarray, predicted_P: np.ndarray,
                           measurement: np.ndarray, H: np.ndarray, 
                           R: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Kalman filter update step
        
        Args:
            predicted_state: Predicted state from prediction step
            predicted_P: Predicted covariance from prediction step
            measurement: Sensor measurement
            H: Measurement matrix
            R: Measurement noise covariance
        
        Returns:
            Tuple of (updated_state, updated_covariance)
        """
        # Innovation
        y = measurement - H @ predicted_state
        
        # Innovation covariance
        S = H @ predicted_P @ H.T + R
        
        # Kalman gain
        K = predicted_P @ H.T @ np.linalg.inv(S)
        
        # Update state
        updated_state = predicted_state + K @ y
        
        # Update covariance
        I = np.eye(len(predicted_state))
        updated_P = (I - K @ H) @ predicted_P
        
        return updated_state, updated_P
    
    @staticmethod
    def rotation_matrix_from_euler(roll: float, pitch: float, yaw: float) -> np.ndarray:
        """
        Create rotation matrix from Euler angles (ZYX convention)
        
        Args:
            roll: Rotation around X-axis (radians)
            pitch: Rotation around Y-axis (radians)
            yaw: Rotation around Z-axis (radians)
        
        Returns:
            3x3 rotation matrix
        """
        r = Rotation.from_euler('xyz', [roll, pitch, yaw])
        return r.as_matrix()
    
    @staticmethod
    def euler_from_rotation_matrix(R: np.ndarray) -> Tuple[float, float, float]:
        """
        Extract Euler angles from rotation matrix
        
        Args:
            R: 3x3 rotation matrix
        
        Returns:
            Tuple of (roll, pitch, yaw) in radians
        """
        r = Rotation.from_matrix(R)
        return r.as_euler('xyz')
    
    @staticmethod
    def quaternion_from_rotation_matrix(R: np.ndarray) -> np.ndarray:
        """
        Convert rotation matrix to quaternion [w, x, y, z]
        
        Args:
            R: 3x3 rotation matrix
        
        Returns:
            Quaternion [w, x, y, z]
        """
        r = Rotation.from_matrix(R)
        quat_xyzw = r.as_quat()  # Returns [x, y, z, w]
        return np.array([quat_xyzw[3], quat_xyzw[0], quat_xyzw[1], quat_xyzw[2]])  # [w, x, y, z]
    
    @staticmethod
    def distance_point_to_line(point: np.ndarray, line_point: np.ndarray, 
                              line_direction: np.ndarray) -> float:
        """
        Calculate distance from point to line
        
        Args:
            point: 3D point
            line_point: Point on the line
            line_direction: Direction vector of the line
        
        Returns:
            Distance from point to line
        """
        # Vector from line point to the point
        v = point - line_point
        
        # Normalize direction vector
        direction_normalized = line_direction / np.linalg.norm(line_direction)
        
        # Project v onto the line direction
        projection_length = np.dot(v, direction_normalized)
        projection = projection_length * direction_normalized
        
        # Distance is the magnitude of the perpendicular component
        perpendicular = v - projection
        return np.linalg.norm(perpendicular)
    
    @staticmethod
    def fit_circle_2d(points: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        Fit circle to 2D points using least squares
        
        Args:
            points: Nx2 array of 2D points
        
        Returns:
            Tuple of (center, radius)
        """
        def residuals(params, points):
            center_x, center_y, radius = params
            distances = np.sqrt((points[:, 0] - center_x)**2 + (points[:, 1] - center_y)**2)
            return distances - radius
        
        # Initial guess
        center_guess = np.mean(points, axis=0)
        radius_guess = np.mean(np.linalg.norm(points - center_guess, axis=1))
        initial_params = [center_guess[0], center_guess[1], radius_guess]
        
        # Least squares optimization
        result = least_squares(residuals, initial_params, args=(points,))
        
        center = np.array([result.x[0], result.x[1]])
        radius = result.x[2]
        
        return center, radius
    
    @staticmethod
    def weighted_least_squares(A: np.ndarray, b: np.ndarray, 
                              weights: np.ndarray) -> np.ndarray:
        """
        Solve weighted least squares problem: min ||W(Ax - b)||^2
        
        Args:
            A: Coefficient matrix (m x n)
            b: Right-hand side vector (m,)
            weights: Weight vector (m,)
        
        Returns:
            Solution vector x (n,)
        """
        # Create weight matrix
        W = np.diag(np.sqrt(weights))
        
        # Weighted matrices
        WA = W @ A
        Wb = W @ b
        
        # Solve normal equations
        x = np.linalg.lstsq(WA, Wb, rcond=None)[0]
        
        return x
    
    @staticmethod
    def robust_mean(data: np.ndarray, threshold: float = 2.0) -> np.ndarray:
        """
        Calculate robust mean by removing outliers
        
        Args:
            data: Input data array
            threshold: Number of standard deviations for outlier detection
        
        Returns:
            Robust mean
        """
        mean = np.mean(data, axis=0)
        std = np.std(data, axis=0)
        
        # Find inliers
        distances = np.linalg.norm(data - mean, axis=1) if data.ndim > 1 else np.abs(data - mean)
        inliers = distances < threshold * np.mean(std)
        
        if np.sum(inliers) > 0:
            return np.mean(data[inliers], axis=0)
        else:
            return mean
    
    @staticmethod
    def interpolate_trajectory(waypoints: np.ndarray, timestamps: np.ndarray, 
                             query_time: float) -> np.ndarray:
        """
        Interpolate position along trajectory at given time
        
        Args:
            waypoints: Nx3 array of waypoints
            timestamps: N array of timestamps
            query_time: Time to interpolate at
        
        Returns:
            Interpolated 3D position
        """
        if query_time <= timestamps[0]:
            return waypoints[0]
        elif query_time >= timestamps[-1]:
            return waypoints[-1]
        
        # Find surrounding timestamps
        idx = np.searchsorted(timestamps, query_time)
        
        # Linear interpolation
        t0, t1 = timestamps[idx-1], timestamps[idx]
        p0, p1 = waypoints[idx-1], waypoints[idx]
        
        alpha = (query_time - t0) / (t1 - t0)
        return p0 + alpha * (p1 - p0)


class CoordinateTransforms:
    """
    Coordinate transformation utilities for sensor fusion
    """
    
    @staticmethod
    def cartesian_to_spherical(x: float, y: float, z: float) -> Tuple[float, float, float]:
        """
        Convert Cartesian coordinates to spherical
        
        Returns:
            Tuple of (range, azimuth, elevation) in meters and radians
        """
        range_val = math.sqrt(x**2 + y**2 + z**2)
        azimuth = math.atan2(y, x)
        elevation = math.asin(z / range_val) if range_val > 0 else 0
        
        return range_val, azimuth, elevation
    
    @staticmethod
    def spherical_to_cartesian(range_val: float, azimuth: float, 
                             elevation: float) -> Tuple[float, float, float]:
        """
        Convert spherical coordinates to Cartesian
        
        Args:
            range_val: Range in meters
            azimuth: Azimuth angle in radians
            elevation: Elevation angle in radians
        
        Returns:
            Tuple of (x, y, z) in meters
        """
        x = range_val * math.cos(elevation) * math.cos(azimuth)
        y = range_val * math.cos(elevation) * math.sin(azimuth)
        z = range_val * math.sin(elevation)
        
        return x, y, z
    
    @staticmethod
    def utm_to_local(utm_x: float, utm_y: float, origin_utm_x: float, 
                    origin_utm_y: float, origin_heading: float) -> Tuple[float, float]:
        """
        Convert UTM coordinates to local coordinate system
        
        Args:
            utm_x, utm_y: UTM coordinates
            origin_utm_x, origin_utm_y: Local origin in UTM
            origin_heading: Heading of local coordinate system (radians)
        
        Returns:
            Tuple of (local_x, local_y)
        """
        # Translate to origin
        dx = utm_x - origin_utm_x
        dy = utm_y - origin_utm_y
        
        # Rotate to local coordinate system
        cos_h = math.cos(-origin_heading)
        sin_h = math.sin(-origin_heading)
        
        local_x = dx * cos_h - dy * sin_h
        local_y = dx * sin_h + dy * cos_h
        
        return local_x, local_y
