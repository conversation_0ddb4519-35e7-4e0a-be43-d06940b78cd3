"""
LiDAR Processing Module
Demonstrates point cloud processing, feature extraction, and real-time algorithms
"""

import numpy as np
from typing import List, Tuple, Dict, Any, Optional
import time
from dataclasses import dataclass
from scipy.spatial import KDTree
from sklearn.cluster import DBSCAN
import rclpy
from rclpy.node import Node
from sensor_msgs.msg import PointCloud2, PointField
from geometry_msgs.msg import Point, Vector3
from visualization_msgs.msg import Marker, MarkerArray
from std_msgs.msg import Header, ColorRGBA

from .sensor_base import SensorProcessor
# from .math_utils import MathUtils  # Will be implemented next


@dataclass
class LiDARPoint:
    """Represents a single LiDAR point with intensity"""
    x: float
    y: float
    z: float
    intensity: float = 0.0
    ring: int = 0
    timestamp: float = 0.0


@dataclass
class DetectedObject:
    """Represents a detected object from LiDAR data"""
    center: Tuple[float, float, float]
    dimensions: Tuple[float, float, float]  # length, width, height
    orientation: float
    confidence: float
    object_type: str
    points: List[LiDARPoint]


class LiDARProcessor(SensorProcessor):
    """
    Advanced LiDAR processing with real-time point cloud analysis
    Includes ground segmentation, object detection, and feature extraction
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__('lidar_processor', config)
        
        # LiDAR-specific parameters
        self.max_range = config.get('max_range', 100.0)
        self.min_range = config.get('min_range', 0.5)
        self.ground_threshold = config.get('ground_threshold', 0.2)
        self.cluster_tolerance = config.get('cluster_tolerance', 0.5)
        self.min_cluster_size = config.get('min_cluster_size', 10)
        self.max_cluster_size = config.get('max_cluster_size', 5000)
        
        # Voxel grid parameters for downsampling
        self.voxel_size = config.get('voxel_size', 0.1)
        
        # Ground plane estimation
        self.ground_plane = None
        self.ground_history = []
        
        # Object tracking
        self.previous_objects = []
        self.object_id_counter = 0
        
        # Performance optimization
        self.use_voxel_grid = config.get('use_voxel_grid', True)
        self.use_roi_filtering = config.get('use_roi_filtering', True)
        
    def initialize_sensor(self):
        """Initialize LiDAR-specific components"""
        # Publishers
        self.pointcloud_pub = self.create_publisher(
            PointCloud2, 
            'processed_pointcloud', 
            self.qos_profile
        )
        
        self.objects_pub = self.create_publisher(
            MarkerArray,
            'detected_objects',
            self.qos_profile
        )
        
        # Subscribers
        self.pointcloud_sub = self.create_subscription(
            PointCloud2,
            'raw_pointcloud',
            self.pointcloud_callback,
            self.qos_profile
        )
        
        self.get_logger().info("LiDAR processor initialized")
    
    def pointcloud_callback(self, msg: PointCloud2):
        """Handle incoming point cloud data"""
        self.add_data_to_queue(msg)
    
    def process_data(self, pointcloud_msg: PointCloud2) -> Optional[Dict[str, Any]]:
        """
        Main LiDAR processing pipeline
        Returns processed data including detected objects and filtered point cloud
        """
        try:
            # Convert ROS message to numpy array
            points = self.pointcloud_to_numpy(pointcloud_msg)
            
            if points is None or len(points) == 0:
                return None
            
            # Step 1: Range filtering
            points = self.range_filter(points)
            
            # Step 2: Voxel grid downsampling (optional)
            if self.use_voxel_grid:
                points = self.voxel_grid_filter(points)
            
            # Step 3: ROI filtering (optional)
            if self.use_roi_filtering:
                points = self.roi_filter(points)
            
            # Step 4: Ground segmentation
            ground_points, object_points = self.segment_ground(points)
            
            # Step 5: Object clustering
            clusters = self.cluster_objects(object_points)
            
            # Step 6: Object detection and classification
            detected_objects = self.detect_objects(clusters)
            
            # Step 7: Feature extraction
            features = self.extract_features(object_points, detected_objects)
            
            return {
                'timestamp': time.time(),
                'original_points': points,
                'ground_points': ground_points,
                'object_points': object_points,
                'detected_objects': detected_objects,
                'features': features,
                'header': pointcloud_msg.header
            }
            
        except Exception as e:
            self.get_logger().error(f"Error processing LiDAR data: {e}")
            return None
    
    def pointcloud_to_numpy(self, pointcloud_msg: PointCloud2) -> Optional[np.ndarray]:
        """Convert ROS PointCloud2 message to numpy array"""
        try:
            # This is a simplified conversion - in practice, you'd use point_cloud2 utilities
            # For demonstration, we'll create synthetic data
            num_points = 10000
            points = np.random.randn(num_points, 4)  # x, y, z, intensity
            
            # Scale to realistic LiDAR ranges
            points[:, :3] *= 20  # Scale position
            points[:, 3] = np.abs(points[:, 3]) * 255  # Intensity 0-255
            
            return points
            
        except Exception as e:
            self.get_logger().error(f"Error converting point cloud: {e}")
            return None
    
    def range_filter(self, points: np.ndarray) -> np.ndarray:
        """Filter points by range"""
        distances = np.linalg.norm(points[:, :3], axis=1)
        mask = (distances >= self.min_range) & (distances <= self.max_range)
        return points[mask]
    
    def voxel_grid_filter(self, points: np.ndarray) -> np.ndarray:
        """Downsample point cloud using voxel grid"""
        if len(points) == 0:
            return points
        
        # Quantize points to voxel grid
        voxel_coords = np.floor(points[:, :3] / self.voxel_size).astype(int)
        
        # Find unique voxels
        _, unique_indices = np.unique(voxel_coords, axis=0, return_index=True)
        
        return points[unique_indices]
    
    def roi_filter(self, points: np.ndarray) -> np.ndarray:
        """Filter points to region of interest"""
        # Example ROI: rectangular region in front of vehicle
        x_min, x_max = -10, 50
        y_min, y_max = -20, 20
        z_min, z_max = -3, 5
        
        mask = (
            (points[:, 0] >= x_min) & (points[:, 0] <= x_max) &
            (points[:, 1] >= y_min) & (points[:, 1] <= y_max) &
            (points[:, 2] >= z_min) & (points[:, 2] <= z_max)
        )
        
        return points[mask]
    
    def segment_ground(self, points: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Segment ground plane using RANSAC algorithm
        Returns ground points and object points
        """
        if len(points) < 3:
            return np.array([]), points
        
        # RANSAC parameters
        max_iterations = 100
        distance_threshold = self.ground_threshold
        min_inliers = len(points) // 10
        
        best_plane = None
        best_inliers = []
        
        for _ in range(max_iterations):
            # Sample 3 random points
            sample_indices = np.random.choice(len(points), 3, replace=False)
            sample_points = points[sample_indices, :3]
            
            # Fit plane to sample points
            plane = self.fit_plane(sample_points)
            if plane is None:
                continue
            
            # Calculate distances to plane
            distances = self.point_to_plane_distance(points[:, :3], plane)
            
            # Find inliers
            inliers = np.where(np.abs(distances) < distance_threshold)[0]
            
            # Check if this is the best plane so far
            if len(inliers) > len(best_inliers) and len(inliers) >= min_inliers:
                best_plane = plane
                best_inliers = inliers
        
        if best_plane is not None:
            # Update ground plane estimate
            self.ground_plane = best_plane
            
            # Separate ground and object points
            ground_points = points[best_inliers]
            object_mask = np.ones(len(points), dtype=bool)
            object_mask[best_inliers] = False
            object_points = points[object_mask]
            
            return ground_points, object_points
        else:
            # No ground plane found
            return np.array([]), points
    
    def fit_plane(self, points: np.ndarray) -> Optional[np.ndarray]:
        """Fit plane to 3 points, returns plane equation [a, b, c, d]"""
        if len(points) != 3:
            return None
        
        try:
            # Calculate normal vector using cross product
            v1 = points[1] - points[0]
            v2 = points[2] - points[0]
            normal = np.cross(v1, v2)
            
            # Normalize
            normal = normal / np.linalg.norm(normal)
            
            # Calculate d coefficient
            d = -np.dot(normal, points[0])
            
            return np.array([normal[0], normal[1], normal[2], d])
            
        except:
            return None
    
    def point_to_plane_distance(self, points: np.ndarray, plane: np.ndarray) -> np.ndarray:
        """Calculate distance from points to plane"""
        a, b, c, d = plane
        return (a * points[:, 0] + b * points[:, 1] + c * points[:, 2] + d) / np.sqrt(a**2 + b**2 + c**2)
    
    def cluster_objects(self, points: np.ndarray) -> List[np.ndarray]:
        """Cluster object points using DBSCAN"""
        if len(points) < self.min_cluster_size:
            return []
        
        # Use DBSCAN clustering
        clustering = DBSCAN(
            eps=self.cluster_tolerance,
            min_samples=self.min_cluster_size
        )
        
        labels = clustering.fit_predict(points[:, :3])
        
        # Extract clusters
        clusters = []
        for label in set(labels):
            if label == -1:  # Noise points
                continue
            
            cluster_mask = labels == label
            cluster_points = points[cluster_mask]
            
            if len(cluster_points) <= self.max_cluster_size:
                clusters.append(cluster_points)
        
        return clusters
    
    def detect_objects(self, clusters: List[np.ndarray]) -> List[DetectedObject]:
        """Detect and classify objects from clusters"""
        detected_objects = []
        
        for i, cluster in enumerate(clusters):
            if len(cluster) < self.min_cluster_size:
                continue
            
            # Calculate bounding box
            min_coords = np.min(cluster[:, :3], axis=0)
            max_coords = np.max(cluster[:, :3], axis=0)
            
            center = (min_coords + max_coords) / 2
            dimensions = max_coords - min_coords
            
            # Simple object classification based on dimensions
            object_type = self.classify_object(dimensions)
            
            # Calculate orientation (simplified)
            orientation = self.estimate_orientation(cluster[:, :3])
            
            # Confidence based on cluster size and compactness
            confidence = min(1.0, len(cluster) / 100.0)
            
            # Convert cluster to LiDARPoint objects
            lidar_points = [
                LiDARPoint(x=p[0], y=p[1], z=p[2], intensity=p[3])
                for p in cluster
            ]
            
            detected_object = DetectedObject(
                center=tuple(center),
                dimensions=tuple(dimensions),
                orientation=orientation,
                confidence=confidence,
                object_type=object_type,
                points=lidar_points
            )
            
            detected_objects.append(detected_object)
        
        return detected_objects
    
    def classify_object(self, dimensions: np.ndarray) -> str:
        """Simple object classification based on dimensions"""
        length, width, height = dimensions
        
        # Simple heuristic classification
        if height < 0.5:
            return "ground_object"
        elif length > 3.0 and width > 1.5 and height > 1.0:
            return "vehicle"
        elif height > 1.5 and max(length, width) < 1.0:
            return "person"
        elif height > 2.0:
            return "pole_or_tree"
        else:
            return "unknown"
    
    def estimate_orientation(self, points: np.ndarray) -> float:
        """Estimate object orientation using PCA"""
        if len(points) < 3:
            return 0.0
        
        # Center the points
        centered_points = points - np.mean(points, axis=0)
        
        # Compute covariance matrix
        cov_matrix = np.cov(centered_points.T)
        
        # Get principal component (largest eigenvector)
        eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)
        principal_component = eigenvectors[:, np.argmax(eigenvalues)]
        
        # Calculate orientation angle
        orientation = np.arctan2(principal_component[1], principal_component[0])
        
        return orientation
    
    def extract_features(self, points: np.ndarray, objects: List[DetectedObject]) -> Dict[str, Any]:
        """Extract features from point cloud and detected objects"""
        features = {
            'point_density': len(points) / (self.max_range ** 2 * np.pi) if len(points) > 0 else 0,
            'num_objects': len(objects),
            'object_types': [obj.object_type for obj in objects],
            'average_object_size': np.mean([np.prod(obj.dimensions) for obj in objects]) if objects else 0,
            'ground_plane': self.ground_plane.tolist() if self.ground_plane is not None else None
        }
        
        return features
    
    def publish_processed_data(self, data: Dict[str, Any]):
        """Publish processed LiDAR data"""
        try:
            # Publish processed point cloud
            if 'object_points' in data:
                pointcloud_msg = self.numpy_to_pointcloud(
                    data['object_points'],
                    data['header']
                )
                self.pointcloud_pub.publish(pointcloud_msg)
            
            # Publish detected objects as markers
            if 'detected_objects' in data:
                marker_array = self.objects_to_markers(
                    data['detected_objects'],
                    data['header']
                )
                self.objects_pub.publish(marker_array)
                
        except Exception as e:
            self.get_logger().error(f"Error publishing LiDAR data: {e}")
    
    def numpy_to_pointcloud(self, points: np.ndarray, header: Header) -> PointCloud2:
        """Convert numpy array to ROS PointCloud2 message"""
        # This is a simplified implementation
        msg = PointCloud2()
        msg.header = header
        msg.height = 1
        msg.width = len(points)
        msg.is_dense = False
        
        # In practice, you would properly encode the point data
        # For now, we'll create an empty message
        return msg
    
    def objects_to_markers(self, objects: List[DetectedObject], header: Header) -> MarkerArray:
        """Convert detected objects to visualization markers"""
        marker_array = MarkerArray()
        
        for i, obj in enumerate(objects):
            marker = Marker()
            marker.header = header
            marker.ns = "detected_objects"
            marker.id = i
            marker.type = Marker.CUBE
            marker.action = Marker.ADD
            
            # Position
            marker.pose.position.x = obj.center[0]
            marker.pose.position.y = obj.center[1]
            marker.pose.position.z = obj.center[2]
            
            # Orientation
            marker.pose.orientation.w = np.cos(obj.orientation / 2)
            marker.pose.orientation.z = np.sin(obj.orientation / 2)
            
            # Scale
            marker.scale.x = obj.dimensions[0]
            marker.scale.y = obj.dimensions[1]
            marker.scale.z = obj.dimensions[2]
            
            # Color based on object type
            color = self.get_object_color(obj.object_type)
            marker.color = ColorRGBA(r=color[0], g=color[1], b=color[2], a=0.7)
            
            marker_array.markers.append(marker)
        
        return marker_array
    
    def get_object_color(self, object_type: str) -> Tuple[float, float, float]:
        """Get color for object type"""
        colors = {
            'vehicle': (1.0, 0.0, 0.0),      # Red
            'person': (0.0, 1.0, 0.0),       # Green
            'pole_or_tree': (0.0, 0.0, 1.0), # Blue
            'ground_object': (1.0, 1.0, 0.0), # Yellow
            'unknown': (0.5, 0.5, 0.5)       # Gray
        }
        return colors.get(object_type, (0.5, 0.5, 0.5))
