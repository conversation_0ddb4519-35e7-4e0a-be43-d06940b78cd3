#!/usr/bin/env python3
"""
Sensor Data Generator
Generates synthetic sensor data for testing and demonstration
"""

import rclpy
from rclpy.node import Node
import numpy as np
import time
import threading
from sensor_msgs.msg import PointCloud2, Image
from std_msgs.msg import Header
import cv2
from cv_bridge import CvBridge


class SensorDataGenerator(Node):
    """
    Generates synthetic sensor data for testing perception algorithms
    """
    
    def __init__(self):
        super().__init__('sensor_data_generator')
        
        # Publishers
        self.lidar_pub = self.create_publisher(PointCloud2, 'raw_pointcloud', 10)
        self.radar_pub = self.create_publisher(PointCloud2, 'raw_radar_data', 10)
        self.camera_pub = self.create_publisher(Image, 'camera/image_raw', 10)
        
        # CV Bridge for image conversion
        self.bridge = CvBridge()
        
        # Generation parameters
        self.lidar_rate = 10.0  # Hz
        self.radar_rate = 20.0  # Hz
        self.camera_rate = 30.0  # Hz
        
        # Synthetic scene parameters
        self.scene_objects = [
            {'type': 'vehicle', 'position': [20, 5, 0], 'velocity': [-5, 0, 0]},
            {'type': 'person', 'position': [15, -3, 0], 'velocity': [1, 0, 0]},
            {'type': 'building', 'position': [50, 0, 0], 'velocity': [0, 0, 0]},
        ]
        
        # Start generation threads
        self.start_generation_threads()
        
        self.get_logger().info("Sensor data generator started")
    
    def start_generation_threads(self):
        """Start data generation threads"""
        # LiDAR thread
        lidar_thread = threading.Thread(target=self.generate_lidar_data, daemon=True)
        lidar_thread.start()
        
        # Radar thread
        radar_thread = threading.Thread(target=self.generate_radar_data, daemon=True)
        radar_thread.start()
        
        # Camera thread
        camera_thread = threading.Thread(target=self.generate_camera_data, daemon=True)
        camera_thread.start()
    
    def generate_lidar_data(self):
        """Generate synthetic LiDAR point clouds"""
        rate = 1.0 / self.lidar_rate
        
        while rclpy.ok():
            try:
                # Create synthetic point cloud
                points = self.create_synthetic_pointcloud()
                
                # Create ROS message
                msg = PointCloud2()
                msg.header = Header()
                msg.header.stamp = self.get_clock().now().to_msg()
                msg.header.frame_id = "lidar_frame"
                
                # Simplified point cloud message
                msg.height = 1
                msg.width = len(points)
                msg.is_dense = False
                
                # Publish
                self.lidar_pub.publish(msg)
                
                time.sleep(rate)
                
            except Exception as e:
                self.get_logger().error(f"Error generating LiDAR data: {e}")
                time.sleep(rate)
    
    def generate_radar_data(self):
        """Generate synthetic radar data"""
        rate = 1.0 / self.radar_rate
        
        while rclpy.ok():
            try:
                # Create synthetic radar data
                radar_data = self.create_synthetic_radar_data()
                
                # Create ROS message (using PointCloud2 as generic container)
                msg = PointCloud2()
                msg.header = Header()
                msg.header.stamp = self.get_clock().now().to_msg()
                msg.header.frame_id = "radar_frame"
                
                # Publish
                self.radar_pub.publish(msg)
                
                time.sleep(rate)
                
            except Exception as e:
                self.get_logger().error(f"Error generating radar data: {e}")
                time.sleep(rate)
    
    def generate_camera_data(self):
        """Generate synthetic camera images"""
        rate = 1.0 / self.camera_rate
        
        while rclpy.ok():
            try:
                # Create synthetic image
                image = self.create_synthetic_image()
                
                # Convert to ROS message
                msg = self.bridge.cv2_to_imgmsg(image, "bgr8")
                msg.header = Header()
                msg.header.stamp = self.get_clock().now().to_msg()
                msg.header.frame_id = "camera_frame"
                
                # Publish
                self.camera_pub.publish(msg)
                
                time.sleep(rate)
                
            except Exception as e:
                self.get_logger().error(f"Error generating camera data: {e}")
                time.sleep(rate)
    
    def create_synthetic_pointcloud(self) -> np.ndarray:
        """Create synthetic LiDAR point cloud with moving objects"""
        current_time = time.time()
        points = []
        
        # Ground plane points
        for x in range(-50, 51, 2):
            for y in range(-25, 26, 2):
                z = np.random.normal(0, 0.1)  # Ground with noise
                intensity = 50 + np.random.normal(0, 10)
                points.append([x, y, z, intensity])
        
        # Add objects
        for obj in self.scene_objects:
            # Update object position based on velocity
            pos = obj['position'].copy()
            vel = obj['velocity']
            pos[0] += vel[0] * (current_time % 10)  # Reset every 10 seconds
            pos[1] += vel[1] * (current_time % 10)
            
            # Generate points for object
            if obj['type'] == 'vehicle':
                object_points = self.generate_vehicle_points(pos)
            elif obj['type'] == 'person':
                object_points = self.generate_person_points(pos)
            elif obj['type'] == 'building':
                object_points = self.generate_building_points(pos)
            else:
                continue
            
            points.extend(object_points)
        
        # Add random noise points
        for _ in range(100):
            x = np.random.uniform(-100, 100)
            y = np.random.uniform(-50, 50)
            z = np.random.uniform(-2, 10)
            intensity = np.random.uniform(0, 255)
            points.append([x, y, z, intensity])
        
        return np.array(points)
    
    def generate_vehicle_points(self, center: list) -> list:
        """Generate points for a vehicle"""
        points = []
        # Vehicle dimensions: 4m x 2m x 1.5m
        for x in np.arange(-2, 2.1, 0.2):
            for y in np.arange(-1, 1.1, 0.2):
                for z in np.arange(0, 1.6, 0.3):
                    point = [
                        center[0] + x + np.random.normal(0, 0.05),
                        center[1] + y + np.random.normal(0, 0.05),
                        center[2] + z + np.random.normal(0, 0.05),
                        150 + np.random.normal(0, 20)  # High intensity
                    ]
                    points.append(point)
        return points
    
    def generate_person_points(self, center: list) -> list:
        """Generate points for a person"""
        points = []
        # Person dimensions: 0.5m x 0.5m x 1.7m
        for x in np.arange(-0.25, 0.26, 0.1):
            for y in np.arange(-0.25, 0.26, 0.1):
                for z in np.arange(0, 1.8, 0.2):
                    point = [
                        center[0] + x + np.random.normal(0, 0.02),
                        center[1] + y + np.random.normal(0, 0.02),
                        center[2] + z + np.random.normal(0, 0.02),
                        80 + np.random.normal(0, 15)  # Medium intensity
                    ]
                    points.append(point)
        return points
    
    def generate_building_points(self, center: list) -> list:
        """Generate points for a building"""
        points = []
        # Building dimensions: 10m x 10m x 5m
        for x in np.arange(-5, 5.1, 0.5):
            for y in np.arange(-5, 5.1, 0.5):
                for z in np.arange(0, 5.1, 0.5):
                    # Only generate points on surfaces
                    if (abs(x) > 4.5 or abs(y) > 4.5 or z < 0.5 or z > 4.5):
                        point = [
                            center[0] + x + np.random.normal(0, 0.1),
                            center[1] + y + np.random.normal(0, 0.1),
                            center[2] + z + np.random.normal(0, 0.1),
                            200 + np.random.normal(0, 30)  # Very high intensity
                        ]
                        points.append(point)
        return points
    
    def create_synthetic_radar_data(self) -> np.ndarray:
        """Create synthetic radar data"""
        # This would contain range-Doppler data
        # For demonstration, create random data
        range_bins = 256
        doppler_bins = 128
        
        # Start with noise
        data = np.random.randn(doppler_bins, range_bins) * 0.1
        
        # Add synthetic targets
        for obj in self.scene_objects:
            if obj['type'] in ['vehicle', 'person']:
                range_val = np.linalg.norm(obj['position'])
                velocity = np.linalg.norm(obj['velocity'])
                
                range_bin = int(range_val / 200.0 * range_bins)
                doppler_bin = int((velocity / 50.0 + 1) / 2 * doppler_bins)
                
                if 0 <= range_bin < range_bins and 0 <= doppler_bin < doppler_bins:
                    data[doppler_bin, range_bin] += 2.0  # Strong target
        
        return data
    
    def create_synthetic_image(self) -> np.ndarray:
        """Create synthetic camera image"""
        # Create base image
        height, width = 480, 640
        image = np.ones((height, width, 3), dtype=np.uint8) * 135  # Gray background
        
        # Add horizon
        cv2.line(image, (0, height//2), (width, height//2), (100, 150, 200), 2)
        
        # Add road
        road_points = np.array([
            [0, height],
            [width//3, height//2],
            [2*width//3, height//2],
            [width, height]
        ], np.int32)
        cv2.fillPoly(image, [road_points], (80, 80, 80))
        
        # Add lane markings
        for i in range(0, width, 40):
            cv2.line(image, (i, height//2 + 20), (i + 20, height//2 + 20), (255, 255, 255), 2)
        
        # Add objects based on scene
        current_time = time.time()
        for obj in self.scene_objects:
            # Project 3D position to 2D image coordinates
            pos = obj['position'].copy()
            vel = obj['velocity']
            pos[0] += vel[0] * (current_time % 10)
            pos[1] += vel[1] * (current_time % 10)
            
            # Simple perspective projection
            if pos[0] > 0:  # Object in front
                x = int(width//2 + pos[1] * 200 / pos[0])
                y = int(height//2 + 100 / pos[0])
                size = max(10, int(100 / pos[0]))
                
                if 0 <= x < width and 0 <= y < height:
                    if obj['type'] == 'vehicle':
                        cv2.rectangle(image, (x-size, y-size//2), (x+size, y+size//2), (0, 0, 255), -1)
                    elif obj['type'] == 'person':
                        cv2.circle(image, (x, y), size//2, (0, 255, 0), -1)
                    elif obj['type'] == 'building':
                        cv2.rectangle(image, (x-size*2, y-size*3), (x+size*2, y), (100, 100, 100), -1)
        
        # Add some noise
        noise = np.random.randint(-20, 21, image.shape, dtype=np.int16)
        image = np.clip(image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        return image


def main(args=None):
    """Main function"""
    rclpy.init(args=args)
    
    try:
        generator = SensorDataGenerator()
        rclpy.spin(generator)
    except KeyboardInterrupt:
        pass
    finally:
        if 'generator' in locals():
            generator.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
