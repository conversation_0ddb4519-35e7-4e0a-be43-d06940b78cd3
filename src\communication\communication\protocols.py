"""
Communication Protocols Module
Demonstrates UDP, CAN, and Serial communication for sensor data
"""

import socket
import threading
import time
import struct
import serial
import queue
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from enum import Enum
import numpy as np
import rclpy
from rclpy.node import Node


class MessageType(Enum):
    """Message types for communication protocols"""
    SENSOR_DATA = 0x01
    COMMAND = 0x02
    STATUS = 0x03
    HEARTBEAT = 0x04
    ERROR = 0xFF


@dataclass
class Message:
    """Generic message structure"""
    message_type: MessageType
    timestamp: float
    source_id: int
    data: bytes
    checksum: Optional[int] = None


class UDPCommunication:
    """
    High-speed UDP communication for sensor data transmission
    Demonstrates real-time data streaming with minimal latency
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.local_ip = config.get('local_ip', '127.0.0.1')
        self.local_port = config.get('local_port', 8888)
        self.remote_ip = config.get('remote_ip', '127.0.0.1')
        self.remote_port = config.get('remote_port', 8889)
        self.buffer_size = config.get('buffer_size', 65536)
        
        # Socket setup
        self.socket = None
        self.is_running = False
        self.receive_thread = None
        self.send_queue = queue.Queue()
        
        # Callbacks
        self.message_callback: Optional[Callable[[Message], None]] = None
        
        # Statistics
        self.bytes_sent = 0
        self.bytes_received = 0
        self.messages_sent = 0
        self.messages_received = 0
        self.last_stats_time = time.time()
    
    def start(self):
        """Start UDP communication"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind((self.local_ip, self.local_port))
            self.socket.settimeout(0.1)  # Non-blocking with timeout
            
            self.is_running = True
            self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
            self.receive_thread.start()
            
            print(f"UDP communication started on {self.local_ip}:{self.local_port}")
            
        except Exception as e:
            print(f"Error starting UDP communication: {e}")
    
    def stop(self):
        """Stop UDP communication"""
        self.is_running = False
        
        if self.receive_thread and self.receive_thread.is_alive():
            self.receive_thread.join(timeout=1.0)
        
        if self.socket:
            self.socket.close()
        
        print("UDP communication stopped")
    
    def send_message(self, message: Message, target_ip: Optional[str] = None, 
                    target_port: Optional[int] = None) -> bool:
        """Send a message via UDP"""
        if not self.socket:
            return False
        
        try:
            # Serialize message
            serialized = self._serialize_message(message)
            
            # Send to target or default remote
            ip = target_ip or self.remote_ip
            port = target_port or self.remote_port
            
            self.socket.sendto(serialized, (ip, port))
            
            # Update statistics
            self.bytes_sent += len(serialized)
            self.messages_sent += 1
            
            return True
            
        except Exception as e:
            print(f"Error sending UDP message: {e}")
            return False
    
    def send_sensor_data(self, sensor_id: int, data: np.ndarray) -> bool:
        """Send sensor data via UDP"""
        # Convert numpy array to bytes
        data_bytes = data.tobytes()
        
        message = Message(
            message_type=MessageType.SENSOR_DATA,
            timestamp=time.time(),
            source_id=sensor_id,
            data=data_bytes
        )
        
        return self.send_message(message)
    
    def _receive_loop(self):
        """Main receive loop"""
        while self.is_running:
            try:
                data, addr = self.socket.recvfrom(self.buffer_size)
                
                # Deserialize message
                message = self._deserialize_message(data)
                
                if message and self.message_callback:
                    self.message_callback(message)
                
                # Update statistics
                self.bytes_received += len(data)
                self.messages_received += 1
                
            except socket.timeout:
                continue
            except Exception as e:
                if self.is_running:
                    print(f"Error in UDP receive loop: {e}")
    
    def _serialize_message(self, message: Message) -> bytes:
        """Serialize message to bytes"""
        # Message format: [type(1)] [timestamp(8)] [source_id(4)] [data_length(4)] [data] [checksum(4)]
        
        header = struct.pack(
            '>BdII',  # Big-endian: byte, double, uint, uint
            message.message_type.value,
            message.timestamp,
            message.source_id,
            len(message.data)
        )
        
        # Calculate checksum
        checksum = self._calculate_checksum(header + message.data)
        checksum_bytes = struct.pack('>I', checksum)
        
        return header + message.data + checksum_bytes
    
    def _deserialize_message(self, data: bytes) -> Optional[Message]:
        """Deserialize bytes to message"""
        if len(data) < 21:  # Minimum message size
            return None
        
        try:
            # Unpack header
            msg_type, timestamp, source_id, data_length = struct.unpack('>BdII', data[:17])
            
            # Extract message data
            if len(data) < 17 + data_length + 4:
                return None
            
            message_data = data[17:17 + data_length]
            received_checksum = struct.unpack('>I', data[17 + data_length:17 + data_length + 4])[0]
            
            # Verify checksum
            calculated_checksum = self._calculate_checksum(data[:17 + data_length])
            if calculated_checksum != received_checksum:
                print("UDP message checksum mismatch")
                return None
            
            return Message(
                message_type=MessageType(msg_type),
                timestamp=timestamp,
                source_id=source_id,
                data=message_data,
                checksum=received_checksum
            )
            
        except Exception as e:
            print(f"Error deserializing UDP message: {e}")
            return None
    
    def _calculate_checksum(self, data: bytes) -> int:
        """Calculate simple checksum"""
        return sum(data) & 0xFFFFFFFF
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get communication statistics"""
        current_time = time.time()
        elapsed = current_time - self.last_stats_time
        
        stats = {
            'bytes_sent': self.bytes_sent,
            'bytes_received': self.bytes_received,
            'messages_sent': self.messages_sent,
            'messages_received': self.messages_received,
            'send_rate_bps': self.bytes_sent / elapsed if elapsed > 0 else 0,
            'receive_rate_bps': self.bytes_received / elapsed if elapsed > 0 else 0,
            'send_rate_mps': self.messages_sent / elapsed if elapsed > 0 else 0,
            'receive_rate_mps': self.messages_received / elapsed if elapsed > 0 else 0
        }
        
        # Reset counters
        self.bytes_sent = 0
        self.bytes_received = 0
        self.messages_sent = 0
        self.messages_received = 0
        self.last_stats_time = current_time
        
        return stats


class CANCommunication:
    """
    CAN bus communication for automotive applications
    Demonstrates automotive protocol implementation
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.interface = config.get('interface', 'vcan0')
        self.bitrate = config.get('bitrate', 500000)  # 500 kbps
        self.node_id = config.get('node_id', 0x123)
        
        # CAN specific
        self.can_socket = None
        self.is_running = False
        self.receive_thread = None
        
        # Message callbacks
        self.message_callbacks: Dict[int, Callable] = {}
        
        # Statistics
        self.frames_sent = 0
        self.frames_received = 0
        self.errors = 0
    
    def start(self):
        """Start CAN communication"""
        try:
            # Note: This is a simplified implementation
            # In practice, you would use python-can library
            print(f"CAN communication started on {self.interface} at {self.bitrate} bps")
            print("Note: This is a demonstration - actual CAN implementation requires python-can library")
            
            self.is_running = True
            self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
            self.receive_thread.start()
            
        except Exception as e:
            print(f"Error starting CAN communication: {e}")
    
    def stop(self):
        """Stop CAN communication"""
        self.is_running = False
        
        if self.receive_thread and self.receive_thread.is_alive():
            self.receive_thread.join(timeout=1.0)
        
        print("CAN communication stopped")
    
    def send_frame(self, can_id: int, data: bytes) -> bool:
        """Send CAN frame"""
        if len(data) > 8:
            print("CAN data too long (max 8 bytes)")
            return False
        
        try:
            # Simulate sending CAN frame
            print(f"Sending CAN frame: ID=0x{can_id:03X}, Data={data.hex()}")
            self.frames_sent += 1
            return True
            
        except Exception as e:
            print(f"Error sending CAN frame: {e}")
            self.errors += 1
            return False
    
    def send_sensor_data(self, sensor_type: int, value: float) -> bool:
        """Send sensor data via CAN"""
        # Pack float as 4 bytes
        data = struct.pack('>f', value)
        can_id = 0x200 + sensor_type  # Base ID + sensor type
        
        return self.send_frame(can_id, data)
    
    def register_callback(self, can_id: int, callback: Callable):
        """Register callback for specific CAN ID"""
        self.message_callbacks[can_id] = callback
    
    def _receive_loop(self):
        """Main CAN receive loop"""
        while self.is_running:
            try:
                # Simulate receiving CAN frames
                time.sleep(0.1)
                
                # Generate synthetic CAN frame for demonstration
                if self.frames_received % 10 == 0:  # Every 10th iteration
                    can_id = 0x100
                    data = struct.pack('>f', time.time())
                    
                    print(f"Received CAN frame: ID=0x{can_id:03X}, Data={data.hex()}")
                    
                    if can_id in self.message_callbacks:
                        self.message_callbacks[can_id](can_id, data)
                    
                    self.frames_received += 1
                
            except Exception as e:
                if self.is_running:
                    print(f"Error in CAN receive loop: {e}")
                    self.errors += 1
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get CAN communication statistics"""
        return {
            'frames_sent': self.frames_sent,
            'frames_received': self.frames_received,
            'errors': self.errors,
            'interface': self.interface,
            'bitrate': self.bitrate
        }


class SerialCommunication:
    """
    Serial communication for low-level device communication
    Demonstrates UART/RS232 protocol implementation
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.port = config.get('port', '/dev/ttyUSB0')
        self.baudrate = config.get('baudrate', 115200)
        self.timeout = config.get('timeout', 1.0)
        self.bytesize = config.get('bytesize', 8)
        self.parity = config.get('parity', 'N')
        self.stopbits = config.get('stopbits', 1)
        
        # Serial connection
        self.serial_conn = None
        self.is_running = False
        self.receive_thread = None
        
        # Message handling
        self.message_callback: Optional[Callable[[bytes], None]] = None
        self.command_queue = queue.Queue()
        
        # Statistics
        self.bytes_sent = 0
        self.bytes_received = 0
        self.errors = 0
    
    def start(self):
        """Start serial communication"""
        try:
            # Note: This would require actual serial hardware
            print(f"Serial communication started on {self.port} at {self.baudrate} baud")
            print("Note: This is a demonstration - actual implementation requires pyserial library")
            
            self.is_running = True
            self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
            self.receive_thread.start()
            
        except Exception as e:
            print(f"Error starting serial communication: {e}")
    
    def stop(self):
        """Stop serial communication"""
        self.is_running = False
        
        if self.receive_thread and self.receive_thread.is_alive():
            self.receive_thread.join(timeout=1.0)
        
        if self.serial_conn:
            self.serial_conn.close()
        
        print("Serial communication stopped")
    
    def send_data(self, data: bytes) -> bool:
        """Send data via serial"""
        try:
            # Simulate sending serial data
            print(f"Sending serial data: {data.hex()}")
            self.bytes_sent += len(data)
            return True
            
        except Exception as e:
            print(f"Error sending serial data: {e}")
            self.errors += 1
            return False
    
    def send_command(self, command: str) -> bool:
        """Send text command via serial"""
        command_bytes = (command + '\r\n').encode('ascii')
        return self.send_data(command_bytes)
    
    def _receive_loop(self):
        """Main serial receive loop"""
        buffer = b''
        
        while self.is_running:
            try:
                # Simulate receiving serial data
                time.sleep(0.1)
                
                # Generate synthetic data for demonstration
                if self.bytes_received % 100 == 0:  # Periodic data
                    data = f"SENSOR_DATA:{time.time():.3f}\r\n".encode('ascii')
                    
                    print(f"Received serial data: {data.decode('ascii').strip()}")
                    
                    if self.message_callback:
                        self.message_callback(data)
                    
                    self.bytes_received += len(data)
                
            except Exception as e:
                if self.is_running:
                    print(f"Error in serial receive loop: {e}")
                    self.errors += 1
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get serial communication statistics"""
        return {
            'bytes_sent': self.bytes_sent,
            'bytes_received': self.bytes_received,
            'errors': self.errors,
            'port': self.port,
            'baudrate': self.baudrate
        }


class CommunicationManager:
    """
    Unified communication manager for all protocols
    Demonstrates multi-protocol communication architecture
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Protocol instances
        self.udp_comm = None
        self.can_comm = None
        self.serial_comm = None
        
        # Message routing
        self.message_handlers: Dict[str, Callable] = {}
        
        # Initialize protocols based on configuration
        if config.get('enable_udp', True):
            self.udp_comm = UDPCommunication(config.get('udp', {}))
        
        if config.get('enable_can', False):
            self.can_comm = CANCommunication(config.get('can', {}))
        
        if config.get('enable_serial', False):
            self.serial_comm = SerialCommunication(config.get('serial', {}))
    
    def start_all(self):
        """Start all enabled communication protocols"""
        if self.udp_comm:
            self.udp_comm.start()
            self.udp_comm.message_callback = self._handle_udp_message
        
        if self.can_comm:
            self.can_comm.start()
        
        if self.serial_comm:
            self.serial_comm.start()
            self.serial_comm.message_callback = self._handle_serial_message
    
    def stop_all(self):
        """Stop all communication protocols"""
        if self.udp_comm:
            self.udp_comm.stop()
        
        if self.can_comm:
            self.can_comm.stop()
        
        if self.serial_comm:
            self.serial_comm.stop()
    
    def register_handler(self, protocol: str, handler: Callable):
        """Register message handler for specific protocol"""
        self.message_handlers[protocol] = handler
    
    def broadcast_sensor_data(self, sensor_id: int, data: np.ndarray):
        """Broadcast sensor data via all available protocols"""
        if self.udp_comm:
            self.udp_comm.send_sensor_data(sensor_id, data)
        
        if self.can_comm and data.size == 1:
            # CAN can only send single values
            self.can_comm.send_sensor_data(sensor_id, float(data.flat[0]))
        
        if self.serial_comm:
            # Send as text over serial
            data_str = f"SENSOR_{sensor_id}:{data.tolist()}"
            self.serial_comm.send_command(data_str)
    
    def _handle_udp_message(self, message: Message):
        """Handle incoming UDP message"""
        if 'udp' in self.message_handlers:
            self.message_handlers['udp'](message)
    
    def _handle_serial_message(self, data: bytes):
        """Handle incoming serial message"""
        if 'serial' in self.message_handlers:
            self.message_handlers['serial'](data)
    
    def get_all_statistics(self) -> Dict[str, Any]:
        """Get statistics from all protocols"""
        stats = {}
        
        if self.udp_comm:
            stats['udp'] = self.udp_comm.get_statistics()
        
        if self.can_comm:
            stats['can'] = self.can_comm.get_statistics()
        
        if self.serial_comm:
            stats['serial'] = self.serial_comm.get_statistics()
        
        return stats
