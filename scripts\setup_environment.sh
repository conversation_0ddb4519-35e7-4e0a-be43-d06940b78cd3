#!/bin/bash

# Perception Engineering Environment Setup Script
# Sets up complete development environment for perception engineering

set -e  # Exit on any error

echo "=========================================="
echo "Perception Engineering Environment Setup"
echo "=========================================="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on supported OS
check_os() {
    print_status "Checking operating system..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_success "Linux detected"
        OS="linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        print_success "macOS detected"
        OS="macos"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

# Install system dependencies
install_system_dependencies() {
    print_status "Installing system dependencies..."
    
    if [[ "$OS" == "linux" ]]; then
        # Update package list
        sudo apt-get update
        
        # Install essential packages
        sudo apt-get install -y \
            python3 \
            python3-pip \
            python3-venv \
            python3-dev \
            build-essential \
            cmake \
            git \
            curl \
            wget \
            software-properties-common \
            lsb-release \
            gnupg2
        
        # Install ROS2 dependencies
        sudo apt-get install -y \
            python3-colcon-common-extensions \
            python3-rosdep \
            python3-vcstool \
            python3-argcomplete
        
        # Install OpenCV dependencies
        sudo apt-get install -y \
            libopencv-dev \
            python3-opencv \
            libgtk-3-dev \
            libavcodec-dev \
            libavformat-dev \
            libswscale-dev \
            libv4l-dev \
            libxvidcore-dev \
            libx264-dev
        
        # Install scientific computing libraries
        sudo apt-get install -y \
            libblas-dev \
            liblapack-dev \
            libatlas-base-dev \
            gfortran \
            libhdf5-dev
        
        # Install communication protocol dependencies
        sudo apt-get install -y \
            can-utils \
            libsocketcan-dev \
            python3-serial
        
    elif [[ "$OS" == "macos" ]]; then
        # Check if Homebrew is installed
        if ! command -v brew &> /dev/null; then
            print_status "Installing Homebrew..."
            /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        fi
        
        # Install packages via Homebrew
        brew install \
            python3 \
            cmake \
            opencv \
            hdf5 \
            pkg-config
    fi
    
    print_success "System dependencies installed"
}

# Install ROS2
install_ros2() {
    print_status "Installing ROS2..."
    
    if [[ "$OS" == "linux" ]]; then
        # Add ROS2 repository
        sudo curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg
        echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros2/ubuntu $(. /etc/os-release && echo $UBUNTU_CODENAME) main" | sudo tee /etc/apt/sources.list.d/ros2.list > /dev/null
        
        # Update and install ROS2
        sudo apt-get update
        sudo apt-get install -y ros-humble-desktop
        
        # Install additional ROS2 packages
        sudo apt-get install -y \
            ros-humble-cv-bridge \
            ros-humble-image-transport \
            ros-humble-tf2 \
            ros-humble-tf2-ros \
            ros-humble-tf2-geometry-msgs \
            ros-humble-visualization-msgs \
            ros-humble-rviz2
        
        # Source ROS2 setup
        echo "source /opt/ros/humble/setup.bash" >> ~/.bashrc
        source /opt/ros/humble/setup.bash
        
    elif [[ "$OS" == "macos" ]]; then
        print_warning "ROS2 installation on macOS requires manual setup"
        print_status "Please follow: https://docs.ros.org/en/humble/Installation/macOS-Install-Binary.html"
    fi
    
    print_success "ROS2 installation completed"
}

# Create Python virtual environment
setup_python_environment() {
    print_status "Setting up Python virtual environment..."
    
    # Create virtual environment
    python3 -m venv venv
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip setuptools wheel
    
    # Install Python dependencies
    pip install \
        numpy \
        scipy \
        matplotlib \
        opencv-python \
        scikit-learn \
        scikit-image \
        pandas \
        seaborn \
        jupyter \
        ipython \
        pytest \
        pytest-cov \
        black \
        flake8 \
        mypy
    
    # Install ROS2 Python packages
    pip install \
        rclpy \
        cv-bridge \
        tf2-ros \
        geometry-msgs \
        sensor-msgs \
        visualization-msgs
    
    # Install communication libraries
    pip install \
        pyserial \
        python-can \
        cantools
    
    # Install additional scientific packages
    pip install \
        sympy \
        numba \
        cython \
        h5py \
        pillow
    
    print_success "Python environment setup completed"
}

# Initialize rosdep
initialize_rosdep() {
    print_status "Initializing rosdep..."
    
    if ! command -v rosdep &> /dev/null; then
        print_warning "rosdep not found, installing..."
        sudo apt-get install -y python3-rosdep
    fi
    
    # Initialize rosdep
    if [ ! -f /etc/ros/rosdep/sources.list.d/20-default.list ]; then
        sudo rosdep init
    fi
    
    rosdep update
    
    print_success "rosdep initialized"
}

# Build the workspace
build_workspace() {
    print_status "Building ROS2 workspace..."
    
    # Source ROS2 if available
    if [ -f "/opt/ros/humble/setup.bash" ]; then
        source /opt/ros/humble/setup.bash
    fi
    
    # Install dependencies
    if command -v rosdep &> /dev/null; then
        rosdep install --from-paths src --ignore-src -r -y
    fi
    
    # Build with colcon
    if command -v colcon &> /dev/null; then
        colcon build --symlink-install
        print_success "Workspace built successfully"
    else
        print_warning "colcon not available, skipping build"
    fi
}

# Setup development tools
setup_development_tools() {
    print_status "Setting up development tools..."
    
    # Create useful aliases
    cat >> ~/.bashrc << 'EOF'

# Perception Engineering Aliases
alias perception_ws='cd ~/perception_engineering && source install/setup.bash'
alias build_perception='colcon build --symlink-install'
alias test_perception='colcon test && colcon test-result --verbose'
alias clean_perception='rm -rf build install log'

# ROS2 Aliases
alias ros2_source='source /opt/ros/humble/setup.bash'
alias ros2_ws_source='source install/setup.bash'

EOF

    # Create development configuration
    mkdir -p .vscode
    cat > .vscode/settings.json << 'EOF'
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length", "88"],
    "files.associations": {
        "*.launch": "xml",
        "*.urdf": "xml",
        "*.xacro": "xml"
    },
    "ros.distro": "humble"
}
EOF

    # Create launch configuration
    cat > .vscode/launch.json << 'EOF'
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}/src:${env:PYTHONPATH}"
            }
        },
        {
            "name": "ROS2: Launch File",
            "type": "python",
            "request": "launch",
            "program": "/opt/ros/humble/bin/ros2",
            "args": ["launch", "${file}"],
            "console": "integratedTerminal"
        }
    ]
}
EOF

    print_success "Development tools configured"
}

# Create test data
create_test_data() {
    print_status "Creating test data..."
    
    mkdir -p data/test
    
    # Create sample configuration files
    cp config/perception_system.yaml data/test/test_config.yaml
    
    print_success "Test data created"
}

# Verify installation
verify_installation() {
    print_status "Verifying installation..."
    
    # Check Python packages
    python3 -c "import numpy, scipy, cv2, sklearn; print('Python packages OK')"
    
    # Check ROS2
    if command -v ros2 &> /dev/null; then
        print_success "ROS2 installation verified"
    else
        print_warning "ROS2 not found in PATH"
    fi
    
    # Check colcon
    if command -v colcon &> /dev/null; then
        print_success "colcon build tool verified"
    else
        print_warning "colcon not found"
    fi
    
    print_success "Installation verification completed"
}

# Main setup function
main() {
    echo "Starting environment setup..."
    echo
    
    check_os
    install_system_dependencies
    install_ros2
    setup_python_environment
    initialize_rosdep
    build_workspace
    setup_development_tools
    create_test_data
    verify_installation
    
    echo
    echo "=========================================="
    echo "Setup completed successfully!"
    echo "=========================================="
    echo
    echo "Next steps:"
    echo "1. Restart your terminal or run: source ~/.bashrc"
    echo "2. Activate the environment: source venv/bin/activate"
    echo "3. Source ROS2: source /opt/ros/humble/setup.bash"
    echo "4. Source workspace: source install/setup.bash"
    echo "5. Run tests: python3 scripts/test_communication.py"
    echo "6. Launch pipeline: ros2 launch perception_engineering full_pipeline.launch.py"
    echo
    echo "For development:"
    echo "- Use 'perception_ws' alias to quickly navigate and source workspace"
    echo "- Use 'build_perception' alias to build the workspace"
    echo "- Use 'test_perception' alias to run tests"
    echo
    print_success "Environment setup complete! 🎉"
}

# Run main function
main "$@"
