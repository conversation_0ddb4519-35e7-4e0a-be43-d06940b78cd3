{"version": 3, "file": "131.729c28b8323daf822cbe.js?v=729c28b8323daf822cbe", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAsB;AACe;AACE;AACgD;AAChF;AACP;AACA;AACA;AACA;AACA;AACO,6CAA6C,EAAE,EAAE,IAAI;AACrD,6DAA6D,aAAa;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC,6FAA6F;AAC7F;AACA;AACe,yFAAyF,+BAA+B,eAAG;AAC1I,+BAA+B,uCAAuC;AACtE;AACA,QAAQ,cAAU;AAClB;AACA;AACA,QAAQ,cAAU;AAClB;AACA;AACA;AACA;AACA;AACA,mBAAmB,kCAAwB;AAC3C,mBAAmB,wCAA8B;AACjD;AACA;AACA;AACA;AACA;AACA,QAAQ,kBAAQ;AAChB;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;;;;ACrD6B;AACgI;AAC7J;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA,gBAAgB,mEAAmE;AACnF,cAAc,eAAe;AAC7B;AACA,uBAAuB,UAAU,EAAE,QAAQ;AAC3C;AACA,qCAAqC,SAAS,GAAG,uBAAuB;AACxE;AACA,kCAAkC,0BAAY,CAAC,aAAG,cAAc,4BAA4B;AAC5F;AACA;AACA;AACA;AACA,0CAA0C,aAAG,gBAAgB,wBAAc;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,0BAAY,CAAC,aAAG,cAAc,4BAA4B;AAC5F;AACA,4BAA4B,cAAc,IAAI,QAAQ;AACtD;AACA;AACA;AACA;AACA,gCAAgC,kBAAkB,IAAI,QAAQ;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe;AACf,YAAY,sCAAsC;AAClD;AACA;AACA,+BAA+B,mCAAmC;AAClE;AACA;AACA;AACA;AACA,sBAAsB,2BAAa;AACnC;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,wBAAwB,iCAAmB;AAC3C,qDAAqD,gCAAkB;AACvE,4BAA4B,gCAAkB;AAC9C,WAAW,iCAAmB,GAAG,qBAAqB;AACtD;AACA;;AC5FuG;AACnD;AACkB;AACtE;AACA;AACe;AACf;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,wFAAwF;AACxG,mBAAmB,iBAAiB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,yBAAW;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,gBAAM;AACzB,0DAA0D,gBAAM;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,0BAA0B;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,gBAAM,oCAAoC,4BAAkB;AAC1G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,6BAAe;AACzD,yDAAyD,gBAAM,oCAAoC,2BAAa;AAChH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC1HwC;AACxC;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACe,wCAAwC;AACvD,eAAe,aAAa;AAC5B;AACA;;;;;ACZ6B;AACQ;AAC6D;AAC5B;AACtE;AACA;AACA;AACe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,aAAG,SAAS,gBAAM,KAAK,2BAAa;AACxD;AACA;AACA,qGAAqG,IAAI;AACzG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,iBAAO;AACpB;AACA,uCAAuC,4BAAc;AACrD,iBAAiB,iBAAO;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,yBAAW;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,0BAA0B;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,aAAG,SAAS,gBAAM,MAAM,wBAAc;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;;ACnH8D;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe;AACf,eAAe,wBAAwB;AACvC;AACA;;ACfsD;AACgB;AACZ;AAClC;AACxB,0CAAe,kBAAkB,EAAE,EAAC;AACpC;;;;;;;;ACLa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB,GAAG,mBAAmB,GAAG,mBAAmB;AAC/D;AACA,aAAa;AACb;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mFAAmF,cAAc,EAAE,+BAA+B,IAAI,GAAG,EAAE,eAAe,IAAI,GAAG,EAAE,aAAa,IAAI,gBAAgB,IAAI,GAAG,EAAE,gBAAgB,IAAI,GAAG,IAAI,SAAS,IAAI,gBAAgB,IAAI,GAAG,EAAE,gBAAgB,IAAI,GAAG,IAAI,SAAS,IAAI,gBAAgB,IAAI,GAAG,EAAE,gBAAgB,IAAI,GAAG,IAAI,SAAS,IAAI,aAAa,IAAI,iBAAiB,IAAI,GAAG,IAAI,SAAS,IAAI,iBAAiB,IAAI,UAAU,IAAI,uCAAuC,EAAE,gDAAgD,IAAI,GAAG,IAAI,SAAS,IAAI,aAAa,IAAI,gBAAgB,IAAI,GAAG,IAAI,SAAS,IAAI,2CAA2C,8CAA8C,EAAE,0DAA0D,aAAa,EAAE,2CAA2C,eAAe,EAAE,oCAAoC,eAAe,EAAE,iCAAiC,eAAe,EAAE,iCAAiC,eAAe,EAAE,iCAAiC,eAAe,EAAE,qCAAqC,iBAAiB,EAAE,kCAAkC,iBAAiB,EAAE;AACtpC;AACA,iDAAiD,EAAE,YAAY,EAAE,IAAI,MAAM,gCAAgC,EAAE,iBAAiB,IAAI,gCAAgC,EAAE,iBAAiB,IAAI,SAAS;AAClM;AACA;AACA,wEAAwE,IAAI,EAAE,EAAE,iCAAiC,IAAI,EAAE,EAAE,sCAAsC,IAAI,EAAE,EAAE,gDAAgD,IAAI,oBAAoB,EAAE,0DAA0D,KAAK,IAAI,KAAK,eAAe,KAAK,IAAI,KAAK,qBAAqB,KAAK,IAAI,KAAK,eAAe,KAAK,IAAI,KAAK,mBAAmB,KAAK,IAAI,KAAK,EAAE,GAAG,UAAU,IAAI;AAC1c,mCAAmC,EAAE,+BAA+B,EAAE;AACtE,qBAAqB,MAAM,0BAA0B,KAAK,oCAAoC,KAAK;AACnG;AACA,kDAAkD,EAAE;AACpD,wBAAwB,IAAI,GAAG,EAAE,UAAU,IAAI,gBAAgB,IAAI,GAAG,EAAE,WAAW,IAAI,yEAAyE,EAAE,iBAAiB,IAAI,GAAG,EAAE,aAAa,IAAI,EAAE,IAAI,2EAA2E,EAAE,iBAAiB,IAAI,GAAG,EAAE,aAAa,IAAI,EAAE,IAAI,cAAc,IAAI,2EAA2E,EAAE,kBAAkB,IAAI,GAAG,EAAE,aAAa,IAAI,EAAE,IAAI,cAAc,IAAI,EAAE,IAAI,yEAAyE,EAAE,kBAAkB,IAAI,GAAG,EAAE,aAAa,IAAI,EAAE,IAAI,cAAc,IAAI,EAAE,IAAI,yEAAyE,EAAE,kBAAkB,IAAI,GAAG,EAAE,aAAa,IAAI,EAAE,IAAI,cAAc,IAAI,EAAE,IAAI,yEAAyE,EAAE,sBAAsB,IAAI,EAAE,IAAI,cAAc,IAAI,EAAE,IAAI,yEAAyE,EAAE;AACl/B;AACA;AACA,mCAAmC,EAAE,aAAa,EAAE,GAAG,EAAE,SAAS,GAAG;AACrE;AACA;AACA;AACA,iEAAiE,eAAe,EAAE;AAClF;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,yCAAyC;AACtD;AACA,aAAa,yCAAyC;AACtD;AACA,aAAa,0CAA0C;AACvD;AACA,cAAc,0CAA0C;AACxD;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,EAAE,0BAA0B,KAAK,oCAAoC,KAAK;AAC9G;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,cAAc,EAAE,+BAA+B,IAAI,GAAG,EAAE,eAAe,IAAI,GAAG,EAAE,aAAa,IAAI,gBAAgB,IAAI,GAAG,EAAE,gBAAgB,IAAI,GAAG,IAAI,SAAS,IAAI,gBAAgB,IAAI,GAAG,EAAE,gBAAgB,IAAI,GAAG,IAAI,SAAS,IAAI,gBAAgB,IAAI,GAAG,EAAE,gBAAgB,IAAI,GAAG,IAAI,SAAS,IAAI,aAAa,IAAI,iBAAiB,IAAI,GAAG,IAAI,SAAS,IAAI,iBAAiB,IAAI,UAAU,IAAI,uCAAuC,EAAE,gDAAgD,IAAI,GAAG,IAAI,SAAS,IAAI,aAAa,IAAI,gBAAgB,IAAI,GAAG,IAAI,SAAS,IAAI,2CAA2C,8CAA8C,EAAE,yDAAyD,aAAa,EAAE,0CAA0C,eAAe,EAAE,mCAAmC,eAAe,EAAE,gCAAgC,eAAe,EAAE,gCAAgC,eAAe,EAAE,gCAAgC,eAAe,EAAE,mCAAmC,iBAAiB,EAAE,iCAAiC,iBAAiB,EAAE;AACnoC;AACA;AACA;AACA;AACA,gCAAgC,EAAE,mBAAmB,EAAE,iBAAiB,EAAE;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AC5Ka;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAW;AACrC,gBAAgB,mBAAO,CAAC,KAAS;AACjC,kBAAkB,mBAAO,CAAC,KAA0B;AACpD;AACA;AACA,qCAAqC,gBAAgB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,KAAK;AAChD;AACA;AACA;AACA;AACA;AACA,0IAA0I,WAAW;AACrJ;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAe;AACf;;;;;;;;ACpCa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,6BAA6B;AAC7B,cAAc,mBAAO,CAAC,KAAK;AAC3B,kBAAkB,mBAAO,CAAC,KAA0B;AACpD;AACA;AACA,qBAAqB,wCAAwC;AAC7D,qBAAqB,wCAAwC;AAC7D,8BAA8B,uCAAuC;AACrE,8BAA8B,uCAAuC;AACrE;AACA;AACA,gBAAgB,qBAAqB,gCAAgC,qBAAqB,EAAE,WAAW;AACvG,eAAe,qBAAqB,mBAAmB,cAAc,oBAAoB,WAAW,YAAY;AAChH;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,qCAAqC;AACrD,gBAAgB,aAAa;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,wDAAwD,KAAK,GAAG,gBAAgB;AAChF,6DAA6D,KAAK,6BAA6B,KAAK,0CAA0C,IAAI;AAClJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,QAAQ,aAAa,OAAO;AAChE;AACA;AACA;AACA;AACA,yDAAyD,kBAAkB,EAAE,8BAA8B;AAC3G,aAAa;AACb;AACA;AACA;AACA,kCAAkC,IAAI,WAAW,KAAK,IAAI,WAAW,IAAI,oBAAoB;AAC7F;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;;ACpEa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB,GAAG,uBAAuB,GAAG,eAAe,GAAG,YAAY,GAAG,WAAW,GAAG,iBAAiB,GAAG,WAAW,GAAG,SAAS,GAAG,kBAAkB,GAAG,WAAW;AACjL,eAAe,mBAAO,CAAC,KAAQ;AAC/B,iBAAiB,mBAAO,CAAC,KAAuB;AAChD,wBAAwB,mBAAO,CAAC,KAA8B;AAC9D,yBAAyB,mBAAO,CAAC,KAAkC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,kBAAkB;AAClB,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAe;AACf,iBAAiB,mBAAO,CAAC,KAAoB;AAC7C,8CAA6C,EAAE,qCAAqC,iCAAiC,EAAC;AACtH,gBAAgB,mBAAO,CAAC,KAAmB;AAC3C,qCAAoC,EAAE,qCAAqC,uBAAuB,EAAC;AACnG,uCAAsC,EAAE,qCAAqC,yBAAyB,EAAC;AACvG,6CAA4C,EAAE,qCAAqC,+BAA+B,EAAC;AACnH,uCAAsC,EAAE,qCAAqC,yBAAyB,EAAC;AACvG,wCAAuC,EAAE,qCAAqC,0BAA0B,EAAC;AACzG,2CAA0C,EAAE,qCAAqC,6BAA6B,EAAC;AAC/G,yBAAyB,mBAAO,CAAC,KAA4B;AAC7D,mDAAkD,EAAE,qCAAqC,sCAAsC,EAAC;AAChI,kBAAkB,mBAAO,CAAC,KAAqB;AAC/C,mDAAkD,EAAE,qCAAqC,+BAA+B,EAAC;AACzH;;;;;;;;ACjDa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,GAAG,wBAAwB,GAAG,mBAAmB,GAAG,qBAAqB,GAAG,iBAAiB,GAAG,iBAAiB,GAAG,kBAAkB,GAAG,WAAW,GAAG,SAAS,GAAG,WAAW,GAAG,aAAa,GAAG,YAAY,GAAG,kBAAkB,GAAG,mBAAmB;AAC1Q;AACA;AACA;AACA,mBAAmB;AACnB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+GAA+G,EAAE,EAAE,EAAE;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,IAAI;AACb;AACA;AACA,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,eAAe,EAAE,EAAE;AACzC;AACA;AACA;AACA;AACA;AACA,mBAAmB,EAAE,EAAE,WAAW;AAClC;AACA;AACA;AACA,4DAA4D,GAAG,EAAE,GAAG;AACpE;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,kFAAkF,IAAI,WAAW,IAAI;AACrG;AACA,mBAAmB;AACnB;AACA;AACA;AACA,4BAA4B,IAAI;AAChC;AACA,qDAAqD,IAAI;AACzD;AACA,wBAAwB;AACxB;AACA;AACA;AACA,kBAAkB;AAClB;;;;;;;;AC3Ja;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,UAAU,GAAG,WAAW,GAAG,WAAW,GAAG,eAAe,GAAG,iBAAiB,GAAG,gBAAgB,GAAG,sBAAsB,GAAG,kBAAkB,GAAG,aAAa,GAAG,YAAY,GAAG,kBAAkB,GAAG,iBAAiB,GAAG,mBAAmB,GAAG,WAAW,GAAG,iBAAiB,GAAG,WAAW,GAAG,SAAS;AACvS,eAAe,mBAAO,CAAC,KAAQ;AAC/B,gBAAgB,mBAAO,CAAC,KAAS;AACjC,aAAa,mBAAO,CAAC,KAAQ;AAC7B,qCAAoC,EAAE,qCAAqC,oBAAoB,EAAC;AAChG,uCAAsC,EAAE,qCAAqC,sBAAsB,EAAC;AACpG,6CAA4C,EAAE,qCAAqC,4BAA4B,EAAC;AAChH,uCAAsC,EAAE,qCAAqC,sBAAsB,EAAC;AACpG,+CAA8C,EAAE,qCAAqC,8BAA8B,EAAC;AACpH,6CAA4C,EAAE,qCAAqC,4BAA4B,EAAC;AAChH,8CAA6C,EAAE,qCAAqC,6BAA6B,EAAC;AAClH,wCAAuC,EAAE,qCAAqC,uBAAuB,EAAC;AACtG,cAAc,mBAAO,CAAC,KAAS;AAC/B,yCAAwC,EAAE,qCAAqC,yBAAyB,EAAC;AACzG,8CAA6C,EAAE,qCAAqC,8BAA8B,EAAC;AACnH,kDAAiD,EAAE,qCAAqC,kCAAkC,EAAC;AAC3H,4CAA2C,EAAE,qCAAqC,4BAA4B,EAAC;AAC/G,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS;AACtB;AACA,wDAAwD,SAAS;AACjE,kBAAkB,SAAS,EAAE,UAAU,EAAE,KAAK;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,IAAI;AACjB,kBAAkB,UAAU,IAAI,UAAU;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,IAAI;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,IAAI;AACjB,kBAAkB,UAAU,EAAE,QAAQ,IAAI,UAAU;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,IAAI;AACjB,kBAAkB,WAAW;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,IAAI;AACjB,uCAAuC,WAAW;AAClD,uBAAuB,OAAO;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,IAAI;AACjB,wBAAwB,YAAY;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,IAAI;AACjB,kBAAkB,WAAW;AAC7B;AACA;AACA,kBAAkB,UAAU;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2EAA2E;AAC3E;AACA;AACA;AACA;AACA,iBAAiB,qCAAqC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,eAAe;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,eAAe;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,iBAAiB;AACjC,sBAAsB,SAAS,EAAE,KAAK,GAAG,OAAO,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,cAAc,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,OAAO,WAAW,UAAU,GAAG,UAAU;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,WAAW;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA,sBAAsB;AACtB;AACA,0CAA0C,kBAAkB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,IAAI;AAC/D,gDAAgD,IAAI,GAAG,EAAE;AACzD;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,IAAI;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,SAAS,KAAK,WAAW;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,QAAQ,QAAQ,GAAG,QAAQ,YAAY;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gGAAgG,OAAO;AACvG;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA,qFAAqF,QAAQ,EAAE,IAAI,EAAE,OAAO;AAC5G;AACA;AACA,4DAA4D,EAAE;AAC9D;AACA;;;;;;;;ACxrBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,GAAG,sBAAsB,GAAG,aAAa,GAAG,gBAAgB,GAAG,sBAAsB;AACvG,eAAe,mBAAO,CAAC,KAAQ;AAC/B;AACA;AACA,qCAAqC,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,qBAAqB,sBAAsB,sBAAsB;AAClE,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,kBAAkB,mBAAmB,IAAI;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,OAAO,EAAE,WAAW;AACtC;AACA;AACA;AACA;AACA,gDAAgD,OAAO;AACvD;AACA,wCAAwC,kBAAkB;AAC1D;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,sBAAsB,qBAAqB;AAC3C;AACA,2CAA2C,0BAA0B,GAAG,UAAU;AAClF;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,SAAS;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,6BAA6B;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,KAAK;AACvD,oCAAoC,UAAU,EAAE,eAAe;AAC/D,SAAS;AACT;AACA;AACA;AACA;AACA,kDAAkD,KAAK;AACvD;AACA,SAAS;AACT;AACA,oDAAoD;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,KAAK,EAAE,KAAK,EAAE,MAAM,IAAI,GAAG,EAAE,aAAa;AACtF;AACA;AACA,4CAA4C,KAAK,EAAE,EAAE,EAAE,aAAa;AACpE;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,kBAAkB;AAClB;;;;;;;;AC9Ia;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB,GAAG,wBAAwB,GAAG,wBAAwB,GAAG,mBAAmB,GAAG,yBAAyB,GAAG,oBAAoB;AACnJ,kBAAkB,mBAAO,CAAC,KAAW;AACrC,eAAe,mBAAO,CAAC,KAAQ;AAC/B,gBAAgB,mBAAO,CAAC,IAAS;AACjC,oBAAoB;AACpB,gBAAgB,SAAS,sCAAsC,QAAQ;AACvE;AACA,yBAAyB;AACzB,gBAAgB,qBAAqB;AACrC,iCAAiC,QAAQ,oBAAoB,YAAY;AACzE,iCAAiC,QAAQ;AACzC;AACA;AACA,YAAY,KAAK;AACjB,YAAY,gCAAgC;AAC5C;AACA;AACA;AACA;AACA;AACA,8CAA8C,OAAO;AACrD;AACA;AACA,mBAAmB;AACnB;AACA,YAAY,KAAK;AACjB,YAAY,gCAAgC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,+BAA+B,yBAAyB,wEAAwE,wBAAwB;AACxJ;AACA,wBAAwB;AACxB,wBAAwB,iDAAiD;AACzE;AACA;AACA;AACA;AACA;AACA,2CAA2C,wBAAwB,GAAG,EAAE;AACxE,mCAAmC,IAAI,mEAAmE,IAAI;AAC9G,uCAAuC,IAAI,oCAAoC,iBAAiB,GAAG,QAAQ;AAC3G;AACA,2CAA2C,IAAI;AAC/C,2CAA2C,IAAI;AAC/C;AACA,KAAK;AACL;AACA,oBAAoB;AACpB;AACA;AACA,+BAA+B,yBAAyB,0EAA0E,IAAI,yBAAyB,wBAAwB,QAAQ,IAAI;AACnM,iCAAiC,uBAAuB;AACxD;AACA;AACA,YAAY,+BAA+B;AAC3C;AACA,0CAA0C,mBAAmB,GAAG,KAAK;AACrE;AACA;AACA,uCAAuC,aAAa;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,eAAe;AAC3B;AACA,mCAAmC;AACnC;AACA;AACA,gDAAgD;AAChD,YAAY,UAAU;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,WAAW,IAAI,cAAc;AAC1D;AACA,gCAAgC,UAAU,EAAE,wDAAwD;AACpG;AACA;AACA;AACA,2BAA2B,eAAe,iBAAiB,IAAI,0BAA0B;AACzF,uEAAuE,cAAc,GAAG,QAAQ;AAChG;AACA,wCAAwC,QAAQ,EAAE,sDAAsD;AACxG;AACA;AACA;AACA,gCAAgC,iBAAiB;AACjD,YAAY,iCAAiC;AAC7C,YAAY,+CAA+C;AAC3D,8HAA8H;AAC9H;AACA;AACA;AACA;AACA,qFAAqF,aAAa,EAAE,WAAW;AAC/G;AACA;AACA;AACA;AACA;;;;;;;;AC1Ha;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB,GAAG,0BAA0B,GAAG,kBAAkB,GAAG,qBAAqB,GAAG,iBAAiB;AACnH,kBAAkB,mBAAO,CAAC,KAAW;AACrC,2BAA2B,mBAAO,CAAC,KAA6B;AAChE,gBAAgB,mBAAO,CAAC,IAAS;AACjC,kBAAkB,mBAAO,CAAC,KAAW;AACrC,eAAe,mBAAO,CAAC,KAAQ;AAC/B,mBAAmB,mBAAO,CAAC,KAAY;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uFAAuF;AACvF,YAAY,aAAa;AACzB,YAAY,gBAAgB;AAC5B,oDAAoD,2BAA2B;AAC/E;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,gBAAgB,iBAAiB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,qCAAqC,SAAS,aAAa;AACnF;AACA;AACA;AACA;AACA,6CAA6C,qBAAqB,MAAM,sBAAsB;AAC9F;AACA,yCAAyC,eAAe;AACxD;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA,oBAAoB,eAAe;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2FAA2F;AAC3F,gBAAgB,WAAW;AAC3B;AACA,mCAAmC,gCAAgC;AACnE;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,SAAS;AACzB,gBAAgB,WAAW;AAC3B;AACA;AACA;AACA,+BAA+B,gCAAgC;AAC/D;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,sBAAsB;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,WAAW;AACvB,iCAAiC,gCAAgC;AACjE;AACA;AACA;AACA;AACA;;;;;;;;ACjPa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAW;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;;AC3Ba;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAW;AACrC;AACA;AACA,gDAAgD,KAAK,UAAU,OAAO;AACtE;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;;ACXa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB,GAAG,kBAAkB,GAAG,mBAAmB,GAAG,oBAAoB,GAAG,mBAAmB,GAAG,iBAAiB;AACjI,eAAe,mBAAO,CAAC,KAAQ;AAC/B,cAAc,mBAAO,CAAC,KAAiB;AACvC,iBAAiB,mBAAO,CAAC,KAAsB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,YAAY,wBAAwB;AACpC;AACA,sBAAsB;AACtB;AACA;AACA;AACA,uBAAuB,eAAe;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uDAAuD,OAAO;AAC9D,sCAAsC,OAAO;AAC7C;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,IAAI;AAC3C;AACA;AACA,qBAAqB;AACrB;;;;;;;;AC1Ja;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB,GAAG,kBAAkB;AACrC;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,kBAAkB,2BAA2B;AAC7C,kBAAkB,2BAA2B;AAC7C,iBAAiB,0BAA0B;AAC3C,kBAAkB,2BAA2B;AAC7C;AACA;AACA,iBAAiB,qDAAqD;AACtE,kBAAkB,WAAW;AAC7B,gBAAgB,WAAW;AAC3B,eAAe;AACf,oBAAoB;AACpB;AACA;AACA,gBAAgB;AAChB;;;;;;;;ACzBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB,GAAG,oBAAoB,GAAG,YAAY,GAAG,eAAe,GAAG,oBAAoB,GAAG,4BAA4B,GAAG,sBAAsB,GAAG,gBAAgB,GAAG,2BAA2B,GAAG,yBAAyB,GAAG,sBAAsB,GAAG,wBAAwB,GAAG,sBAAsB,GAAG,4BAA4B,GAAG,sBAAsB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,cAAc;AACzb,kBAAkB,mBAAO,CAAC,KAAW;AACrC,eAAe,mBAAO,CAAC,KAAgB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,YAAY,aAAa;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,IAAI;AACzD;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B,0BAA0B,0BAA0B;AACpD;AACA;AACA;AACA;AACA,uCAAuC,OAAO;AAC9C;AACA,+BAA+B,aAAa,EAAE,WAAW,EAAE,oCAAoC;AAC/F;AACA,sBAAsB;AACtB;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,kBAAkB,IAAI;AACtB;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,8BAA8B,qDAAqD;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA,kEAAkE,IAAI,cAAc,MAAM;AAC1F,uCAAuC,MAAM,gFAAgF,IAAI,KAAK,0CAA0C,GAAG,IAAI,KAAK;AAC5L,SAAS;AACT,mEAAmE,IAAI;AACvE;AACA;AACA;AACA;AACA,mDAAmD,IAAI,KAAK;AAC5D;AACA;AACA,SAAS;AACT,6DAA6D,gBAAgB;AAC7E;AACA,KAAK;AACL;AACA,kEAAkE,IAAI,cAAc,MAAM,yDAAyD,MAAM,oBAAoB,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,KAAK;AAC5M,mEAAmE,IAAI,2EAA2E,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,KAAK;AACjL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,uDAAuD;AACvD;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA,kEAAkE,MAAM,EAAE,8BAA8B;AACxG;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,eAAe;AACf;AACA;AACA;AACA;AACA,CAAC,WAAW,YAAY,YAAY;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,UAAU;AACtD,6CAA6C,UAAU;AACvD;AACA,4CAA4C,SAAS;AACrD,4CAA4C,SAAS,6CAA6C;AAClG;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA,0BAA0B,IAAI;AAC9B;AACA;AACA;AACA;AACA,uBAAuB;AACvB;;;;;;;;ACjLa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB,GAAG,sBAAsB,GAAG,6BAA6B;AAC9E,iCAAiC,cAAc;AAC/C;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;;;;;;;;AClBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB,GAAG,4BAA4B;AACxD,iBAAiB,mBAAO,CAAC,KAAW;AACpC,kBAAkB,mBAAO,CAAC,KAAY;AACtC,gBAAgB,mBAAO,CAAC,IAAU;AAClC;AACA;AACA;AACA;AACA,YAAY,4BAA4B;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,aAAa;AACpD;AACA;AACA;AACA,4BAA4B;AAC5B;AACA,YAAY,cAAc;AAC1B;AACA,+BAA+B;AAC/B;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA,yBAAyB;AACzB;AACA,YAAY,YAAY;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;;;;;;;;ACjDa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB,GAAG,sBAAsB,GAAG,qBAAqB,GAAG,8BAA8B,GAAG,oBAAoB,GAAG,sBAAsB,GAAG,gBAAgB;AAC5K,gBAAgB,mBAAO,CAAC,KAAU;AAClC,wBAAwB,mBAAO,CAAC,KAAiB;AACjD,iBAAiB,mBAAO,CAAC,KAAW;AACpC,kBAAkB,mBAAO,CAAC,KAAY;AACtC,eAAe,mBAAO,CAAC,KAAS;AAChC;AACA;AACA;AACA;AACA,CAAC,eAAe,gBAAgB,gBAAgB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA,YAAY,kBAAkB;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,kBAAkB;AAC9B,oEAAoE,KAAK;AACzE;AACA;AACA,mCAAmC,UAAU,+BAA+B,KAAK,OAAO,KAAK;AAC7F,8CAA8C,KAAK;AACnD,yDAAyD,KAAK;AAC9D;AACA;AACA,+BAA+B,SAAS;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,SAAS;AACxC;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,gDAAgD,UAAU,iBAAiB,UAAU;AACrF,8DAA8D,KAAK;AACnE,gDAAgD,MAAM;AACtD;AACA;AACA;AACA;AACA,gDAAgD,UAAU,kBAAkB,MAAM;AAClF,oBAAoB,UAAU,iBAAiB,MAAM,KAAK,MAAM,MAAM,KAAK;AAC3E,0DAA0D,KAAK;AAC/D;AACA;AACA;AACA,gDAAgD,UAAU,mBAAmB,MAAM;AACnF,oBAAoB,UAAU,kBAAkB,MAAM,KAAK,MAAM,MAAM,MAAM,OAAO,MAAM;AAC1F,0DAA0D,KAAK;AAC/D;AACA;AACA;AACA,gDAAgD,MAAM,iBAAiB,MAAM,WAAW,MAAM;AAC9F;AACA,gDAAgD,MAAM,gBAAgB,MAAM;AAC5E;AACA;AACA;AACA,+CAA+C,MAAM,YAAY,MAAM,WAAW,MAAM;AACxF;AACA;AACA;AACA;AACA,gDAAgD,UAAU,kBAAkB,UAAU;AACtF,mBAAmB,UAAU,mBAAmB,MAAM;AACtD,0DAA0D,KAAK;AAC/D;AACA;AACA;AACA,4BAA4B,qCAAqC;AACjE;AACA,+BAA+B,YAAY,qDAAqD,WAAW,GAAG,mBAAmB;AACjI;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,MAAM,EAAE,IAAI;AACnD;AACA,qDAAqD,KAAK;AAC1D;AACA;AACA,uCAAuC,MAAM,YAAY,MAAM,gCAAgC,KAAK;AACpG;AACA;AACA,iDAAiD,MAAM,iBAAiB,KAAK;AAC7E;AACA;AACA;AACA;AACA;AACA,8CAA8C,MAAM,EAAE,IAAI,EAAE,SAAS;AACrE;AACA;AACA;AACA,6DAA6D,MAAM,+DAA+D,KAAK;AACvI;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,MAAM;AACxD,0DAA0D,MAAM,KAAK,OAAO;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA,gBAAgB,QAAQ,gBAAgB,OAAO;AAC/C,eAAe,qBAAqB,oDAAoD,QAAQ,QAAQ,uBAAuB,QAAQ,aAAa;AACpJ;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,YAAY,oBAAoB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;;;;;;;;AC1Ma;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,sBAAsB;AACtB,kBAAkB,mBAAO,CAAC,KAAY;AACtC,eAAe,mBAAO,CAAC,KAAS;AAChC;AACA,YAAY,oBAAoB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA,YAAY,iCAAiC;AAC7C;AACA;AACA,0CAA0C,KAAK,EAAE,iCAAiC;AAClF;AACA,mEAAmE,UAAU;AAC7E;AACA;AACA,wCAAwC,WAAW;AACnD;AACA,wCAAwC,WAAW,KAAK,WAAW,cAAc,WAAW;AAC5F;AACA,UAAU,WAAW;AACrB,8CAA8C,WAAW,cAAc,WAAW;AAClF,0CAA0C,WAAW,IAAI,uCAAuC;AAChG;AACA;;;;;;;;AClCa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,GAAG,kBAAkB,GAAG,4BAA4B;AACnE,qBAAqB,mBAAO,CAAC,KAAc;AAC3C,mBAAmB,mBAAO,CAAC,KAAY;AACvC,wBAAwB,mBAAO,CAAC,KAAiB;AACjD,mBAAmB,mBAAO,CAAC,KAAY;AACvC,mBAAmB,mBAAO,CAAC,IAAY;AACvC,kBAAkB,mBAAO,CAAC,KAAW;AACrC,oBAAoB,mBAAO,CAAC,KAAa;AACzC,kBAAkB,mBAAO,CAAC,KAAY;AACtC,gBAAgB,mBAAO,CAAC,IAAU;AAClC,kBAAkB,mBAAO,CAAC,KAAY;AACtC,eAAe,mBAAO,CAAC,KAAS;AAChC,iBAAiB,mBAAO,CAAC,KAAW;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B,4BAA4B,4CAA4C;AACxE;AACA,mDAAmD,qBAAqB,IAAI,uBAAuB;AACnG,qDAAqD,EAAE,4BAA4B;AACnF;AACA;AACA,SAAS;AACT;AACA;AACA,mDAAmD,qBAAqB,IAAI,wBAAwB;AACpG;AACA;AACA;AACA,8BAA8B,EAAE,6BAA6B,OAAO,2BAA2B,IAAI,mCAAmC,IAAI,yBAAyB,GAAG,qBAAqB,EAAE,wCAAwC,+BAA+B,GAAG,mBAAmB,GAAG;AAC7R;AACA;AACA;AACA,kEAAkE,uBAAuB,GAAG,6BAA6B;AACzH,gEAAgE,uBAAuB,GAAG,2BAA2B;AACrH,wEAAwE,uBAAuB,GAAG,mCAAmC;AACrI,8DAA8D,uBAAuB,GAAG,yBAAyB;AACjH;AACA,wEAAwE,uBAAuB,GAAG,+BAA+B;AACjI,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,wEAAwE;AACxE,KAAK;AACL;AACA;AACA,YAAY,oBAAoB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,YAAY,oBAAoB;AAChC,8DAA8D,aAAa;AAC3E,+BAA+B,aAAa,qDAAqD,aAAa;AAC9G,+BAA+B,aAAa,qDAAqD,aAAa;AAC9G;AACA;AACA;AACA,gGAAgG,OAAO;AACvG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,cAAc;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,oBAAoB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,WAAW,MAAM,uBAAuB;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,oCAAoC;AAChD;AACA,sEAAsE,cAAc;AACpF;AACA;AACA;AACA,YAAY,eAAe;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,6CAA6C;AACvE;AACA;AACA,qCAAqC,qBAAqB,cAAc,IAAI;AAC5E;AACA;AACA,iDAAiD,cAAc;AAC/D,kDAAkD,qBAAqB;AACvE,qCAAqC,qBAAqB,iBAAiB,IAAI,IAAI,WAAW,IAAI,SAAS;AAC3G;AACA;AACA;AACA,YAAY,sDAAsD;AAClE;AACA;AACA,mCAAmC,wBAAwB,wFAAwF,gBAAgB,GAAG,wBAAwB;AAC9L;AACA;AACA,uCAAuC,aAAa;AACpD;AACA;AACA,uCAAuC,wBAAwB;AAC/D;AACA;AACA,2BAA2B,8BAA8B;AACzD;AACA,uCAAuC,UAAU;AACjD;AACA,uCAAuC,UAAU;AACjD;AACA;AACA,YAAY,2CAA2C;AACvD,YAAY,QAAQ;AACpB;AACA,6EAA6E;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,wBAAwB,MAAM,eAAe;AACpF;AACA;AACA;AACA,YAAY,qBAAqB,aAAa,IAAI;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,EAAE,4BAA4B,uBAAuB;AAC/F;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,OAAO;AAC3B;AACA,sDAAsD,eAAe,iBAAiB,QAAQ;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,WAAW;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,SAAS,gBAAgB,+BAA+B;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,aAAa;AAC7B,sCAAsC,YAAY,oBAAoB,kDAAkD;AACxH;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,gBAAgB,mCAAmC;AACnD,qDAAqD,YAAY;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,uCAAuC;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,iGAAiG;AAC5I;AACA;AACA;AACA;AACA;AACA,4EAA4E,yBAAyB,GAAG;AACxG,4CAA4C,kBAAkB,GAAG,WAAW;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA,gBAAgB,UAAU;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,UAAU;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,mCAAmC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,MAAM;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,MAAM;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,KAAK,EAAE,qEAAqE;AACnH,uCAAuC,MAAM,KAAK,KAAK;AACvD;AACA;AACA;AACA;AACA,gCAAgC,aAAa,EAAE,IAAI,8BAA8B,UAAU;AAC3F;AACA;AACA,eAAe;AACf;;;;;;;;ACvgBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,4BAA4B,GAAG,uBAAuB,GAAG,uBAAuB,GAAG,wBAAwB;AAC3G,kBAAkB,mBAAO,CAAC,KAAY;AACtC,gBAAgB,mBAAO,CAAC,IAAU;AAClC,eAAe,mBAAO,CAAC,KAAyB;AAChD,iBAAiB,mBAAO,CAAC,KAAW;AACpC;AACA,YAAY,yCAAyC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,iBAAiB,GAAG,QAAQ;AACtD;AACA;AACA,KAAK;AACL;AACA;AACA,wBAAwB;AACxB;AACA;AACA,YAAY,gDAAgD;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qHAAqH,GAAG,aAAa,mBAAmB,kDAAkD,EAAE;AAC5M;AACA;AACA;AACA,iDAAiD,YAAY;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,OAAO,EAAE,oEAAoE;AAC3H;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,YAAY,gBAAgB;AAC5B,qEAAqE,cAAc,GAAG,sBAAsB;AAC5G;AACA;AACA,YAAY,MAAM;AAClB,6CAA6C,KAAK;AAClD;AACA,iEAAiE,yBAAyB,aAAa,MAAM,IAAI,wBAAwB,UAAU,KAAK;AACxJ,gEAAgE,wBAAwB;AACxF;AACA,KAAK;AACL;AACA,6BAA6B,WAAW;AACxC;AACA;AACA;AACA;AACA;AACA,oCAAoC,QAAQ;AAC5C,qEAAqE,cAAc,IAAI,qDAAqD;AAC5I;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB,gCAAgC,mCAAmC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA,mEAAmE,QAAQ,IAAI,eAAe;AAC9F;AACA;AACA;AACA;AACA,oCAAoC,QAAQ,8BAA8B,cAAc;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;;;;;;;;AC3Ha;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B,GAAG,2BAA2B,GAAG,oBAAoB;AAChF,kBAAkB,mBAAO,CAAC,KAAY;AACtC,eAAe,mBAAO,CAAC,KAAS;AAChC,4BAA4B,sEAAsE;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,cAAc,EAAE,oCAAoC;AACpG,kCAAkC,iBAAiB,GAAG,QAAQ;AAC9D;AACA;AACA;AACA,gDAAgD,cAAc,EAAE,oCAAoC,EAAE,uCAAuC;AAC7I,kCAAkC,iBAAiB,GAAG,QAAQ,GAAG,uCAAuC;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB,8CAA8C,+DAA+D;AAC7G;AACA;AACA;AACA,YAAY,MAAM;AAClB;AACA,gBAAgB,+BAA+B;AAC/C,6DAA6D,QAAQ,EAAE,qCAAqC;AAC5G;AACA,oDAAoD,UAAU,EAAE,kEAAkE;AAClI,2DAA2D,SAAS;AACpE;AACA;AACA;AACA,8FAA8F;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,0CAA0C,uEAAuE;AACjH;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD;AACnD,yCAAyC;AACzC;AACA,2BAA2B;AAC3B;;;;;;;;AChFa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,GAAG,YAAY,GAAG,WAAW,GAAG,iBAAiB,GAAG,WAAW,GAAG,SAAS,GAAG,kBAAkB;AAC/G,iBAAiB,mBAAO,CAAC,KAAoB;AAC7C,8CAA6C,EAAE,qCAAqC,iCAAiC,EAAC;AACtH,gBAAgB,mBAAO,CAAC,KAAmB;AAC3C,qCAAoC,EAAE,qCAAqC,uBAAuB,EAAC;AACnG,uCAAsC,EAAE,qCAAqC,yBAAyB,EAAC;AACvG,6CAA4C,EAAE,qCAAqC,+BAA+B,EAAC;AACnH,uCAAsC,EAAE,qCAAqC,yBAAyB,EAAC;AACvG,wCAAuC,EAAE,qCAAqC,0BAA0B,EAAC;AACzG,2CAA0C,EAAE,qCAAqC,6BAA6B,EAAC;AAC/G,2BAA2B,mBAAO,CAAC,KAA4B;AAC/D,oBAAoB,mBAAO,CAAC,KAAqB;AACjD,gBAAgB,mBAAO,CAAC,KAAiB;AACzC,kBAAkB,mBAAO,CAAC,KAAW;AACrC,kBAAkB,mBAAO,CAAC,KAAmB;AAC7C,kBAAkB,mBAAO,CAAC,KAAmB;AAC7C,mBAAmB,mBAAO,CAAC,KAA6B;AACxD,eAAe,mBAAO,CAAC,KAAgB;AACvC,uBAAuB,mBAAO,CAAC,KAAkB;AACjD,cAAc,mBAAO,CAAC,KAAe;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,6CAA6C;AAClF,oCAAoC,aAAa;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,8BAA8B,IAAI,kBAAkB;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,gBAAgB,aAAa;AAC7B,gDAAgD,SAAS,yCAAyC;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,wBAAwB;AACxC;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,iBAAiB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,aAAa;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,aAAa;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,MAAM;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,gCAAgC;AAC/D;AACA,6CAA6C,KAAK,gBAAgB,YAAY;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,WAAW;AAC/B;AACA;AACA,0CAA0C,UAAU;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,WAAW;AAC/B,mDAAmD,UAAU,YAAY;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qCAAqC,KAAK;AAChD;AACA;AACA;AACA;AACA,2BAA2B,QAAQ,EAAE,gBAAgB,EAAE,UAAU;AACjE;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,WAAW;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,2CAA2C;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,GAAG;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAe;AACf;AACA;AACA;AACA;AACA,gCAAgC,IAAI,WAAW,IAAI,IAAI,eAAe;AACtE;AACA;AACA;AACA,iDAAiD;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA,iBAAiB,SAAS,YAAY;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,QAAQ;AACpB;AACA;AACA,uCAAuC,KAAK;AAC5C;AACA,uCAAuC,KAAK;AAC5C,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,QAAQ;AACpB,4DAA4D,SAAS;AACrE;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,QAAQ;AACzC;AACA;AACA;AACA,UAAU,aAAa;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;;;;;;;;ACzmBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D;AACA,cAAc,mBAAO,CAAC,KAAiB;AACvC;AACA,kBAAe;AACf;;;;;;;;ACNa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA,kBAAe;AACf;AACA;;;;;;;;ACvBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,YAAY,mBAAO,CAAC,KAAU;AAC9B;AACA,kBAAe;AACf;;;;;;;;ACLa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;;ACVa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,+BAA+B;AAC/B,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,eAAe,mBAAO,CAAC,KAAoB;AAC3C;AACA,gBAAgB,UAAU,OAAO,mDAAmD,KAAK;AACzF,eAAe,UAAU,OAAO,wBAAwB,SAAS,KAAK;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,mBAAmB;AACnC,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,YAAY,iCAAiC;AAC7C;AACA,qDAAqD,KAAK;AAC1D;AACA,wBAAwB,mBAAmB;AAC3C,qCAAqC,KAAK,KAAK,aAAa;AAC5D;AACA;AACA,2DAA2D,KAAK,KAAK,aAAa,IAAI;AACtF;AACA;AACA;AACA;AACA;AACA,4BAA4B,qDAAqD;AACjF;AACA;AACA,SAAS;AACT;AACA;AACA,+BAA+B;AAC/B,kBAAe;AACf;;;;;;;;AChDa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,mBAAO,CAAC,KAAS;AAChC,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,gBAAgB,mBAAO,CAAC,IAAqB;AAC7C,eAAe,mBAAO,CAAC,KAAoB;AAC3C;AACA;AACA,eAAe,QAAQ,wBAAwB,sBAAsB,2BAA2B;AAChG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,iDAAiD;AACjE;AACA;AACA;AACA,gBAAgB,kBAAkB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,WAAW,MAAM,uBAAuB;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wFAAwF,KAAK,MAAM,EAAE;AACrG;AACA;AACA;AACA;AACA;AACA,wGAAwG,+BAA+B,QAAQ,IAAI;AACnJ;AACA;AACA;AACA;AACA,gDAAgD,KAAK,GAAG,IAAI;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,yBAAyB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;ACzGa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,mBAAO,CAAC,KAAoB;AAC3C;AACA;AACA;AACA;AACA,gBAAgB,kBAAkB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,iCAAiC;AAC5E;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;ACtBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,mBAAO,CAAC,KAAS;AAChC;AACA;AACA;AACA;AACA;AACA,aAAa,yCAAyC;AACtD;AACA,kBAAe;AACf;;;;;;;;ACXa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,eAAe,mBAAO,CAAC,KAAoB;AAC3C;AACA,gBAAgB,UAAU,YAAY;AACtC,sDAAsD,KAAK;AAC3D,sDAAsD,KAAK,mBAAmB,KAAK;AACnF,eAAe,UAAU,YAAY,4CAA4C,eAAe,KAAK,uBAAuB,eAAe,IAAI,iBAAiB,KAAK;AACrK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,sCAAsC;AACtD;AACA;AACA,gBAAgB,2BAA2B;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,KAAK;AAC9D,wBAAwB,UAAU;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,KAAK,KAAK,IAAI;AACzD;AACA,2CAA2C,MAAM,KAAK,KAAK,KAAK,IAAI;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,KAAK;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,aAAa;AACb;AACA;AACA,yCAAyC,MAAM;AAC/C;AACA,2CAA2C,OAAO,KAAK,IAAI;AAC3D;AACA;AACA,2CAA2C,OAAO,IAAI,IAAI;AAC1D;AACA;AACA;AACA,+CAA+C,OAAO,KAAK,IAAI;AAC/D;AACA;AACA,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;AC9Fa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B,GAAG,4BAA4B,GAAG,aAAa;AACzE,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,eAAe,mBAAO,CAAC,KAAoB;AAC3C,eAAe,mBAAO,CAAC,KAAS;AAChC,aAAa;AACb,gBAAgB,UAAU,6BAA6B;AACvD;AACA,+CAA+C,cAAc,EAAE,MAAM,gBAAgB,UAAU;AAC/F,KAAK;AACL,eAAe,UAAU,8CAA8C,wBAAwB,YAAY,SAAS;AACpH,uBAAuB,gBAAgB;AACvC,iBAAiB,UAAU;AAC3B,YAAY,MAAM;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,6BAA6B,QAAQ;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,gBAAgB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,uCAAuC,aAAa,MAAM,iDAAiD;AAC3G;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA,YAAY,yBAAyB;AACrC;AACA;AACA;AACA;AACA;AACA,2CAA2C,2BAA2B;AACtE;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,0BAA0B;AAC1B,kBAAe;AACf;;;;;;;;ACpFa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,eAAe,mBAAO,CAAC,KAAoB;AAC3C;AACA,gBAAgB,QAAQ,uCAAuC,gBAAgB;AAC/E,eAAe,QAAQ,wBAAwB,kBAAkB,iBAAiB;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,wBAAwB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,UAAU;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,+CAA+C,SAAS;AACxD;AACA;AACA;AACA,6DAA6D,QAAQ;AACrE;AACA,oCAAoC,mBAAmB;AACvD;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;;ACjEa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B,mBAAO,CAAC,KAAmB;AACrD,sBAAsB,mBAAO,CAAC,KAAe;AAC7C,gBAAgB,mBAAO,CAAC,KAAS;AACjC,oBAAoB,mBAAO,CAAC,KAAa;AACzC,mBAAmB,mBAAO,CAAC,KAAY;AACvC,uBAAuB,mBAAO,CAAC,KAAgB;AAC/C,wBAAwB,mBAAO,CAAC,KAAiB;AACjD,+BAA+B,mBAAO,CAAC,IAAwB;AAC/D,qBAAqB,mBAAO,CAAC,IAAc;AAC3C,4BAA4B,mBAAO,CAAC,KAAqB;AACzD,cAAc,mBAAO,CAAC,KAAO;AAC7B,gBAAgB,mBAAO,CAAC,KAAS;AACjC,gBAAgB,mBAAO,CAAC,KAAS;AACjC,gBAAgB,mBAAO,CAAC,KAAS;AACjC,aAAa,mBAAO,CAAC,KAAM;AAC3B,mBAAmB,mBAAO,CAAC,KAAY;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;;AC3Ca;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB;AACrB,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,eAAe,mBAAO,CAAC,KAAoB;AAC3C,eAAe,mBAAO,CAAC,KAAS;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,aAAa;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,YAAY,uCAAuC;AACnD;AACA;AACA;AACA;AACA;AACA,qDAAqD,KAAK;AAC1D;AACA;AACA;AACA,mCAAmC,KAAK,IAAI,EAAE;AAC9C;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA,gBAAgB,sBAAsB;AACtC;AACA;AACA;AACA,4BAA4B,QAAQ,OAAO,EAAE,mCAAmC,YAAY,0CAA0C,cAAc;AACpJ;AACA;AACA;AACA;AACA,qBAAqB;AACrB,kBAAe;AACf;;;;;;;;ACnDa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,eAAe,mBAAO,CAAC,KAAoB;AAC3C,eAAe,mBAAO,CAAC,KAAS;AAChC,0BAA0B,mBAAO,CAAC,KAAmB;AACrD;AACA,gBAAgB,UAAU,OAAO,mDAAmD,KAAK;AACzF,eAAe,UAAU,OAAO,wBAAwB,SAAS,KAAK;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,2BAA2B;AAC3C,gBAAgB,cAAc;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;AC7Ba;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,mBAAO,CAAC,KAAoB;AAC3C;AACA;AACA;AACA;AACA;AACA,gBAAgB,kBAAkB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL,aAAa,8BAA8B;AAC3C;AACA,kBAAe;AACf;;;;;;;;ACzBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,eAAe,mBAAO,CAAC,KAAoB;AAC3C;AACA;AACA,eAAe,QAAQ,wBAAwB,kBAAkB,gBAAgB;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,gCAAgC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,SAAS;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,gDAAgD,UAAU,KAAK,MAAM;AACrE;AACA,8DAA8D,QAAQ,IAAI,EAAE;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;AC3Da;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,mBAAO,CAAC,KAAS;AAChC,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,eAAe,mBAAO,CAAC,KAAoB;AAC3C,eAAe,mBAAO,CAAC,KAAoB;AAC3C;AACA;AACA;AACA;AACA;AACA,gBAAgB,sCAAsC;AACtD,gBAAgB,OAAO;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gEAAgE,MAAM,kBAAkB,KAAK;AAC7F;AACA;AACA;AACA;AACA;AACA,2CAA2C,iCAAiC,QAAQ,IAAI;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,uDAAuD,MAAM,GAAG,IAAI;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;AC1Ea;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB,mBAAO,CAAC,KAAS;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;;ACXa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB,mBAAO,CAAC,KAAwB;AACnD,eAAe,mBAAO,CAAC,KAAS;AAChC,eAAe,mBAAO,CAAC,KAAoB;AAC3C,+BAA+B,mBAAO,CAAC,IAAwB;AAC/D;AACA;AACA;AACA;AACA;AACA,gBAAgB,sCAAsC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;ACrDa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,eAAe,mBAAO,CAAC,KAAoB;AAC3C;AACA;AACA,eAAe,QAAQ,wBAAwB,gBAAgB,qBAAqB;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,wBAAwB;AACxC;AACA;AACA;AACA;AACA,4BAA4B,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;ACrCa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,mBAAO,CAAC,KAAoB;AAC3C;AACA;AACA;AACA,WAAW,2BAA2B;AACtC;AACA,gDAAgD,QAAQ;AACxD,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;ACZa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB,GAAG,qBAAqB,GAAG,kBAAkB,GAAG,wBAAwB,GAAG,wBAAwB,GAAG,2BAA2B,GAAG,wBAAwB,GAAG,sBAAsB,GAAG,qBAAqB,GAAG,mBAAmB,GAAG,yBAAyB,GAAG,wBAAwB,GAAG,8BAA8B;AAChV,kBAAkB,mBAAO,CAAC,KAAoB;AAC9C,eAAe,mBAAO,CAAC,KAAiB;AACxC,gBAAgB,mBAAO,CAAC,IAAkB;AAC1C,eAAe,mBAAO,CAAC,KAAiB;AACxC;AACA,YAAY,gBAAgB;AAC5B;AACA,wBAAwB,qCAAqC,KAAK,GAAG;AACrE;AACA,KAAK;AACL;AACA,8BAA8B;AAC9B,4BAA4B,iBAAiB,QAAQ;AACrD,uJAAuJ,SAAS,IAAI,KAAK;AACzK;AACA,wBAAwB;AACxB;AACA,oBAAoB,0BAA0B;AAC9C;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,mBAAmB;AACnB;AACA,+BAA+B,iBAAiB,QAAQ,KAAK,IAAI,SAAS;AAC1E;AACA,qBAAqB;AACrB;AACA,qCAAqC,KAAK,EAAE,sCAAsC;AAClF,+CAA+C,MAAM,KAAK,mCAAmC;AAC7F;AACA,sBAAsB;AACtB;AACA,qCAAqC,KAAK,EAAE,sCAAsC;AAClF;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA,wBAAwB;AACxB,4BAA4B,wBAAwB,0CAA0C,MAAM;AACpG,2DAA2D,WAAW,IAAI,KAAK,IAAI,aAAa,EAAE,WAAW;AAC7G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,cAAc,IAAI,sBAAsB;AAC7E,2DAA2D,KAAK,QAAQ,QAAQ,IAAI,KAAK,yBAAyB,KAAK,GAAG,KAAK;AAC/H;AACA,wBAAwB;AACxB;AACA,sBAAsB,WAAW,QAAQ;AACzC;AACA,YAAY,SAAS;AACrB;AACA;AACA;AACA;AACA,kCAAkC,4EAA4E,GAAG,QAAQ,IAAI,EAAE;AAC/H,KAAK;AACL;AACA,kBAAkB;AAClB;AACA,YAAY,yBAAyB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,KAAK;AAC9D;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA,qBAAqB;AACrB;AACA,YAAY,2BAA2B;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,8CAA8C,OAAO,KAAK,SAAS;AACnE;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,qBAAqB;AACrB;;;;;;;;AClIa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;ACTa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,aAAa,mBAAO,CAAC,KAAM;AAC3B,cAAc,mBAAO,CAAC,KAAO;AAC7B;AACA;AACA;AACA;AACA;AACA,MAAM,qBAAqB;AAC3B;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;;ACfa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,GAAG,mBAAmB;AACrC,oBAAoB,mBAAO,CAAC,KAAyB;AACrD,eAAe,mBAAO,CAAC,KAAS;AAChC,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,gBAAgB,mBAAO,CAAC,IAAqB;AAC7C,kBAAkB,mBAAO,CAAC,KAAe;AACzC,eAAe,mBAAO,CAAC,KAAoB;AAC3C;AACA;AACA;AACA;AACA,gBAAgB,wBAAwB;AACxC,gBAAgB,mDAAmD;AACnE,gBAAgB,OAAO;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,WAAW;AACjE,oDAAoD,SAAS;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,mFAAmF,gDAAgD,IAAI,UAAU;AACjJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,YAAY,MAAM;AAClB;AACA,uCAAuC,mBAAmB;AAC1D,8BAA8B,4BAA4B,UAAU,EAAE;AACtE;AACA,mBAAmB;AACnB;AACA,YAAY,UAAU;AACtB,YAAY,kCAAkC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,8CAA8C;AAC7F,iCAAiC;AACjC;AACA;AACA,SAAS;AACT,yCAAyC,GAAG,aAAa,mBAAmB;AAC5E;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,OAAO;AAChD,gEAAgE,yBAAyB,aAAa,MAAM,IAAI,wBAAwB,UAAU,KAAK,KAAK;AAC5J,+DAA+D,wBAAwB;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mEAAmE,OAAO;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mEAAmE,OAAO;AAC1E;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,kBAAe;AACf;;;;;;;;ACzHa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,gBAAgB,mBAAO,CAAC,IAAwB;AAChD,kBAAkB,mBAAO,CAAC,KAAe;AACzC,oBAAoB,mBAAO,CAAC,KAAyB;AACrD,eAAe,mBAAO,CAAC,KAAoB;AAC3C;AACA,gBAAgB,UAAU,uBAAuB;AACjD,kBAAkB,QAAQ;AAC1B,2BAA2B,QAAQ;AACnC,eAAe,UAAU,4BAA4B,wBAAwB,SAAS,WAAW,SAAS,QAAQ,cAAc,KAAK;AACrI;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,sCAAsC;AACtD,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,KAAK,EAAE,oCAAoC;AACpG,0CAA0C,KAAK,gEAAgE,kDAAkD;AACjK;AACA;AACA;AACA;AACA;AACA,+CAA+C,KAAK,MAAM,SAAS;AACnE;AACA;AACA;AACA,+BAA+B,sDAAsD;AACrF;AACA;AACA;AACA;AACA,2CAA2C,8BAA8B;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,kBAAkB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qHAAqH,QAAQ;AAC7H;AACA;AACA;AACA;AACA;AACA,mDAAmD,QAAQ;AAC3D;AACA,mCAAmC,UAAU;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,QAAQ;AAC1E;AACA;AACA;AACA;AACA,uDAAuD,QAAQ;AAC/D;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;ACvGa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB;AACA;AACA;AACA;AACA,CAAC,iBAAiB,kBAAkB,kBAAkB;AACtD;;;;;;;;ACRa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,mBAAO,CAAC,KAAQ;AAC/B,qBAAqB,mBAAO,CAAC,KAAc;AAC3C,qBAAqB,mBAAO,CAAC,KAAc;AAC3C,iBAAiB,mBAAO,CAAC,IAAU;AACnC,mBAAmB,mBAAO,CAAC,KAAY;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;;AChBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD;AACA,gBAAgB,YAAY,8CAA8C,WAAW;AACrF,eAAe,YAAY,wBAAwB,UAAU,YAAY;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,2CAA2C;AAC3D,gBAAgB,uCAAuC;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,+DAA+D,KAAK,GAAG,WAAW;AAClF;AACA;AACA;AACA,8CAA8C,MAAM,mBAAmB,MAAM,iEAAiE,KAAK,uDAAuD,KAAK;AAC/M;AACA;AACA;AACA;AACA,2CAA2C,YAAY,MAAM,OAAO;AACpE;AACA;AACA;AACA,2CAA2C,KAAK,iBAAiB,OAAO,GAAG,KAAK,MAAM,OAAO,GAAG,KAAK;AACrG,0CAA0C,OAAO,GAAG,KAAK;AACzD,8DAA8D,QAAQ,kBAAkB,YAAY,IAAI,OAAO,QAAQ,KAAK;AAC5H,2CAA2C,QAAQ,KAAK,QAAQ,cAAc,OAAO,MAAM,UAAU,MAAM,UAAU;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,OAAO,+BAA+B,cAAc;AAClG;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,kBAAkB,EAAE,mCAAmC;AACrG;AACA,wDAAwD,gCAAgC;AACxF;AACA,0FAA0F,IAAI;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,OAAO,GAAG,KAAK;AACpE;AACA,yEAAyE,OAAO,GAAG,KAAK,yBAAyB,OAAO,QAAQ,KAAK;AACrI;AACA;AACA,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;AC3Fa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB,mBAAO,CAAC,KAAU;AACnC;AACA,kBAAe;AACf;;;;;;;;ACLa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB,GAAG,0BAA0B;AACtD,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;;;;;;;;ACjBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,eAAe,mBAAO,CAAC,KAAoB;AAC3C,gBAAgB,mBAAO,CAAC,KAAqB;AAC7C;AACA;AACA,eAAe,YAAY,wBAAwB,gBAAgB,YAAY;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,uCAAuC;AACvD;AACA,+CAA+C,0CAA0C,GAAG,KAAK,IAAI,WAAW;AAChH;AACA;AACA,yCAAyC,QAAQ,MAAM,KAAK;AAC5D;AACA,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;ACxBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,eAAe,mBAAO,CAAC,KAAoB;AAC3C,gBAAgB,mBAAO,CAAC,KAAqB;AAC7C;AACA;AACA,eAAe,YAAY,wBAAwB,iBAAiB,YAAY;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,2CAA2C;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,SAAS,GAAG,KAAK,IAAI,EAAE;AAChG;AACA;AACA;AACA;AACA,sCAAsC,SAAS,GAAG,KAAK,IAAI,QAAQ,GAAG,EAAE;AACxE,sCAAsC,MAAM,MAAM,IAAI;AACtD;AACA,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;AC/Ca;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,sBAAsB,mBAAO,CAAC,KAAe;AAC7C,qBAAqB,mBAAO,CAAC,KAAc;AAC3C,sBAAsB,mBAAO,CAAC,KAAe;AAC7C,kBAAkB,mBAAO,CAAC,KAAW;AACrC,0BAA0B,mBAAO,CAAC,KAAmB;AACrD,mBAAmB,mBAAO,CAAC,KAAY;AACvC,qBAAqB,mBAAO,CAAC,KAAc;AAC3C,sBAAsB,mBAAO,CAAC,KAAe;AAC7C,gBAAgB,mBAAO,CAAC,KAAS;AACjC,eAAe,mBAAO,CAAC,IAAQ;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,kDAAkD;AACxD,MAAM,4CAA4C;AAClD;AACA;AACA;AACA,kBAAe;AACf;;;;;;;;AChCa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD;AACA,cAAc,qBAAqB;AACnC;AACA,mDAAmD,MAAM,OAAO,YAAY;AAC5E,KAAK;AACL,eAAe,YAAY,wBAAwB,SAAS,YAAY;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,4BAA4B;AAC5C;AACA,0CAA0C,KAAK,UAAU,IAAI,EAAE,WAAW;AAC1E,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;ACvBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,eAAe,mBAAO,CAAC,KAAoB;AAC3C,qBAAqB,mBAAO,CAAC,KAA0B;AACvD;AACA,cAAc,qBAAqB;AACnC;AACA,mDAAmD,MAAM,OAAO,YAAY;AAC5E,KAAK;AACL,eAAe,YAAY,wBAAwB,SAAS,YAAY;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,gCAAgC;AAChD;AACA,oEAAoE,KAAK,+BAA+B,mDAAmD,GAAG,KAAK;AACnK,0CAA0C,KAAK,EAAE,IAAI,EAAE,WAAW;AAClE,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;AC1Ba;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD;AACA;AACA,eAAe,wCAAwC;AACvD,eAAe,wCAAwC;AACvD,wBAAwB,uCAAuC;AAC/D,wBAAwB,uCAAuC;AAC/D;AACA;AACA,gBAAgB,qBAAqB,mCAAmC,qBAAqB,EAAE,WAAW;AAC1G,eAAe,qBAAqB,wBAAwB,cAAc,oBAAoB,WAAW,YAAY;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,4BAA4B;AAC5C,0CAA0C,MAAM,EAAE,oBAAoB,EAAE,YAAY,WAAW,KAAK;AACpG,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;AC1Ba;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD;AACA,cAAc,qBAAqB;AACnC;AACA,mDAAmD,MAAM,OAAO,YAAY;AAC5E,KAAK;AACL,eAAe,YAAY,wBAAwB,SAAS,YAAY;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,4BAA4B;AAC5C;AACA,sDAAsD,KAAK,WAAW,IAAI,EAAE,WAAW;AACvF,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;ACvBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD;AACA,gBAAgB,YAAY,+CAA+C,WAAW;AACtF,eAAe,YAAY,wBAAwB,cAAc,YAAY;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,4BAA4B;AAC5C;AACA;AACA;AACA;AACA,sDAAsD,IAAI,MAAM,IAAI,SAAS,KAAK;AAClF,kCAAkC,KAAK,eAAe,IAAI;AAC1D,2CAA2C,YAAY,YAAY,KAAK,IAAI,KAAK,GAAG,WAAW,IAAI,QAAQ;AAC3G,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;ACzBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,mBAAO,CAAC,KAAS;AAChC,kBAAkB,mBAAO,CAAC,KAAuB;AACjD;AACA,gBAAgB,YAAY,+CAA+C,WAAW;AACtF,eAAe,YAAY,wBAAwB,WAAW,YAAY;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,sCAAsC;AACtD;AACA;AACA,+DAA+D,WAAW,IAAI,EAAE;AAChF,2CAA2C,OAAO,QAAQ,KAAK;AAC/D,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;ACvBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,mBAAO,CAAC,KAAS;AAChC,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,eAAe,mBAAO,CAAC,KAAoB;AAC3C;AACA,gBAAgB,UAAU,mBAAmB,wDAAwD,gBAAgB;AACrH,eAAe,UAAU,mBAAmB,wBAAwB,mBAAmB,iBAAiB;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,2CAA2C;AAC3D,gBAAgB,OAAO;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,oBAAoB;AACxC;AACA;AACA;AACA,sDAAsD,YAAY,uBAAuB,WAAW;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,uBAAuB;AACvD;AACA,aAAa;AACb;AACA;AACA,4BAA4B,0BAA0B;AACtD;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;;AC9Ea;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB,mBAAO,CAAC,KAAiC;AAC5D,kBAAkB,mBAAO,CAAC,KAAuB;AACjD,eAAe,mBAAO,CAAC,KAAoB;AAC3C,gBAAgB,mBAAO,CAAC,KAAqB;AAC7C;AACA,gBAAgB,UAAU,QAAQ,mEAAmE,GAAG,MAAM,GAAG;AACjH,eAAe,UAAU,QAAQ,wBAAwB,KAAK,EAAE,OAAO,GAAG;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,yDAAyD;AACzE;AACA;AACA;AACA;AACA,uEAAuE,YAAY;AACnF;AACA;AACA,uDAAuD,KAAK;AAC5D;AACA,4BAA4B,MAAM;AAClC;AACA,uCAAuC,GAAG;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE;AACrE,uCAAuC,EAAE,EAAE,GAAG;AAC9C,kDAAkD,KAAK,GAAG,EAAE;AAC5D;AACA;AACA,sDAAsD,MAAM,kCAAkC,MAAM;AACpG;AACA,mDAAmD,QAAQ,GAAG,KAAK;AACnE,sDAAsD,QAAQ,GAAG,KAAK;AACtE;AACA;AACA,iBAAiB;AACjB,8CAA8C,QAAQ,GAAG,KAAK,MAAM,EAAE;AACtE,aAAa;AACb;AACA;AACA;AACA;AACA,oDAAoD,EAAE,EAAE,GAAG,qCAAqC,GAAG,IAAI,IAAI,EAAE,EAAE,GAAG,oCAAoC,IAAI,GAAG,KAAK,GAAG,EAAE,KAAK,KAAK,GAAG,EAAE;AACtL;AACA;AACA,aAAa;AACb;AACA,KAAK;AACL;AACA,kBAAe;AACf;;;;;;;AC/DA,eAAe,mBAAO,CAAC,KAAa;AACpC,kBAAkB,mBAAO,CAAC,KAAgB;AAC1C,kBAAkB,mBAAO,CAAC,KAAgB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;;AC1BA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,UAAU;AACrB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;ACtBA,sBAAsB,mBAAO,CAAC,KAAoB;AAClD,mBAAmB,mBAAO,CAAC,KAAgB;;AAE3C;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,GAAG;AACd,WAAW,SAAS;AACpB;AACA;AACA,WAAW,UAAU;AACrB,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;AC3BA,YAAY,mBAAO,CAAC,KAAU;AAC9B,kBAAkB,mBAAO,CAAC,KAAgB;AAC1C,iBAAiB,mBAAO,CAAC,GAAe;AACxC,mBAAmB,mBAAO,CAAC,KAAiB;AAC5C,aAAa,mBAAO,CAAC,IAAW;AAChC,cAAc,mBAAO,CAAC,KAAW;AACjC,eAAe,mBAAO,CAAC,KAAY;AACnC,mBAAmB,mBAAO,CAAC,KAAgB;;AAE3C;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;AClFA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;;;;;;;;ACZA,eAAe,mBAAO,CAAC,KAAa;AACpC,gBAAgB,mBAAO,CAAC,KAAc;AACtC,eAAe,mBAAO,CAAC,KAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;ACnFA,aAAa,mBAAO,CAAC,KAAW;AAChC,iBAAiB,mBAAO,CAAC,KAAe;AACxC,SAAS,mBAAO,CAAC,KAAM;AACvB,kBAAkB,mBAAO,CAAC,KAAgB;AAC1C,iBAAiB,mBAAO,CAAC,KAAe;AACxC,iBAAiB,mBAAO,CAAC,KAAe;;AAExC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;AC/GA,iBAAiB,mBAAO,CAAC,KAAe;;AAExC;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;ACzFA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,OAAO;AACpB;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;;AAEA;;;;;;;;ACjBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;;;;;;;;ACbA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,OAAO;AACpB;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;;AAEA;;;;;;;;ACjBA,kBAAkB,mBAAO,CAAC,KAAgB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA,kBAAkB;AAClB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/lib/createAjvInstance.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/lib/processRawValidationErrors.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/lib/validator.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/lib/customizeValidator.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/lib/precompiledValidator.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/lib/createPrecompiledValidator.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/lib/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv-formats/dist/formats.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv-formats/dist/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv-formats/dist/limit.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/ajv.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/codegen/code.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/codegen/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/codegen/scope.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/errors.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/names.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/ref_error.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/resolve.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/rules.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/util.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/validate/applicability.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/validate/boolSchema.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/validate/dataType.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/validate/defaults.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/validate/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/validate/keyword.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/compile/validate/subschema.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/core.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/runtime/equal.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/runtime/ucs2length.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/runtime/uri.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/runtime/validation_error.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/additionalItems.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/additionalProperties.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/allOf.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/anyOf.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/contains.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/dependencies.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/if.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/items.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/items2020.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/not.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/oneOf.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/patternProperties.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/prefixItems.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/properties.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/propertyNames.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/applicator/thenElse.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/code.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/core/id.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/core/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/core/ref.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/discriminator/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/discriminator/types.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/draft7.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/format/format.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/format/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/metadata.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/validation/const.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/validation/enum.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/validation/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/validation/limitItems.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/validation/limitLength.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/validation/limitNumber.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/validation/limitProperties.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/validation/multipleOf.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/validation/pattern.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/validation/required.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/validator-ajv8/node_modules/ajv/dist/vocabularies/validation/uniqueItems.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/_SetCache.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/_arraySome.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/_baseIsEqual.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/_baseIsEqualDeep.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/_cacheHas.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/_equalArrays.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/_equalByTag.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/_equalObjects.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/_mapToArray.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/_setCacheAdd.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/_setCacheHas.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/_setToArray.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/isEqual.js"], "sourcesContent": ["import Ajv from 'ajv';\nimport addFormats from 'ajv-formats';\nimport isObject from 'lodash/isObject';\nimport { ADDITIONAL_PROPERTY_FLAG, RJSF_ADDITONAL_PROPERTIES_FLAG } from '@rjsf/utils';\nexport const AJV_CONFIG = {\n    allErrors: true,\n    multipleOfPrecision: 8,\n    strict: false,\n    verbose: true,\n};\nexport const COLOR_FORMAT_REGEX = /^(#?([0-9A-Fa-f]{3}){1,2}\\b|aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow|(rgb\\(\\s*\\b([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\b\\s*,\\s*\\b([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\b\\s*,\\s*\\b([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\b\\s*\\))|(rgb\\(\\s*(\\d?\\d%|100%)+\\s*,\\s*(\\d?\\d%|100%)+\\s*,\\s*(\\d?\\d%|100%)+\\s*\\)))$/;\nexport const DATA_URL_FORMAT_REGEX = /^data:([a-z]+\\/[a-z0-9-+.]+)?;(?:name=(.*);)?base64,(.*)$/;\n/** Creates an Ajv version 8 implementation object with standard support for the 'color` and `data-url` custom formats.\n * If `additionalMetaSchemas` are provided then the Ajv instance is modified to add each of the meta schemas in the\n * list. If `customFormats` are provided then those additional formats are added to the list of supported formats. If\n * `ajvOptionsOverrides` are provided then they are spread on top of the default `AJV_CONFIG` options when constructing\n * the `Ajv` instance. With Ajv v8, the JSON Schema formats are not provided by default, but can be plugged in. By\n * default, all formats from the `ajv-formats` library are added. To disable this capability, set the `ajvFormatOptions`\n * parameter to `false`. Additionally, you can configure the `ajv-formats` by providing a custom set of\n * [format options](https://github.com/ajv-validator/ajv-formats) to the `ajvFormatOptions` parameter.\n *\n * @param [additionalMetaSchemas] - The list of additional meta schemas that the validator can access\n * @param [customFormats] - The set of additional custom formats that the validator will support\n * @param [ajvOptionsOverrides={}] - The set of validator config override options\n * @param [ajvFormatOptions] - The `ajv-format` options to use when adding formats to `ajv`; pass `false` to disable it\n * @param [AjvClass] - The `Ajv` class to use when creating the validator instance\n */\nexport default function createAjvInstance(additionalMetaSchemas, customFormats, ajvOptionsOverrides = {}, ajvFormatOptions, AjvClass = Ajv) {\n    const ajv = new AjvClass({ ...AJV_CONFIG, ...ajvOptionsOverrides });\n    if (ajvFormatOptions) {\n        addFormats(ajv, ajvFormatOptions);\n    }\n    else if (ajvFormatOptions !== false) {\n        addFormats(ajv);\n    }\n    // add custom formats\n    ajv.addFormat('data-url', DATA_URL_FORMAT_REGEX);\n    ajv.addFormat('color', COLOR_FORMAT_REGEX);\n    // Add RJSF-specific additional properties keywords so Ajv doesn't report errors if strict is enabled.\n    ajv.addKeyword(ADDITIONAL_PROPERTY_FLAG);\n    ajv.addKeyword(RJSF_ADDITONAL_PROPERTIES_FLAG);\n    // add more schemas to validate against\n    if (Array.isArray(additionalMetaSchemas)) {\n        ajv.addMetaSchema(additionalMetaSchemas);\n    }\n    // add more custom formats to validate against\n    if (isObject(customFormats)) {\n        Object.keys(customFormats).forEach((formatName) => {\n            ajv.addFormat(formatName, customFormats[formatName]);\n        });\n    }\n    return ajv;\n}\n//# sourceMappingURL=createAjvInstance.js.map", "import get from 'lodash/get';\nimport { create<PERSON><PERSON>r<PERSON><PERSON><PERSON>, getDefaultFormState, getUiOptions, PROPERTIES_KEY, toErrorSchema, unwrapErrorHandler, validationDataMerge, } from '@rjsf/utils';\n/** Transforming the error output from ajv to format used by @rjsf/utils.\n * At some point, components should be updated to support ajv.\n *\n * @param errors - The list of AJV errors to convert to `RJSFValidationErrors`\n * @param [uiSchema] - An optional uiSchema that is passed to `transformErrors` and `customValidate`\n */\nexport function transformRJSFValidationErrors(errors = [], uiSchema) {\n    return errors.map((e) => {\n        const { instancePath, keyword, params, schemaPath, parentSchema, ...rest } = e;\n        let { message = '' } = rest;\n        let property = instancePath.replace(/\\//g, '.');\n        let stack = `${property} ${message}`.trim();\n        if ('missingProperty' in params) {\n            property = property ? `${property}.${params.missingProperty}` : params.missingProperty;\n            const currentProperty = params.missingProperty;\n            const uiSchemaTitle = getUiOptions(get(uiSchema, `${property.replace(/^\\./, '')}`)).title;\n            if (uiSchemaTitle) {\n                message = message.replace(currentProperty, uiSchemaTitle);\n            }\n            else {\n                const parentSchemaTitle = get(parentSchema, [PROPERTIES_KEY, currentProperty, 'title']);\n                if (parentSchemaTitle) {\n                    message = message.replace(currentProperty, parentSchemaTitle);\n                }\n            }\n            stack = message;\n        }\n        else {\n            const uiSchemaTitle = getUiOptions(get(uiSchema, `${property.replace(/^\\./, '')}`)).title;\n            if (uiSchemaTitle) {\n                stack = `'${uiSchemaTitle}' ${message}`.trim();\n            }\n            else {\n                const parentSchemaTitle = parentSchema === null || parentSchema === void 0 ? void 0 : parentSchema.title;\n                if (parentSchemaTitle) {\n                    stack = `'${parentSchemaTitle}' ${message}`.trim();\n                }\n            }\n        }\n        // put data in expected format\n        return {\n            name: keyword,\n            property,\n            message,\n            params,\n            stack,\n            schemaPath,\n        };\n    });\n}\n/** This function processes the `formData` with an optional user contributed `customValidate` function, which receives\n * the form data and a `errorHandler` function that will be used to add custom validation errors for each field. Also\n * supports a `transformErrors` function that will take the raw AJV validation errors, prior to custom validation and\n * transform them in what ever way it chooses.\n *\n * @param validator - The `ValidatorType` implementation used for the `getDefaultFormState()` call\n * @param rawErrors - The list of raw `ErrorObject`s to process\n * @param formData - The form data to validate\n * @param schema - The schema against which to validate the form data\n * @param [customValidate] - An optional function that is used to perform custom validation\n * @param [transformErrors] - An optional function that is used to transform errors after AJV validation\n * @param [uiSchema] - An optional uiSchema that is passed to `transformErrors` and `customValidate`\n */\nexport default function processRawValidationErrors(validator, rawErrors, formData, schema, customValidate, transformErrors, uiSchema) {\n    const { validationError: invalidSchemaError } = rawErrors;\n    let errors = transformRJSFValidationErrors(rawErrors.errors, uiSchema);\n    if (invalidSchemaError) {\n        errors = [...errors, { stack: invalidSchemaError.message }];\n    }\n    if (typeof transformErrors === 'function') {\n        errors = transformErrors(errors, uiSchema);\n    }\n    let errorSchema = toErrorSchema(errors);\n    if (invalidSchemaError) {\n        errorSchema = {\n            ...errorSchema,\n            $schema: {\n                __errors: [invalidSchemaError.message],\n            },\n        };\n    }\n    if (typeof customValidate !== 'function') {\n        return { errors, errorSchema };\n    }\n    // Include form data with undefined values, which is required for custom validation.\n    const newFormData = getDefaultFormState(validator, schema, formData, schema, true);\n    const errorHandler = customValidate(newFormData, createErrorHandler(newFormData), uiSchema);\n    const userErrorSchema = unwrapErrorHandler(errorHandler);\n    return validationDataMerge({ errors, errorSchema }, userErrorSchema);\n}\n//# sourceMappingURL=processRawValidationErrors.js.map", "import { ID_KEY, ROOT_SCHEMA_PREFIX, toErrorList, withIdRefPrefix, hashForSchema, } from '@rjsf/utils';\nimport createAjvInstance from './createAjvInstance';\nimport processRawValidationErrors from './processRawValidationErrors';\n/** `ValidatorType` implementation that uses the AJV 8 validation mechanism.\n */\nexport default class AJV8Validator {\n    /** Constructs an `AJV8Validator` instance using the `options`\n     *\n     * @param options - The `CustomValidatorOptionsType` options that are used to create the AJV instance\n     * @param [localizer] - If provided, is used to localize a list of Ajv `ErrorObject`s\n     */\n    constructor(options, localizer) {\n        const { additionalMetaSchemas, customFormats, ajvOptionsOverrides, ajvFormatOptions, AjvClass } = options;\n        this.ajv = createAjvInstance(additionalMetaSchemas, customFormats, ajvOptionsOverrides, ajvFormatOptions, AjvClass);\n        this.localizer = localizer;\n    }\n    /** Converts an `errorSchema` into a list of `RJSFValidationErrors`\n     *\n     * @param errorSchema - The `ErrorSchema` instance to convert\n     * @param [fieldPath=[]] - The current field path, defaults to [] if not specified\n     * @deprecated - Use the `toErrorList()` function provided by `@rjsf/utils` instead. This function will be removed in\n     *        the next major release.\n     */\n    toErrorList(errorSchema, fieldPath = []) {\n        return toErrorList(errorSchema, fieldPath);\n    }\n    /** Runs the pure validation of the `schema` and `formData` without any of the RJSF functionality. Provided for use\n     * by the playground. Returns the `errors` from the validation\n     *\n     * @param schema - The schema against which to validate the form data   * @param schema\n     * @param formData - The form data to validate\n     */\n    rawValidation(schema, formData) {\n        let compilationError = undefined;\n        let compiledValidator;\n        if (schema[ID_KEY]) {\n            compiledValidator = this.ajv.getSchema(schema[ID_KEY]);\n        }\n        try {\n            if (compiledValidator === undefined) {\n                compiledValidator = this.ajv.compile(schema);\n            }\n            compiledValidator(formData);\n        }\n        catch (err) {\n            compilationError = err;\n        }\n        let errors;\n        if (compiledValidator) {\n            if (typeof this.localizer === 'function') {\n                this.localizer(compiledValidator.errors);\n            }\n            errors = compiledValidator.errors || undefined;\n            // Clear errors to prevent persistent errors, see #1104\n            compiledValidator.errors = null;\n        }\n        return {\n            errors: errors,\n            validationError: compilationError,\n        };\n    }\n    /** This function processes the `formData` with an optional user contributed `customValidate` function, which receives\n     * the form data and a `errorHandler` function that will be used to add custom validation errors for each field. Also\n     * supports a `transformErrors` function that will take the raw AJV validation errors, prior to custom validation and\n     * transform them in what ever way it chooses.\n     *\n     * @param formData - The form data to validate\n     * @param schema - The schema against which to validate the form data\n     * @param [customValidate] - An optional function that is used to perform custom validation\n     * @param [transformErrors] - An optional function that is used to transform errors after AJV validation\n     * @param [uiSchema] - An optional uiSchema that is passed to `transformErrors` and `customValidate`\n     */\n    validateFormData(formData, schema, customValidate, transformErrors, uiSchema) {\n        const rawErrors = this.rawValidation(schema, formData);\n        return processRawValidationErrors(this, rawErrors, formData, schema, customValidate, transformErrors, uiSchema);\n    }\n    /** Validates data against a schema, returning true if the data is valid, or\n     * false otherwise. If the schema is invalid, then this function will return\n     * false.\n     *\n     * @param schema - The schema against which to validate the form data\n     * @param formData - The form data to validate\n     * @param rootSchema - The root schema used to provide $ref resolutions\n     */\n    isValid(schema, formData, rootSchema) {\n        var _a, _b;\n        const rootSchemaId = (_a = rootSchema[ID_KEY]) !== null && _a !== void 0 ? _a : ROOT_SCHEMA_PREFIX;\n        try {\n            // add the rootSchema ROOT_SCHEMA_PREFIX as id.\n            // then rewrite the schema ref's to point to the rootSchema\n            // this accounts for the case where schema have references to models\n            // that lives in the rootSchema but not in the schema in question.\n            // if (this.ajv.getSchema(rootSchemaId) === undefined) {\n            // TODO restore the commented out `if` above when the TODO in the `finally` is completed\n            this.ajv.addSchema(rootSchema, rootSchemaId);\n            // }\n            const schemaWithIdRefPrefix = withIdRefPrefix(schema);\n            const schemaId = (_b = schemaWithIdRefPrefix[ID_KEY]) !== null && _b !== void 0 ? _b : hashForSchema(schemaWithIdRefPrefix);\n            let compiledValidator;\n            compiledValidator = this.ajv.getSchema(schemaId);\n            if (compiledValidator === undefined) {\n                // Add schema by an explicit ID so it can be fetched later\n                // Fall back to using compile if necessary\n                // https://ajv.js.org/guide/managing-schemas.html#pre-adding-all-schemas-vs-adding-on-demand\n                compiledValidator =\n                    this.ajv.addSchema(schemaWithIdRefPrefix, schemaId).getSchema(schemaId) ||\n                        this.ajv.compile(schemaWithIdRefPrefix);\n            }\n            const result = compiledValidator(formData);\n            return result;\n        }\n        catch (e) {\n            console.warn('Error encountered compiling schema:', e);\n            return false;\n        }\n        finally {\n            // TODO: A function should be called if the root schema changes so we don't have to remove and recompile the schema every run.\n            // make sure we remove the rootSchema from the global ajv instance\n            this.ajv.removeSchema(rootSchemaId);\n        }\n    }\n}\n//# sourceMappingURL=validator.js.map", "import AJV8Validator from './validator';\n/** Creates and returns a customized implementation of the `ValidatorType` with the given customization `options` if\n * provided. If a `localizer` is provided, it is used to translate the messages generated by the underlying AJV\n * validation.\n *\n * @param [options={}] - The `CustomValidatorOptionsType` options that are used to create the `ValidatorType` instance\n * @param [localizer] - If provided, is used to localize a list of Ajv `ErrorObject`s\n * @returns - The custom validator implementation resulting from the set of parameters provided\n */\nexport default function customizeValidator(options = {}, localizer) {\n    return new AJV8Validator(options, localizer);\n}\n//# sourceMappingURL=customizeValidator.js.map", "import get from 'lodash/get';\nimport isEqual from 'lodash/isEqual';\nimport { hashForSchema, ID_KEY, JUNK_OPTION_ID, toErrorList, retrieveSchema, } from '@rjsf/utils';\nimport processRawValidationErrors from './processRawValidationErrors';\n/** `ValidatorType` implementation that uses an AJV 8 precompiled validator as created by the\n * `compileSchemaValidators()` function provided by the `@rjsf/validator-ajv8` library.\n */\nexport default class AJV8PrecompiledValidator {\n    /** Constructs an `AJV8PrecompiledValidator` instance using the `validateFns` and `rootSchema`\n     *\n     * @param validateFns - The map of the validation functions that are generated by the `schemaCompile()` function\n     * @param rootSchema - The root schema that was used with the `compileSchema()` function\n     * @param [localizer] - If provided, is used to localize a list of Ajv `ErrorObject`s\n     * @throws - Error when the base schema of the precompiled validator does not have a matching validator function\n     */\n    constructor(validateFns, rootSchema, localizer) {\n        this.rootSchema = rootSchema;\n        this.validateFns = validateFns;\n        this.localizer = localizer;\n        this.mainValidator = this.getValidator(rootSchema);\n    }\n    /** Returns the precompiled validator associated with the given `schema` from the map of precompiled validator\n     * functions.\n     *\n     * @param schema - The schema for which a precompiled validator function is desired\n     * @returns - The precompiled validator function associated with this schema\n     */\n    getValidator(schema) {\n        const key = get(schema, ID_KEY) || hashForSchema(schema);\n        const validator = this.validateFns[key];\n        if (!validator) {\n            throw new Error(`No precompiled validator function was found for the given schema for \"${key}\"`);\n        }\n        return validator;\n    }\n    /** Ensures that the validator is using the same schema as the root schema used to construct the precompiled\n     * validator. It first compares the given `schema` against the root schema and if they aren't the same, then it\n     * checks against the resolved root schema, on the chance that a resolved version of the root schema was passed in\n     * instead of the raw root schema.\n     *\n     * @param schema - The schema against which to validate the form data\n     * @param [formData] - The form data to validate if any\n     */\n    ensureSameRootSchema(schema, formData) {\n        if (!isEqual(schema, this.rootSchema)) {\n            // Resolve the root schema with the passed in form data since that may affect the resolution\n            const resolvedRootSchema = retrieveSchema(this, this.rootSchema, this.rootSchema, formData);\n            if (!isEqual(schema, resolvedRootSchema)) {\n                throw new Error('The schema associated with the precompiled validator differs from the rootSchema provided for validation');\n            }\n        }\n        return true;\n    }\n    /** Converts an `errorSchema` into a list of `RJSFValidationErrors`\n     *\n     * @param errorSchema - The `ErrorSchema` instance to convert\n     * @param [fieldPath=[]] - The current field path, defaults to [] if not specified\n     * @deprecated - Use the `toErrorList()` function provided by `@rjsf/utils` instead. This function will be removed in\n     *        the next major release.\n     */\n    toErrorList(errorSchema, fieldPath = []) {\n        return toErrorList(errorSchema, fieldPath);\n    }\n    /** Runs the pure validation of the `schema` and `formData` without any of the RJSF functionality. Provided for use\n     * by the playground. Returns the `errors` from the validation\n     *\n     * @param schema - The schema against which to validate the form data\n     * @param [formData] - The form data to validate, if any\n     * @throws - Error when the schema provided does not match the base schema of the precompiled validator\n     */\n    rawValidation(schema, formData) {\n        this.ensureSameRootSchema(schema, formData);\n        this.mainValidator(formData);\n        if (typeof this.localizer === 'function') {\n            this.localizer(this.mainValidator.errors);\n        }\n        const errors = this.mainValidator.errors || undefined;\n        // Clear errors to prevent persistent errors, see #1104\n        this.mainValidator.errors = null;\n        return { errors: errors };\n    }\n    /** This function processes the `formData` with an optional user contributed `customValidate` function, which receives\n     * the form data and a `errorHandler` function that will be used to add custom validation errors for each field. Also\n     * supports a `transformErrors` function that will take the raw AJV validation errors, prior to custom validation and\n     * transform them in what ever way it chooses.\n     *\n     * @param formData - The form data to validate\n     * @param schema - The schema against which to validate the form data\n     * @param [customValidate] - An optional function that is used to perform custom validation\n     * @param [transformErrors] - An optional function that is used to transform errors after AJV validation\n     * @param [uiSchema] - An optional uiSchema that is passed to `transformErrors` and `customValidate`\n     */\n    validateFormData(formData, schema, customValidate, transformErrors, uiSchema) {\n        const rawErrors = this.rawValidation(schema, formData);\n        return processRawValidationErrors(this, rawErrors, formData, schema, customValidate, transformErrors, uiSchema);\n    }\n    /** Validates data against a schema, returning true if the data is valid, or false otherwise. If the schema is\n     * invalid, then this function will return false.\n     *\n     * @param schema - The schema against which to validate the form data\n     * @param formData - The form data to validate\n     * @param rootSchema - The root schema used to provide $ref resolutions\n     * @returns - true if the formData validates against the schema, false otherwise\n     * @throws - Error when the schema provided does not match the base schema of the precompiled validator OR if there\n     *        isn't a precompiled validator function associated with the schema\n     */\n    isValid(schema, formData, rootSchema) {\n        this.ensureSameRootSchema(rootSchema, formData);\n        if (get(schema, ID_KEY) === JUNK_OPTION_ID) {\n            return false;\n        }\n        const validator = this.getValidator(schema);\n        return validator(formData);\n    }\n}\n//# sourceMappingURL=precompiledValidator.js.map", "import AJV8PrecompiledValidator from './precompiledValidator';\n/** Creates and returns a `ValidatorType` interface that is implemented with a precompiled validator. If a `localizer`\n * is provided, it is used to translate the messages generated by the underlying AJV validation.\n *\n * NOTE: The `validateFns` parameter is an object obtained by importing from a precompiled validation file created via\n * the `compileSchemaValidators()` function.\n *\n * @param validateFns - The map of the validation functions that are created by the `compileSchemaValidators()` function\n * @param rootSchema - The root schema that was used with the `compileSchemaValidators()` function\n * @param [localizer] - If provided, is used to localize a list of Ajv `ErrorObject`s\n * @returns - The precompiled validator implementation resulting from the set of parameters provided\n */\nexport default function createPrecompiledValidator(validateFns, rootSchema, localizer) {\n    return new AJV8PrecompiledValidator(validateFns, rootSchema, localizer);\n}\n//# sourceMappingURL=createPrecompiledValidator.js.map", "import customizeValidator from './customizeValidator';\nimport createPrecompiledValidator from './createPrecompiledValidator';\nexport { customizeValidator, createPrecompiledValidator };\nexport * from './types';\nexport default customizeValidator();\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.formatNames = exports.fastFormats = exports.fullFormats = void 0;\nfunction fmtDef(validate, compare) {\n    return { validate, compare };\n}\nexports.fullFormats = {\n    // date: http://tools.ietf.org/html/rfc3339#section-5.6\n    date: fmtDef(date, compareDate),\n    // date-time: http://tools.ietf.org/html/rfc3339#section-5.6\n    time: fmtDef(time, compareTime),\n    \"date-time\": fmtDef(date_time, compareDateTime),\n    // duration: https://tools.ietf.org/html/rfc3339#appendix-A\n    duration: /^P(?!$)((\\d+Y)?(\\d+M)?(\\d+D)?(T(?=\\d)(\\d+H)?(\\d+M)?(\\d+S)?)?|(\\d+W)?)$/,\n    uri,\n    \"uri-reference\": /^(?:[a-z][a-z0-9+\\-.]*:)?(?:\\/?\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\\.[a-z0-9\\-._~!$&'()*+,;=:]+)\\]|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)|(?:[a-z0-9\\-._~!$&'\"()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*|\\/(?:(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\\?(?:[a-z0-9\\-._~!$&'\"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&'\"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,\n    // uri-template: https://tools.ietf.org/html/rfc6570\n    \"uri-template\": /^(?:(?:[^\\x00-\\x20\"'<>%\\\\^`{|}]|%[0-9a-f]{2})|\\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\\*)?)*\\})*$/i,\n    // For the source: https://gist.github.com/dperini/729294\n    // For test cases: https://mathiasbynens.be/demo/url-regex\n    url: /^(?:https?|ftp):\\/\\/(?:\\S+(?::\\S*)?@)?(?:(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z0-9\\u{00a1}-\\u{ffff}]+-)*[a-z0-9\\u{00a1}-\\u{ffff}]+)(?:\\.(?:[a-z0-9\\u{00a1}-\\u{ffff}]+-)*[a-z0-9\\u{00a1}-\\u{ffff}]+)*(?:\\.(?:[a-z\\u{00a1}-\\u{ffff}]{2,})))(?::\\d{2,5})?(?:\\/[^\\s]*)?$/iu,\n    email: /^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,\n    hostname: /^(?=.{1,253}\\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\\.?$/i,\n    // optimized https://www.safaribooksonline.com/library/view/regular-expressions-cookbook/9780596802837/ch07s16.html\n    ipv4: /^(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$/,\n    ipv6: /^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))$/i,\n    regex,\n    // uuid: http://tools.ietf.org/html/rfc4122\n    uuid: /^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i,\n    // JSON-pointer: https://tools.ietf.org/html/rfc6901\n    // uri fragment: https://tools.ietf.org/html/rfc3986#appendix-A\n    \"json-pointer\": /^(?:\\/(?:[^~/]|~0|~1)*)*$/,\n    \"json-pointer-uri-fragment\": /^#(?:\\/(?:[a-z0-9_\\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i,\n    // relative JSON-pointer: http://tools.ietf.org/html/draft-luff-relative-json-pointer-00\n    \"relative-json-pointer\": /^(?:0|[1-9][0-9]*)(?:#|(?:\\/(?:[^~/]|~0|~1)*)*)$/,\n    // the following formats are used by the openapi specification: https://spec.openapis.org/oas/v3.0.0#data-types\n    // byte: https://github.com/miguelmota/is-base64\n    byte,\n    // signed 32 bit integer\n    int32: { type: \"number\", validate: validateInt32 },\n    // signed 64 bit integer\n    int64: { type: \"number\", validate: validateInt64 },\n    // C-type float\n    float: { type: \"number\", validate: validateNumber },\n    // C-type double\n    double: { type: \"number\", validate: validateNumber },\n    // hint to the UI to hide input strings\n    password: true,\n    // unchecked string payload\n    binary: true,\n};\nexports.fastFormats = {\n    ...exports.fullFormats,\n    date: fmtDef(/^\\d\\d\\d\\d-[0-1]\\d-[0-3]\\d$/, compareDate),\n    time: fmtDef(/^(?:[0-2]\\d:[0-5]\\d:[0-5]\\d|23:59:60)(?:\\.\\d+)?(?:z|[+-]\\d\\d(?::?\\d\\d)?)?$/i, compareTime),\n    \"date-time\": fmtDef(/^\\d\\d\\d\\d-[0-1]\\d-[0-3]\\d[t\\s](?:[0-2]\\d:[0-5]\\d:[0-5]\\d|23:59:60)(?:\\.\\d+)?(?:z|[+-]\\d\\d(?::?\\d\\d)?)$/i, compareDateTime),\n    // uri: https://github.com/mafintosh/is-my-json-valid/blob/master/formats.js\n    uri: /^(?:[a-z][a-z0-9+\\-.]*:)(?:\\/?\\/)?[^\\s]*$/i,\n    \"uri-reference\": /^(?:(?:[a-z][a-z0-9+\\-.]*:)?\\/?\\/)?(?:[^\\\\\\s#][^\\s#]*)?(?:#[^\\\\\\s]*)?$/i,\n    // email (sources from jsen validator):\n    // http://stackoverflow.com/questions/201323/using-a-regular-expression-to-validate-an-email-address#answer-8829363\n    // http://www.w3.org/TR/html5/forms.html#valid-e-mail-address (search for 'wilful violation')\n    email: /^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i,\n};\nexports.formatNames = Object.keys(exports.fullFormats);\nfunction isLeapYear(year) {\n    // https://tools.ietf.org/html/rfc3339#appendix-C\n    return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\nconst DATE = /^(\\d\\d\\d\\d)-(\\d\\d)-(\\d\\d)$/;\nconst DAYS = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nfunction date(str) {\n    // full-date from http://tools.ietf.org/html/rfc3339#section-5.6\n    const matches = DATE.exec(str);\n    if (!matches)\n        return false;\n    const year = +matches[1];\n    const month = +matches[2];\n    const day = +matches[3];\n    return (month >= 1 &&\n        month <= 12 &&\n        day >= 1 &&\n        day <= (month === 2 && isLeapYear(year) ? 29 : DAYS[month]));\n}\nfunction compareDate(d1, d2) {\n    if (!(d1 && d2))\n        return undefined;\n    if (d1 > d2)\n        return 1;\n    if (d1 < d2)\n        return -1;\n    return 0;\n}\nconst TIME = /^(\\d\\d):(\\d\\d):(\\d\\d)(\\.\\d+)?(z|[+-]\\d\\d(?::?\\d\\d)?)?$/i;\nfunction time(str, withTimeZone) {\n    const matches = TIME.exec(str);\n    if (!matches)\n        return false;\n    const hour = +matches[1];\n    const minute = +matches[2];\n    const second = +matches[3];\n    const timeZone = matches[5];\n    return (((hour <= 23 && minute <= 59 && second <= 59) ||\n        (hour === 23 && minute === 59 && second === 60)) &&\n        (!withTimeZone || timeZone !== \"\"));\n}\nfunction compareTime(t1, t2) {\n    if (!(t1 && t2))\n        return undefined;\n    const a1 = TIME.exec(t1);\n    const a2 = TIME.exec(t2);\n    if (!(a1 && a2))\n        return undefined;\n    t1 = a1[1] + a1[2] + a1[3] + (a1[4] || \"\");\n    t2 = a2[1] + a2[2] + a2[3] + (a2[4] || \"\");\n    if (t1 > t2)\n        return 1;\n    if (t1 < t2)\n        return -1;\n    return 0;\n}\nconst DATE_TIME_SEPARATOR = /t|\\s/i;\nfunction date_time(str) {\n    // http://tools.ietf.org/html/rfc3339#section-5.6\n    const dateTime = str.split(DATE_TIME_SEPARATOR);\n    return dateTime.length === 2 && date(dateTime[0]) && time(dateTime[1], true);\n}\nfunction compareDateTime(dt1, dt2) {\n    if (!(dt1 && dt2))\n        return undefined;\n    const [d1, t1] = dt1.split(DATE_TIME_SEPARATOR);\n    const [d2, t2] = dt2.split(DATE_TIME_SEPARATOR);\n    const res = compareDate(d1, d2);\n    if (res === undefined)\n        return undefined;\n    return res || compareTime(t1, t2);\n}\nconst NOT_URI_FRAGMENT = /\\/|:/;\nconst URI = /^(?:[a-z][a-z0-9+\\-.]*:)(?:\\/?\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\\.[a-z0-9\\-._~!$&'()*+,;=:]+)\\]|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)|(?:[a-z0-9\\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\\?(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;\nfunction uri(str) {\n    // http://jmrware.com/articles/2009/uri_regexp/URI_regex.html + optional protocol + required \".\"\n    return NOT_URI_FRAGMENT.test(str) && URI.test(str);\n}\nconst BYTE = /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/gm;\nfunction byte(str) {\n    BYTE.lastIndex = 0;\n    return BYTE.test(str);\n}\nconst MIN_INT32 = -(2 ** 31);\nconst MAX_INT32 = 2 ** 31 - 1;\nfunction validateInt32(value) {\n    return Number.isInteger(value) && value <= MAX_INT32 && value >= MIN_INT32;\n}\nfunction validateInt64(value) {\n    // JSON and javascript max Int is 2**53, so any int that passes isInteger is valid for Int64\n    return Number.isInteger(value);\n}\nfunction validateNumber() {\n    return true;\n}\nconst Z_ANCHOR = /[^\\\\]\\\\Z/;\nfunction regex(str) {\n    if (Z_ANCHOR.test(str))\n        return false;\n    try {\n        new RegExp(str);\n        return true;\n    }\n    catch (e) {\n        return false;\n    }\n}\n//# sourceMappingURL=formats.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst formats_1 = require(\"./formats\");\nconst limit_1 = require(\"./limit\");\nconst codegen_1 = require(\"ajv/dist/compile/codegen\");\nconst fullName = new codegen_1.Name(\"fullFormats\");\nconst fastName = new codegen_1.Name(\"fastFormats\");\nconst formatsPlugin = (ajv, opts = { keywords: true }) => {\n    if (Array.isArray(opts)) {\n        addFormats(ajv, opts, formats_1.fullFormats, fullName);\n        return ajv;\n    }\n    const [formats, exportName] = opts.mode === \"fast\" ? [formats_1.fastFormats, fastName] : [formats_1.fullFormats, fullName];\n    const list = opts.formats || formats_1.formatNames;\n    addFormats(ajv, list, formats, exportName);\n    if (opts.keywords)\n        limit_1.default(ajv);\n    return ajv;\n};\nformatsPlugin.get = (name, mode = \"full\") => {\n    const formats = mode === \"fast\" ? formats_1.fastFormats : formats_1.fullFormats;\n    const f = formats[name];\n    if (!f)\n        throw new Error(`Unknown format \"${name}\"`);\n    return f;\n};\nfunction addFormats(ajv, list, fs, exportName) {\n    var _a;\n    var _b;\n    (_a = (_b = ajv.opts.code).formats) !== null && _a !== void 0 ? _a : (_b.formats = codegen_1._ `require(\"ajv-formats/dist/formats\").${exportName}`);\n    for (const f of list)\n        ajv.addFormat(f, fs[f]);\n}\nmodule.exports = exports = formatsPlugin;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.default = formatsPlugin;\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.formatLimitDefinition = void 0;\nconst ajv_1 = require(\"ajv\");\nconst codegen_1 = require(\"ajv/dist/compile/codegen\");\nconst ops = codegen_1.operators;\nconst KWDs = {\n    formatMaximum: { okStr: \"<=\", ok: ops.LTE, fail: ops.GT },\n    formatMinimum: { okStr: \">=\", ok: ops.GTE, fail: ops.LT },\n    formatExclusiveMaximum: { okStr: \"<\", ok: ops.LT, fail: ops.GTE },\n    formatExclusiveMinimum: { okStr: \">\", ok: ops.GT, fail: ops.LTE },\n};\nconst error = {\n    message: ({ keyword, schemaCode }) => codegen_1.str `should be ${KWDs[keyword].okStr} ${schemaCode}`,\n    params: ({ keyword, schemaCode }) => codegen_1._ `{comparison: ${KWDs[keyword].okStr}, limit: ${schemaCode}}`,\n};\nexports.formatLimitDefinition = {\n    keyword: Object.keys(KWDs),\n    type: \"string\",\n    schemaType: \"string\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, data, schemaCode, keyword, it } = cxt;\n        const { opts, self } = it;\n        if (!opts.validateFormats)\n            return;\n        const fCxt = new ajv_1.KeywordCxt(it, self.RULES.all.format.definition, \"format\");\n        if (fCxt.$data)\n            validate$DataFormat();\n        else\n            validateFormat();\n        function validate$DataFormat() {\n            const fmts = gen.scopeValue(\"formats\", {\n                ref: self.formats,\n                code: opts.code.formats,\n            });\n            const fmt = gen.const(\"fmt\", codegen_1._ `${fmts}[${fCxt.schemaCode}]`);\n            cxt.fail$data(codegen_1.or(codegen_1._ `typeof ${fmt} != \"object\"`, codegen_1._ `${fmt} instanceof RegExp`, codegen_1._ `typeof ${fmt}.compare != \"function\"`, compareCode(fmt)));\n        }\n        function validateFormat() {\n            const format = fCxt.schema;\n            const fmtDef = self.formats[format];\n            if (!fmtDef || fmtDef === true)\n                return;\n            if (typeof fmtDef != \"object\" ||\n                fmtDef instanceof RegExp ||\n                typeof fmtDef.compare != \"function\") {\n                throw new Error(`\"${keyword}\": format \"${format}\" does not define \"compare\" function`);\n            }\n            const fmt = gen.scopeValue(\"formats\", {\n                key: format,\n                ref: fmtDef,\n                code: opts.code.formats ? codegen_1._ `${opts.code.formats}${codegen_1.getProperty(format)}` : undefined,\n            });\n            cxt.fail$data(compareCode(fmt));\n        }\n        function compareCode(fmt) {\n            return codegen_1._ `${fmt}.compare(${data}, ${schemaCode}) ${KWDs[keyword].fail} 0`;\n        }\n    },\n    dependencies: [\"format\"],\n};\nconst formatLimitPlugin = (ajv) => {\n    ajv.addKeyword(exports.formatLimitDefinition);\n    return ajv;\n};\nexports.default = formatLimitPlugin;\n//# sourceMappingURL=limit.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MissingRefError = exports.ValidationError = exports.CodeGen = exports.Name = exports.nil = exports.stringify = exports.str = exports._ = exports.KeywordCxt = exports.Ajv = void 0;\nconst core_1 = require(\"./core\");\nconst draft7_1 = require(\"./vocabularies/draft7\");\nconst discriminator_1 = require(\"./vocabularies/discriminator\");\nconst draft7MetaSchema = require(\"./refs/json-schema-draft-07.json\");\nconst META_SUPPORT_DATA = [\"/properties\"];\nconst META_SCHEMA_ID = \"http://json-schema.org/draft-07/schema\";\nclass Ajv extends core_1.default {\n    _addVocabularies() {\n        super._addVocabularies();\n        draft7_1.default.forEach((v) => this.addVocabulary(v));\n        if (this.opts.discriminator)\n            this.addKeyword(discriminator_1.default);\n    }\n    _addDefaultMetaSchema() {\n        super._addDefaultMetaSchema();\n        if (!this.opts.meta)\n            return;\n        const metaSchema = this.opts.$data\n            ? this.$dataMetaSchema(draft7MetaSchema, META_SUPPORT_DATA)\n            : draft7MetaSchema;\n        this.addMetaSchema(metaSchema, META_SCHEMA_ID, false);\n        this.refs[\"http://json-schema.org/schema\"] = META_SCHEMA_ID;\n    }\n    defaultMeta() {\n        return (this.opts.defaultMeta =\n            super.defaultMeta() || (this.getSchema(META_SCHEMA_ID) ? META_SCHEMA_ID : undefined));\n    }\n}\nexports.Ajv = Ajv;\nmodule.exports = exports = Ajv;\nmodule.exports.Ajv = Ajv;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.default = Ajv;\nvar validate_1 = require(\"./compile/validate\");\nObject.defineProperty(exports, \"KeywordCxt\", { enumerable: true, get: function () { return validate_1.KeywordCxt; } });\nvar codegen_1 = require(\"./compile/codegen\");\nObject.defineProperty(exports, \"_\", { enumerable: true, get: function () { return codegen_1._; } });\nObject.defineProperty(exports, \"str\", { enumerable: true, get: function () { return codegen_1.str; } });\nObject.defineProperty(exports, \"stringify\", { enumerable: true, get: function () { return codegen_1.stringify; } });\nObject.defineProperty(exports, \"nil\", { enumerable: true, get: function () { return codegen_1.nil; } });\nObject.defineProperty(exports, \"Name\", { enumerable: true, get: function () { return codegen_1.Name; } });\nObject.defineProperty(exports, \"CodeGen\", { enumerable: true, get: function () { return codegen_1.CodeGen; } });\nvar validation_error_1 = require(\"./runtime/validation_error\");\nObject.defineProperty(exports, \"ValidationError\", { enumerable: true, get: function () { return validation_error_1.default; } });\nvar ref_error_1 = require(\"./compile/ref_error\");\nObject.defineProperty(exports, \"MissingRefError\", { enumerable: true, get: function () { return ref_error_1.default; } });\n//# sourceMappingURL=ajv.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.regexpCode = exports.getEsmExportName = exports.getProperty = exports.safeStringify = exports.stringify = exports.strConcat = exports.addCodeArg = exports.str = exports._ = exports.nil = exports._Code = exports.Name = exports.IDENTIFIER = exports._CodeOrName = void 0;\n// eslint-disable-next-line @typescript-eslint/no-extraneous-class\nclass _CodeOrName {\n}\nexports._CodeOrName = _CodeOrName;\nexports.IDENTIFIER = /^[a-z$_][a-z$_0-9]*$/i;\nclass Name extends _CodeOrName {\n    constructor(s) {\n        super();\n        if (!exports.IDENTIFIER.test(s))\n            throw new Error(\"CodeGen: name must be a valid identifier\");\n        this.str = s;\n    }\n    toString() {\n        return this.str;\n    }\n    emptyStr() {\n        return false;\n    }\n    get names() {\n        return { [this.str]: 1 };\n    }\n}\nexports.Name = Name;\nclass _Code extends _CodeOrName {\n    constructor(code) {\n        super();\n        this._items = typeof code === \"string\" ? [code] : code;\n    }\n    toString() {\n        return this.str;\n    }\n    emptyStr() {\n        if (this._items.length > 1)\n            return false;\n        const item = this._items[0];\n        return item === \"\" || item === '\"\"';\n    }\n    get str() {\n        var _a;\n        return ((_a = this._str) !== null && _a !== void 0 ? _a : (this._str = this._items.reduce((s, c) => `${s}${c}`, \"\")));\n    }\n    get names() {\n        var _a;\n        return ((_a = this._names) !== null && _a !== void 0 ? _a : (this._names = this._items.reduce((names, c) => {\n            if (c instanceof Name)\n                names[c.str] = (names[c.str] || 0) + 1;\n            return names;\n        }, {})));\n    }\n}\nexports._Code = _Code;\nexports.nil = new _Code(\"\");\nfunction _(strs, ...args) {\n    const code = [strs[0]];\n    let i = 0;\n    while (i < args.length) {\n        addCodeArg(code, args[i]);\n        code.push(strs[++i]);\n    }\n    return new _Code(code);\n}\nexports._ = _;\nconst plus = new _Code(\"+\");\nfunction str(strs, ...args) {\n    const expr = [safeStringify(strs[0])];\n    let i = 0;\n    while (i < args.length) {\n        expr.push(plus);\n        addCodeArg(expr, args[i]);\n        expr.push(plus, safeStringify(strs[++i]));\n    }\n    optimize(expr);\n    return new _Code(expr);\n}\nexports.str = str;\nfunction addCodeArg(code, arg) {\n    if (arg instanceof _Code)\n        code.push(...arg._items);\n    else if (arg instanceof Name)\n        code.push(arg);\n    else\n        code.push(interpolate(arg));\n}\nexports.addCodeArg = addCodeArg;\nfunction optimize(expr) {\n    let i = 1;\n    while (i < expr.length - 1) {\n        if (expr[i] === plus) {\n            const res = mergeExprItems(expr[i - 1], expr[i + 1]);\n            if (res !== undefined) {\n                expr.splice(i - 1, 3, res);\n                continue;\n            }\n            expr[i++] = \"+\";\n        }\n        i++;\n    }\n}\nfunction mergeExprItems(a, b) {\n    if (b === '\"\"')\n        return a;\n    if (a === '\"\"')\n        return b;\n    if (typeof a == \"string\") {\n        if (b instanceof Name || a[a.length - 1] !== '\"')\n            return;\n        if (typeof b != \"string\")\n            return `${a.slice(0, -1)}${b}\"`;\n        if (b[0] === '\"')\n            return a.slice(0, -1) + b.slice(1);\n        return;\n    }\n    if (typeof b == \"string\" && b[0] === '\"' && !(a instanceof Name))\n        return `\"${a}${b.slice(1)}`;\n    return;\n}\nfunction strConcat(c1, c2) {\n    return c2.emptyStr() ? c1 : c1.emptyStr() ? c2 : str `${c1}${c2}`;\n}\nexports.strConcat = strConcat;\n// TODO do not allow arrays here\nfunction interpolate(x) {\n    return typeof x == \"number\" || typeof x == \"boolean\" || x === null\n        ? x\n        : safeStringify(Array.isArray(x) ? x.join(\",\") : x);\n}\nfunction stringify(x) {\n    return new _Code(safeStringify(x));\n}\nexports.stringify = stringify;\nfunction safeStringify(x) {\n    return JSON.stringify(x)\n        .replace(/\\u2028/g, \"\\\\u2028\")\n        .replace(/\\u2029/g, \"\\\\u2029\");\n}\nexports.safeStringify = safeStringify;\nfunction getProperty(key) {\n    return typeof key == \"string\" && exports.IDENTIFIER.test(key) ? new _Code(`.${key}`) : _ `[${key}]`;\n}\nexports.getProperty = getProperty;\n//Does best effort to format the name properly\nfunction getEsmExportName(key) {\n    if (typeof key == \"string\" && exports.IDENTIFIER.test(key)) {\n        return new _Code(`${key}`);\n    }\n    throw new Error(`CodeGen: invalid export name: ${key}, use explicit $id name mapping`);\n}\nexports.getEsmExportName = getEsmExportName;\nfunction regexpCode(rx) {\n    return new _Code(rx.toString());\n}\nexports.regexpCode = regexpCode;\n//# sourceMappingURL=code.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.or = exports.and = exports.not = exports.CodeGen = exports.operators = exports.varKinds = exports.ValueScopeName = exports.ValueScope = exports.Scope = exports.Name = exports.regexpCode = exports.stringify = exports.getProperty = exports.nil = exports.strConcat = exports.str = exports._ = void 0;\nconst code_1 = require(\"./code\");\nconst scope_1 = require(\"./scope\");\nvar code_2 = require(\"./code\");\nObject.defineProperty(exports, \"_\", { enumerable: true, get: function () { return code_2._; } });\nObject.defineProperty(exports, \"str\", { enumerable: true, get: function () { return code_2.str; } });\nObject.defineProperty(exports, \"strConcat\", { enumerable: true, get: function () { return code_2.strConcat; } });\nObject.defineProperty(exports, \"nil\", { enumerable: true, get: function () { return code_2.nil; } });\nObject.defineProperty(exports, \"getProperty\", { enumerable: true, get: function () { return code_2.getProperty; } });\nObject.defineProperty(exports, \"stringify\", { enumerable: true, get: function () { return code_2.stringify; } });\nObject.defineProperty(exports, \"regexpCode\", { enumerable: true, get: function () { return code_2.regexpCode; } });\nObject.defineProperty(exports, \"Name\", { enumerable: true, get: function () { return code_2.Name; } });\nvar scope_2 = require(\"./scope\");\nObject.defineProperty(exports, \"Scope\", { enumerable: true, get: function () { return scope_2.Scope; } });\nObject.defineProperty(exports, \"ValueScope\", { enumerable: true, get: function () { return scope_2.ValueScope; } });\nObject.defineProperty(exports, \"ValueScopeName\", { enumerable: true, get: function () { return scope_2.ValueScopeName; } });\nObject.defineProperty(exports, \"varKinds\", { enumerable: true, get: function () { return scope_2.varKinds; } });\nexports.operators = {\n    GT: new code_1._Code(\">\"),\n    GTE: new code_1._Code(\">=\"),\n    LT: new code_1._Code(\"<\"),\n    LTE: new code_1._Code(\"<=\"),\n    EQ: new code_1._Code(\"===\"),\n    NEQ: new code_1._Code(\"!==\"),\n    NOT: new code_1._Code(\"!\"),\n    OR: new code_1._Code(\"||\"),\n    AND: new code_1._Code(\"&&\"),\n    ADD: new code_1._Code(\"+\"),\n};\nclass Node {\n    optimizeNodes() {\n        return this;\n    }\n    optimizeNames(_names, _constants) {\n        return this;\n    }\n}\nclass Def extends Node {\n    constructor(varKind, name, rhs) {\n        super();\n        this.varKind = varKind;\n        this.name = name;\n        this.rhs = rhs;\n    }\n    render({ es5, _n }) {\n        const varKind = es5 ? scope_1.varKinds.var : this.varKind;\n        const rhs = this.rhs === undefined ? \"\" : ` = ${this.rhs}`;\n        return `${varKind} ${this.name}${rhs};` + _n;\n    }\n    optimizeNames(names, constants) {\n        if (!names[this.name.str])\n            return;\n        if (this.rhs)\n            this.rhs = optimizeExpr(this.rhs, names, constants);\n        return this;\n    }\n    get names() {\n        return this.rhs instanceof code_1._CodeOrName ? this.rhs.names : {};\n    }\n}\nclass Assign extends Node {\n    constructor(lhs, rhs, sideEffects) {\n        super();\n        this.lhs = lhs;\n        this.rhs = rhs;\n        this.sideEffects = sideEffects;\n    }\n    render({ _n }) {\n        return `${this.lhs} = ${this.rhs};` + _n;\n    }\n    optimizeNames(names, constants) {\n        if (this.lhs instanceof code_1.Name && !names[this.lhs.str] && !this.sideEffects)\n            return;\n        this.rhs = optimizeExpr(this.rhs, names, constants);\n        return this;\n    }\n    get names() {\n        const names = this.lhs instanceof code_1.Name ? {} : { ...this.lhs.names };\n        return addExprNames(names, this.rhs);\n    }\n}\nclass AssignOp extends Assign {\n    constructor(lhs, op, rhs, sideEffects) {\n        super(lhs, rhs, sideEffects);\n        this.op = op;\n    }\n    render({ _n }) {\n        return `${this.lhs} ${this.op}= ${this.rhs};` + _n;\n    }\n}\nclass Label extends Node {\n    constructor(label) {\n        super();\n        this.label = label;\n        this.names = {};\n    }\n    render({ _n }) {\n        return `${this.label}:` + _n;\n    }\n}\nclass Break extends Node {\n    constructor(label) {\n        super();\n        this.label = label;\n        this.names = {};\n    }\n    render({ _n }) {\n        const label = this.label ? ` ${this.label}` : \"\";\n        return `break${label};` + _n;\n    }\n}\nclass Throw extends Node {\n    constructor(error) {\n        super();\n        this.error = error;\n    }\n    render({ _n }) {\n        return `throw ${this.error};` + _n;\n    }\n    get names() {\n        return this.error.names;\n    }\n}\nclass AnyCode extends Node {\n    constructor(code) {\n        super();\n        this.code = code;\n    }\n    render({ _n }) {\n        return `${this.code};` + _n;\n    }\n    optimizeNodes() {\n        return `${this.code}` ? this : undefined;\n    }\n    optimizeNames(names, constants) {\n        this.code = optimizeExpr(this.code, names, constants);\n        return this;\n    }\n    get names() {\n        return this.code instanceof code_1._CodeOrName ? this.code.names : {};\n    }\n}\nclass ParentNode extends Node {\n    constructor(nodes = []) {\n        super();\n        this.nodes = nodes;\n    }\n    render(opts) {\n        return this.nodes.reduce((code, n) => code + n.render(opts), \"\");\n    }\n    optimizeNodes() {\n        const { nodes } = this;\n        let i = nodes.length;\n        while (i--) {\n            const n = nodes[i].optimizeNodes();\n            if (Array.isArray(n))\n                nodes.splice(i, 1, ...n);\n            else if (n)\n                nodes[i] = n;\n            else\n                nodes.splice(i, 1);\n        }\n        return nodes.length > 0 ? this : undefined;\n    }\n    optimizeNames(names, constants) {\n        const { nodes } = this;\n        let i = nodes.length;\n        while (i--) {\n            // iterating backwards improves 1-pass optimization\n            const n = nodes[i];\n            if (n.optimizeNames(names, constants))\n                continue;\n            subtractNames(names, n.names);\n            nodes.splice(i, 1);\n        }\n        return nodes.length > 0 ? this : undefined;\n    }\n    get names() {\n        return this.nodes.reduce((names, n) => addNames(names, n.names), {});\n    }\n}\nclass BlockNode extends ParentNode {\n    render(opts) {\n        return \"{\" + opts._n + super.render(opts) + \"}\" + opts._n;\n    }\n}\nclass Root extends ParentNode {\n}\nclass Else extends BlockNode {\n}\nElse.kind = \"else\";\nclass If extends BlockNode {\n    constructor(condition, nodes) {\n        super(nodes);\n        this.condition = condition;\n    }\n    render(opts) {\n        let code = `if(${this.condition})` + super.render(opts);\n        if (this.else)\n            code += \"else \" + this.else.render(opts);\n        return code;\n    }\n    optimizeNodes() {\n        super.optimizeNodes();\n        const cond = this.condition;\n        if (cond === true)\n            return this.nodes; // else is ignored here\n        let e = this.else;\n        if (e) {\n            const ns = e.optimizeNodes();\n            e = this.else = Array.isArray(ns) ? new Else(ns) : ns;\n        }\n        if (e) {\n            if (cond === false)\n                return e instanceof If ? e : e.nodes;\n            if (this.nodes.length)\n                return this;\n            return new If(not(cond), e instanceof If ? [e] : e.nodes);\n        }\n        if (cond === false || !this.nodes.length)\n            return undefined;\n        return this;\n    }\n    optimizeNames(names, constants) {\n        var _a;\n        this.else = (_a = this.else) === null || _a === void 0 ? void 0 : _a.optimizeNames(names, constants);\n        if (!(super.optimizeNames(names, constants) || this.else))\n            return;\n        this.condition = optimizeExpr(this.condition, names, constants);\n        return this;\n    }\n    get names() {\n        const names = super.names;\n        addExprNames(names, this.condition);\n        if (this.else)\n            addNames(names, this.else.names);\n        return names;\n    }\n}\nIf.kind = \"if\";\nclass For extends BlockNode {\n}\nFor.kind = \"for\";\nclass ForLoop extends For {\n    constructor(iteration) {\n        super();\n        this.iteration = iteration;\n    }\n    render(opts) {\n        return `for(${this.iteration})` + super.render(opts);\n    }\n    optimizeNames(names, constants) {\n        if (!super.optimizeNames(names, constants))\n            return;\n        this.iteration = optimizeExpr(this.iteration, names, constants);\n        return this;\n    }\n    get names() {\n        return addNames(super.names, this.iteration.names);\n    }\n}\nclass ForRange extends For {\n    constructor(varKind, name, from, to) {\n        super();\n        this.varKind = varKind;\n        this.name = name;\n        this.from = from;\n        this.to = to;\n    }\n    render(opts) {\n        const varKind = opts.es5 ? scope_1.varKinds.var : this.varKind;\n        const { name, from, to } = this;\n        return `for(${varKind} ${name}=${from}; ${name}<${to}; ${name}++)` + super.render(opts);\n    }\n    get names() {\n        const names = addExprNames(super.names, this.from);\n        return addExprNames(names, this.to);\n    }\n}\nclass ForIter extends For {\n    constructor(loop, varKind, name, iterable) {\n        super();\n        this.loop = loop;\n        this.varKind = varKind;\n        this.name = name;\n        this.iterable = iterable;\n    }\n    render(opts) {\n        return `for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})` + super.render(opts);\n    }\n    optimizeNames(names, constants) {\n        if (!super.optimizeNames(names, constants))\n            return;\n        this.iterable = optimizeExpr(this.iterable, names, constants);\n        return this;\n    }\n    get names() {\n        return addNames(super.names, this.iterable.names);\n    }\n}\nclass Func extends BlockNode {\n    constructor(name, args, async) {\n        super();\n        this.name = name;\n        this.args = args;\n        this.async = async;\n    }\n    render(opts) {\n        const _async = this.async ? \"async \" : \"\";\n        return `${_async}function ${this.name}(${this.args})` + super.render(opts);\n    }\n}\nFunc.kind = \"func\";\nclass Return extends ParentNode {\n    render(opts) {\n        return \"return \" + super.render(opts);\n    }\n}\nReturn.kind = \"return\";\nclass Try extends BlockNode {\n    render(opts) {\n        let code = \"try\" + super.render(opts);\n        if (this.catch)\n            code += this.catch.render(opts);\n        if (this.finally)\n            code += this.finally.render(opts);\n        return code;\n    }\n    optimizeNodes() {\n        var _a, _b;\n        super.optimizeNodes();\n        (_a = this.catch) === null || _a === void 0 ? void 0 : _a.optimizeNodes();\n        (_b = this.finally) === null || _b === void 0 ? void 0 : _b.optimizeNodes();\n        return this;\n    }\n    optimizeNames(names, constants) {\n        var _a, _b;\n        super.optimizeNames(names, constants);\n        (_a = this.catch) === null || _a === void 0 ? void 0 : _a.optimizeNames(names, constants);\n        (_b = this.finally) === null || _b === void 0 ? void 0 : _b.optimizeNames(names, constants);\n        return this;\n    }\n    get names() {\n        const names = super.names;\n        if (this.catch)\n            addNames(names, this.catch.names);\n        if (this.finally)\n            addNames(names, this.finally.names);\n        return names;\n    }\n}\nclass Catch extends BlockNode {\n    constructor(error) {\n        super();\n        this.error = error;\n    }\n    render(opts) {\n        return `catch(${this.error})` + super.render(opts);\n    }\n}\nCatch.kind = \"catch\";\nclass Finally extends BlockNode {\n    render(opts) {\n        return \"finally\" + super.render(opts);\n    }\n}\nFinally.kind = \"finally\";\nclass CodeGen {\n    constructor(extScope, opts = {}) {\n        this._values = {};\n        this._blockStarts = [];\n        this._constants = {};\n        this.opts = { ...opts, _n: opts.lines ? \"\\n\" : \"\" };\n        this._extScope = extScope;\n        this._scope = new scope_1.Scope({ parent: extScope });\n        this._nodes = [new Root()];\n    }\n    toString() {\n        return this._root.render(this.opts);\n    }\n    // returns unique name in the internal scope\n    name(prefix) {\n        return this._scope.name(prefix);\n    }\n    // reserves unique name in the external scope\n    scopeName(prefix) {\n        return this._extScope.name(prefix);\n    }\n    // reserves unique name in the external scope and assigns value to it\n    scopeValue(prefixOrName, value) {\n        const name = this._extScope.value(prefixOrName, value);\n        const vs = this._values[name.prefix] || (this._values[name.prefix] = new Set());\n        vs.add(name);\n        return name;\n    }\n    getScopeValue(prefix, keyOrRef) {\n        return this._extScope.getValue(prefix, keyOrRef);\n    }\n    // return code that assigns values in the external scope to the names that are used internally\n    // (same names that were returned by gen.scopeName or gen.scopeValue)\n    scopeRefs(scopeName) {\n        return this._extScope.scopeRefs(scopeName, this._values);\n    }\n    scopeCode() {\n        return this._extScope.scopeCode(this._values);\n    }\n    _def(varKind, nameOrPrefix, rhs, constant) {\n        const name = this._scope.toName(nameOrPrefix);\n        if (rhs !== undefined && constant)\n            this._constants[name.str] = rhs;\n        this._leafNode(new Def(varKind, name, rhs));\n        return name;\n    }\n    // `const` declaration (`var` in es5 mode)\n    const(nameOrPrefix, rhs, _constant) {\n        return this._def(scope_1.varKinds.const, nameOrPrefix, rhs, _constant);\n    }\n    // `let` declaration with optional assignment (`var` in es5 mode)\n    let(nameOrPrefix, rhs, _constant) {\n        return this._def(scope_1.varKinds.let, nameOrPrefix, rhs, _constant);\n    }\n    // `var` declaration with optional assignment\n    var(nameOrPrefix, rhs, _constant) {\n        return this._def(scope_1.varKinds.var, nameOrPrefix, rhs, _constant);\n    }\n    // assignment code\n    assign(lhs, rhs, sideEffects) {\n        return this._leafNode(new Assign(lhs, rhs, sideEffects));\n    }\n    // `+=` code\n    add(lhs, rhs) {\n        return this._leafNode(new AssignOp(lhs, exports.operators.ADD, rhs));\n    }\n    // appends passed SafeExpr to code or executes Block\n    code(c) {\n        if (typeof c == \"function\")\n            c();\n        else if (c !== code_1.nil)\n            this._leafNode(new AnyCode(c));\n        return this;\n    }\n    // returns code for object literal for the passed argument list of key-value pairs\n    object(...keyValues) {\n        const code = [\"{\"];\n        for (const [key, value] of keyValues) {\n            if (code.length > 1)\n                code.push(\",\");\n            code.push(key);\n            if (key !== value || this.opts.es5) {\n                code.push(\":\");\n                (0, code_1.addCodeArg)(code, value);\n            }\n        }\n        code.push(\"}\");\n        return new code_1._Code(code);\n    }\n    // `if` clause (or statement if `thenBody` and, optionally, `elseBody` are passed)\n    if(condition, thenBody, elseBody) {\n        this._blockNode(new If(condition));\n        if (thenBody && elseBody) {\n            this.code(thenBody).else().code(elseBody).endIf();\n        }\n        else if (thenBody) {\n            this.code(thenBody).endIf();\n        }\n        else if (elseBody) {\n            throw new Error('CodeGen: \"else\" body without \"then\" body');\n        }\n        return this;\n    }\n    // `else if` clause - invalid without `if` or after `else` clauses\n    elseIf(condition) {\n        return this._elseNode(new If(condition));\n    }\n    // `else` clause - only valid after `if` or `else if` clauses\n    else() {\n        return this._elseNode(new Else());\n    }\n    // end `if` statement (needed if gen.if was used only with condition)\n    endIf() {\n        return this._endBlockNode(If, Else);\n    }\n    _for(node, forBody) {\n        this._blockNode(node);\n        if (forBody)\n            this.code(forBody).endFor();\n        return this;\n    }\n    // a generic `for` clause (or statement if `forBody` is passed)\n    for(iteration, forBody) {\n        return this._for(new ForLoop(iteration), forBody);\n    }\n    // `for` statement for a range of values\n    forRange(nameOrPrefix, from, to, forBody, varKind = this.opts.es5 ? scope_1.varKinds.var : scope_1.varKinds.let) {\n        const name = this._scope.toName(nameOrPrefix);\n        return this._for(new ForRange(varKind, name, from, to), () => forBody(name));\n    }\n    // `for-of` statement (in es5 mode replace with a normal for loop)\n    forOf(nameOrPrefix, iterable, forBody, varKind = scope_1.varKinds.const) {\n        const name = this._scope.toName(nameOrPrefix);\n        if (this.opts.es5) {\n            const arr = iterable instanceof code_1.Name ? iterable : this.var(\"_arr\", iterable);\n            return this.forRange(\"_i\", 0, (0, code_1._) `${arr}.length`, (i) => {\n                this.var(name, (0, code_1._) `${arr}[${i}]`);\n                forBody(name);\n            });\n        }\n        return this._for(new ForIter(\"of\", varKind, name, iterable), () => forBody(name));\n    }\n    // `for-in` statement.\n    // With option `ownProperties` replaced with a `for-of` loop for object keys\n    forIn(nameOrPrefix, obj, forBody, varKind = this.opts.es5 ? scope_1.varKinds.var : scope_1.varKinds.const) {\n        if (this.opts.ownProperties) {\n            return this.forOf(nameOrPrefix, (0, code_1._) `Object.keys(${obj})`, forBody);\n        }\n        const name = this._scope.toName(nameOrPrefix);\n        return this._for(new ForIter(\"in\", varKind, name, obj), () => forBody(name));\n    }\n    // end `for` loop\n    endFor() {\n        return this._endBlockNode(For);\n    }\n    // `label` statement\n    label(label) {\n        return this._leafNode(new Label(label));\n    }\n    // `break` statement\n    break(label) {\n        return this._leafNode(new Break(label));\n    }\n    // `return` statement\n    return(value) {\n        const node = new Return();\n        this._blockNode(node);\n        this.code(value);\n        if (node.nodes.length !== 1)\n            throw new Error('CodeGen: \"return\" should have one node');\n        return this._endBlockNode(Return);\n    }\n    // `try` statement\n    try(tryBody, catchCode, finallyCode) {\n        if (!catchCode && !finallyCode)\n            throw new Error('CodeGen: \"try\" without \"catch\" and \"finally\"');\n        const node = new Try();\n        this._blockNode(node);\n        this.code(tryBody);\n        if (catchCode) {\n            const error = this.name(\"e\");\n            this._currNode = node.catch = new Catch(error);\n            catchCode(error);\n        }\n        if (finallyCode) {\n            this._currNode = node.finally = new Finally();\n            this.code(finallyCode);\n        }\n        return this._endBlockNode(Catch, Finally);\n    }\n    // `throw` statement\n    throw(error) {\n        return this._leafNode(new Throw(error));\n    }\n    // start self-balancing block\n    block(body, nodeCount) {\n        this._blockStarts.push(this._nodes.length);\n        if (body)\n            this.code(body).endBlock(nodeCount);\n        return this;\n    }\n    // end the current self-balancing block\n    endBlock(nodeCount) {\n        const len = this._blockStarts.pop();\n        if (len === undefined)\n            throw new Error(\"CodeGen: not in self-balancing block\");\n        const toClose = this._nodes.length - len;\n        if (toClose < 0 || (nodeCount !== undefined && toClose !== nodeCount)) {\n            throw new Error(`CodeGen: wrong number of nodes: ${toClose} vs ${nodeCount} expected`);\n        }\n        this._nodes.length = len;\n        return this;\n    }\n    // `function` heading (or definition if funcBody is passed)\n    func(name, args = code_1.nil, async, funcBody) {\n        this._blockNode(new Func(name, args, async));\n        if (funcBody)\n            this.code(funcBody).endFunc();\n        return this;\n    }\n    // end function definition\n    endFunc() {\n        return this._endBlockNode(Func);\n    }\n    optimize(n = 1) {\n        while (n-- > 0) {\n            this._root.optimizeNodes();\n            this._root.optimizeNames(this._root.names, this._constants);\n        }\n    }\n    _leafNode(node) {\n        this._currNode.nodes.push(node);\n        return this;\n    }\n    _blockNode(node) {\n        this._currNode.nodes.push(node);\n        this._nodes.push(node);\n    }\n    _endBlockNode(N1, N2) {\n        const n = this._currNode;\n        if (n instanceof N1 || (N2 && n instanceof N2)) {\n            this._nodes.pop();\n            return this;\n        }\n        throw new Error(`CodeGen: not in block \"${N2 ? `${N1.kind}/${N2.kind}` : N1.kind}\"`);\n    }\n    _elseNode(node) {\n        const n = this._currNode;\n        if (!(n instanceof If)) {\n            throw new Error('CodeGen: \"else\" without \"if\"');\n        }\n        this._currNode = n.else = node;\n        return this;\n    }\n    get _root() {\n        return this._nodes[0];\n    }\n    get _currNode() {\n        const ns = this._nodes;\n        return ns[ns.length - 1];\n    }\n    set _currNode(node) {\n        const ns = this._nodes;\n        ns[ns.length - 1] = node;\n    }\n}\nexports.CodeGen = CodeGen;\nfunction addNames(names, from) {\n    for (const n in from)\n        names[n] = (names[n] || 0) + (from[n] || 0);\n    return names;\n}\nfunction addExprNames(names, from) {\n    return from instanceof code_1._CodeOrName ? addNames(names, from.names) : names;\n}\nfunction optimizeExpr(expr, names, constants) {\n    if (expr instanceof code_1.Name)\n        return replaceName(expr);\n    if (!canOptimize(expr))\n        return expr;\n    return new code_1._Code(expr._items.reduce((items, c) => {\n        if (c instanceof code_1.Name)\n            c = replaceName(c);\n        if (c instanceof code_1._Code)\n            items.push(...c._items);\n        else\n            items.push(c);\n        return items;\n    }, []));\n    function replaceName(n) {\n        const c = constants[n.str];\n        if (c === undefined || names[n.str] !== 1)\n            return n;\n        delete names[n.str];\n        return c;\n    }\n    function canOptimize(e) {\n        return (e instanceof code_1._Code &&\n            e._items.some((c) => c instanceof code_1.Name && names[c.str] === 1 && constants[c.str] !== undefined));\n    }\n}\nfunction subtractNames(names, from) {\n    for (const n in from)\n        names[n] = (names[n] || 0) - (from[n] || 0);\n}\nfunction not(x) {\n    return typeof x == \"boolean\" || typeof x == \"number\" || x === null ? !x : (0, code_1._) `!${par(x)}`;\n}\nexports.not = not;\nconst andCode = mappend(exports.operators.AND);\n// boolean AND (&&) expression with the passed arguments\nfunction and(...args) {\n    return args.reduce(andCode);\n}\nexports.and = and;\nconst orCode = mappend(exports.operators.OR);\n// boolean OR (||) expression with the passed arguments\nfunction or(...args) {\n    return args.reduce(orCode);\n}\nexports.or = or;\nfunction mappend(op) {\n    return (x, y) => (x === code_1.nil ? y : y === code_1.nil ? x : (0, code_1._) `${par(x)} ${op} ${par(y)}`);\n}\nfunction par(x) {\n    return x instanceof code_1.Name ? x : (0, code_1._) `(${x})`;\n}\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ValueScope = exports.ValueScopeName = exports.Scope = exports.varKinds = exports.UsedValueState = void 0;\nconst code_1 = require(\"./code\");\nclass ValueError extends Error {\n    constructor(name) {\n        super(`CodeGen: \"code\" for ${name} not defined`);\n        this.value = name.value;\n    }\n}\nvar UsedValueState;\n(function (UsedValueState) {\n    UsedValueState[UsedValueState[\"Started\"] = 0] = \"Started\";\n    UsedValueState[UsedValueState[\"Completed\"] = 1] = \"Completed\";\n})(UsedValueState || (exports.UsedValueState = UsedValueState = {}));\nexports.varKinds = {\n    const: new code_1.Name(\"const\"),\n    let: new code_1.Name(\"let\"),\n    var: new code_1.Name(\"var\"),\n};\nclass Scope {\n    constructor({ prefixes, parent } = {}) {\n        this._names = {};\n        this._prefixes = prefixes;\n        this._parent = parent;\n    }\n    toName(nameOrPrefix) {\n        return nameOrPrefix instanceof code_1.Name ? nameOrPrefix : this.name(nameOrPrefix);\n    }\n    name(prefix) {\n        return new code_1.Name(this._newName(prefix));\n    }\n    _newName(prefix) {\n        const ng = this._names[prefix] || this._nameGroup(prefix);\n        return `${prefix}${ng.index++}`;\n    }\n    _nameGroup(prefix) {\n        var _a, _b;\n        if (((_b = (_a = this._parent) === null || _a === void 0 ? void 0 : _a._prefixes) === null || _b === void 0 ? void 0 : _b.has(prefix)) || (this._prefixes && !this._prefixes.has(prefix))) {\n            throw new Error(`CodeGen: prefix \"${prefix}\" is not allowed in this scope`);\n        }\n        return (this._names[prefix] = { prefix, index: 0 });\n    }\n}\nexports.Scope = Scope;\nclass ValueScopeName extends code_1.Name {\n    constructor(prefix, nameStr) {\n        super(nameStr);\n        this.prefix = prefix;\n    }\n    setValue(value, { property, itemIndex }) {\n        this.value = value;\n        this.scopePath = (0, code_1._) `.${new code_1.Name(property)}[${itemIndex}]`;\n    }\n}\nexports.ValueScopeName = ValueScopeName;\nconst line = (0, code_1._) `\\n`;\nclass ValueScope extends Scope {\n    constructor(opts) {\n        super(opts);\n        this._values = {};\n        this._scope = opts.scope;\n        this.opts = { ...opts, _n: opts.lines ? line : code_1.nil };\n    }\n    get() {\n        return this._scope;\n    }\n    name(prefix) {\n        return new ValueScopeName(prefix, this._newName(prefix));\n    }\n    value(nameOrPrefix, value) {\n        var _a;\n        if (value.ref === undefined)\n            throw new Error(\"CodeGen: ref must be passed in value\");\n        const name = this.toName(nameOrPrefix);\n        const { prefix } = name;\n        const valueKey = (_a = value.key) !== null && _a !== void 0 ? _a : value.ref;\n        let vs = this._values[prefix];\n        if (vs) {\n            const _name = vs.get(valueKey);\n            if (_name)\n                return _name;\n        }\n        else {\n            vs = this._values[prefix] = new Map();\n        }\n        vs.set(valueKey, name);\n        const s = this._scope[prefix] || (this._scope[prefix] = []);\n        const itemIndex = s.length;\n        s[itemIndex] = value.ref;\n        name.setValue(value, { property: prefix, itemIndex });\n        return name;\n    }\n    getValue(prefix, keyOrRef) {\n        const vs = this._values[prefix];\n        if (!vs)\n            return;\n        return vs.get(keyOrRef);\n    }\n    scopeRefs(scopeName, values = this._values) {\n        return this._reduceValues(values, (name) => {\n            if (name.scopePath === undefined)\n                throw new Error(`CodeGen: name \"${name}\" has no value`);\n            return (0, code_1._) `${scopeName}${name.scopePath}`;\n        });\n    }\n    scopeCode(values = this._values, usedValues, getCode) {\n        return this._reduceValues(values, (name) => {\n            if (name.value === undefined)\n                throw new Error(`CodeGen: name \"${name}\" has no value`);\n            return name.value.code;\n        }, usedValues, getCode);\n    }\n    _reduceValues(values, valueCode, usedValues = {}, getCode) {\n        let code = code_1.nil;\n        for (const prefix in values) {\n            const vs = values[prefix];\n            if (!vs)\n                continue;\n            const nameSet = (usedValues[prefix] = usedValues[prefix] || new Map());\n            vs.forEach((name) => {\n                if (nameSet.has(name))\n                    return;\n                nameSet.set(name, UsedValueState.Started);\n                let c = valueCode(name);\n                if (c) {\n                    const def = this.opts.es5 ? exports.varKinds.var : exports.varKinds.const;\n                    code = (0, code_1._) `${code}${def} ${name} = ${c};${this.opts._n}`;\n                }\n                else if ((c = getCode === null || getCode === void 0 ? void 0 : getCode(name))) {\n                    code = (0, code_1._) `${code}${c}${this.opts._n}`;\n                }\n                else {\n                    throw new ValueError(name);\n                }\n                nameSet.set(name, UsedValueState.Completed);\n            });\n        }\n        return code;\n    }\n}\nexports.ValueScope = ValueScope;\n//# sourceMappingURL=scope.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.extendErrors = exports.resetErrorsCount = exports.reportExtraError = exports.reportError = exports.keyword$DataError = exports.keywordError = void 0;\nconst codegen_1 = require(\"./codegen\");\nconst util_1 = require(\"./util\");\nconst names_1 = require(\"./names\");\nexports.keywordError = {\n    message: ({ keyword }) => (0, codegen_1.str) `must pass \"${keyword}\" keyword validation`,\n};\nexports.keyword$DataError = {\n    message: ({ keyword, schemaType }) => schemaType\n        ? (0, codegen_1.str) `\"${keyword}\" keyword must be ${schemaType} ($data)`\n        : (0, codegen_1.str) `\"${keyword}\" keyword is invalid ($data)`,\n};\nfunction reportError(cxt, error = exports.keywordError, errorPaths, overrideAllErrors) {\n    const { it } = cxt;\n    const { gen, compositeRule, allErrors } = it;\n    const errObj = errorObjectCode(cxt, error, errorPaths);\n    if (overrideAllErrors !== null && overrideAllErrors !== void 0 ? overrideAllErrors : (compositeRule || allErrors)) {\n        addError(gen, errObj);\n    }\n    else {\n        returnErrors(it, (0, codegen_1._) `[${errObj}]`);\n    }\n}\nexports.reportError = reportError;\nfunction reportExtraError(cxt, error = exports.keywordError, errorPaths) {\n    const { it } = cxt;\n    const { gen, compositeRule, allErrors } = it;\n    const errObj = errorObjectCode(cxt, error, errorPaths);\n    addError(gen, errObj);\n    if (!(compositeRule || allErrors)) {\n        returnErrors(it, names_1.default.vErrors);\n    }\n}\nexports.reportExtraError = reportExtraError;\nfunction resetErrorsCount(gen, errsCount) {\n    gen.assign(names_1.default.errors, errsCount);\n    gen.if((0, codegen_1._) `${names_1.default.vErrors} !== null`, () => gen.if(errsCount, () => gen.assign((0, codegen_1._) `${names_1.default.vErrors}.length`, errsCount), () => gen.assign(names_1.default.vErrors, null)));\n}\nexports.resetErrorsCount = resetErrorsCount;\nfunction extendErrors({ gen, keyword, schemaValue, data, errsCount, it, }) {\n    /* istanbul ignore if */\n    if (errsCount === undefined)\n        throw new Error(\"ajv implementation error\");\n    const err = gen.name(\"err\");\n    gen.forRange(\"i\", errsCount, names_1.default.errors, (i) => {\n        gen.const(err, (0, codegen_1._) `${names_1.default.vErrors}[${i}]`);\n        gen.if((0, codegen_1._) `${err}.instancePath === undefined`, () => gen.assign((0, codegen_1._) `${err}.instancePath`, (0, codegen_1.strConcat)(names_1.default.instancePath, it.errorPath)));\n        gen.assign((0, codegen_1._) `${err}.schemaPath`, (0, codegen_1.str) `${it.errSchemaPath}/${keyword}`);\n        if (it.opts.verbose) {\n            gen.assign((0, codegen_1._) `${err}.schema`, schemaValue);\n            gen.assign((0, codegen_1._) `${err}.data`, data);\n        }\n    });\n}\nexports.extendErrors = extendErrors;\nfunction addError(gen, errObj) {\n    const err = gen.const(\"err\", errObj);\n    gen.if((0, codegen_1._) `${names_1.default.vErrors} === null`, () => gen.assign(names_1.default.vErrors, (0, codegen_1._) `[${err}]`), (0, codegen_1._) `${names_1.default.vErrors}.push(${err})`);\n    gen.code((0, codegen_1._) `${names_1.default.errors}++`);\n}\nfunction returnErrors(it, errs) {\n    const { gen, validateName, schemaEnv } = it;\n    if (schemaEnv.$async) {\n        gen.throw((0, codegen_1._) `new ${it.ValidationError}(${errs})`);\n    }\n    else {\n        gen.assign((0, codegen_1._) `${validateName}.errors`, errs);\n        gen.return(false);\n    }\n}\nconst E = {\n    keyword: new codegen_1.Name(\"keyword\"),\n    schemaPath: new codegen_1.Name(\"schemaPath\"), // also used in JTD errors\n    params: new codegen_1.Name(\"params\"),\n    propertyName: new codegen_1.Name(\"propertyName\"),\n    message: new codegen_1.Name(\"message\"),\n    schema: new codegen_1.Name(\"schema\"),\n    parentSchema: new codegen_1.Name(\"parentSchema\"),\n};\nfunction errorObjectCode(cxt, error, errorPaths) {\n    const { createErrors } = cxt.it;\n    if (createErrors === false)\n        return (0, codegen_1._) `{}`;\n    return errorObject(cxt, error, errorPaths);\n}\nfunction errorObject(cxt, error, errorPaths = {}) {\n    const { gen, it } = cxt;\n    const keyValues = [\n        errorInstancePath(it, errorPaths),\n        errorSchemaPath(cxt, errorPaths),\n    ];\n    extraErrorProps(cxt, error, keyValues);\n    return gen.object(...keyValues);\n}\nfunction errorInstancePath({ errorPath }, { instancePath }) {\n    const instPath = instancePath\n        ? (0, codegen_1.str) `${errorPath}${(0, util_1.getErrorPath)(instancePath, util_1.Type.Str)}`\n        : errorPath;\n    return [names_1.default.instancePath, (0, codegen_1.strConcat)(names_1.default.instancePath, instPath)];\n}\nfunction errorSchemaPath({ keyword, it: { errSchemaPath } }, { schemaPath, parentSchema }) {\n    let schPath = parentSchema ? errSchemaPath : (0, codegen_1.str) `${errSchemaPath}/${keyword}`;\n    if (schemaPath) {\n        schPath = (0, codegen_1.str) `${schPath}${(0, util_1.getErrorPath)(schemaPath, util_1.Type.Str)}`;\n    }\n    return [E.schemaPath, schPath];\n}\nfunction extraErrorProps(cxt, { params, message }, keyValues) {\n    const { keyword, data, schemaValue, it } = cxt;\n    const { opts, propertyName, topSchemaRef, schemaPath } = it;\n    keyValues.push([E.keyword, keyword], [E.params, typeof params == \"function\" ? params(cxt) : params || (0, codegen_1._) `{}`]);\n    if (opts.messages) {\n        keyValues.push([E.message, typeof message == \"function\" ? message(cxt) : message]);\n    }\n    if (opts.verbose) {\n        keyValues.push([E.schema, schemaValue], [E.parentSchema, (0, codegen_1._) `${topSchemaRef}${schemaPath}`], [names_1.default.data, data]);\n    }\n    if (propertyName)\n        keyValues.push([E.propertyName, propertyName]);\n}\n//# sourceMappingURL=errors.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.resolveSchema = exports.getCompilingSchema = exports.resolveRef = exports.compileSchema = exports.SchemaEnv = void 0;\nconst codegen_1 = require(\"./codegen\");\nconst validation_error_1 = require(\"../runtime/validation_error\");\nconst names_1 = require(\"./names\");\nconst resolve_1 = require(\"./resolve\");\nconst util_1 = require(\"./util\");\nconst validate_1 = require(\"./validate\");\nclass SchemaEnv {\n    constructor(env) {\n        var _a;\n        this.refs = {};\n        this.dynamicAnchors = {};\n        let schema;\n        if (typeof env.schema == \"object\")\n            schema = env.schema;\n        this.schema = env.schema;\n        this.schemaId = env.schemaId;\n        this.root = env.root || this;\n        this.baseId = (_a = env.baseId) !== null && _a !== void 0 ? _a : (0, resolve_1.normalizeId)(schema === null || schema === void 0 ? void 0 : schema[env.schemaId || \"$id\"]);\n        this.schemaPath = env.schemaPath;\n        this.localRefs = env.localRefs;\n        this.meta = env.meta;\n        this.$async = schema === null || schema === void 0 ? void 0 : schema.$async;\n        this.refs = {};\n    }\n}\nexports.SchemaEnv = SchemaEnv;\n// let codeSize = 0\n// let nodeCount = 0\n// Compiles schema in SchemaEnv\nfunction compileSchema(sch) {\n    // TODO refactor - remove compilations\n    const _sch = getCompilingSchema.call(this, sch);\n    if (_sch)\n        return _sch;\n    const rootId = (0, resolve_1.getFullPath)(this.opts.uriResolver, sch.root.baseId); // TODO if getFullPath removed 1 tests fails\n    const { es5, lines } = this.opts.code;\n    const { ownProperties } = this.opts;\n    const gen = new codegen_1.CodeGen(this.scope, { es5, lines, ownProperties });\n    let _ValidationError;\n    if (sch.$async) {\n        _ValidationError = gen.scopeValue(\"Error\", {\n            ref: validation_error_1.default,\n            code: (0, codegen_1._) `require(\"ajv/dist/runtime/validation_error\").default`,\n        });\n    }\n    const validateName = gen.scopeName(\"validate\");\n    sch.validateName = validateName;\n    const schemaCxt = {\n        gen,\n        allErrors: this.opts.allErrors,\n        data: names_1.default.data,\n        parentData: names_1.default.parentData,\n        parentDataProperty: names_1.default.parentDataProperty,\n        dataNames: [names_1.default.data],\n        dataPathArr: [codegen_1.nil], // TODO can its length be used as dataLevel if nil is removed?\n        dataLevel: 0,\n        dataTypes: [],\n        definedProperties: new Set(),\n        topSchemaRef: gen.scopeValue(\"schema\", this.opts.code.source === true\n            ? { ref: sch.schema, code: (0, codegen_1.stringify)(sch.schema) }\n            : { ref: sch.schema }),\n        validateName,\n        ValidationError: _ValidationError,\n        schema: sch.schema,\n        schemaEnv: sch,\n        rootId,\n        baseId: sch.baseId || rootId,\n        schemaPath: codegen_1.nil,\n        errSchemaPath: sch.schemaPath || (this.opts.jtd ? \"\" : \"#\"),\n        errorPath: (0, codegen_1._) `\"\"`,\n        opts: this.opts,\n        self: this,\n    };\n    let sourceCode;\n    try {\n        this._compilations.add(sch);\n        (0, validate_1.validateFunctionCode)(schemaCxt);\n        gen.optimize(this.opts.code.optimize);\n        // gen.optimize(1)\n        const validateCode = gen.toString();\n        sourceCode = `${gen.scopeRefs(names_1.default.scope)}return ${validateCode}`;\n        // console.log((codeSize += sourceCode.length), (nodeCount += gen.nodeCount))\n        if (this.opts.code.process)\n            sourceCode = this.opts.code.process(sourceCode, sch);\n        // console.log(\"\\n\\n\\n *** \\n\", sourceCode)\n        const makeValidate = new Function(`${names_1.default.self}`, `${names_1.default.scope}`, sourceCode);\n        const validate = makeValidate(this, this.scope.get());\n        this.scope.value(validateName, { ref: validate });\n        validate.errors = null;\n        validate.schema = sch.schema;\n        validate.schemaEnv = sch;\n        if (sch.$async)\n            validate.$async = true;\n        if (this.opts.code.source === true) {\n            validate.source = { validateName, validateCode, scopeValues: gen._values };\n        }\n        if (this.opts.unevaluated) {\n            const { props, items } = schemaCxt;\n            validate.evaluated = {\n                props: props instanceof codegen_1.Name ? undefined : props,\n                items: items instanceof codegen_1.Name ? undefined : items,\n                dynamicProps: props instanceof codegen_1.Name,\n                dynamicItems: items instanceof codegen_1.Name,\n            };\n            if (validate.source)\n                validate.source.evaluated = (0, codegen_1.stringify)(validate.evaluated);\n        }\n        sch.validate = validate;\n        return sch;\n    }\n    catch (e) {\n        delete sch.validate;\n        delete sch.validateName;\n        if (sourceCode)\n            this.logger.error(\"Error compiling schema, function code:\", sourceCode);\n        // console.log(\"\\n\\n\\n *** \\n\", sourceCode, this.opts)\n        throw e;\n    }\n    finally {\n        this._compilations.delete(sch);\n    }\n}\nexports.compileSchema = compileSchema;\nfunction resolveRef(root, baseId, ref) {\n    var _a;\n    ref = (0, resolve_1.resolveUrl)(this.opts.uriResolver, baseId, ref);\n    const schOrFunc = root.refs[ref];\n    if (schOrFunc)\n        return schOrFunc;\n    let _sch = resolve.call(this, root, ref);\n    if (_sch === undefined) {\n        const schema = (_a = root.localRefs) === null || _a === void 0 ? void 0 : _a[ref]; // TODO maybe localRefs should hold SchemaEnv\n        const { schemaId } = this.opts;\n        if (schema)\n            _sch = new SchemaEnv({ schema, schemaId, root, baseId });\n    }\n    if (_sch === undefined)\n        return;\n    return (root.refs[ref] = inlineOrCompile.call(this, _sch));\n}\nexports.resolveRef = resolveRef;\nfunction inlineOrCompile(sch) {\n    if ((0, resolve_1.inlineRef)(sch.schema, this.opts.inlineRefs))\n        return sch.schema;\n    return sch.validate ? sch : compileSchema.call(this, sch);\n}\n// Index of schema compilation in the currently compiled list\nfunction getCompilingSchema(schEnv) {\n    for (const sch of this._compilations) {\n        if (sameSchemaEnv(sch, schEnv))\n            return sch;\n    }\n}\nexports.getCompilingSchema = getCompilingSchema;\nfunction sameSchemaEnv(s1, s2) {\n    return s1.schema === s2.schema && s1.root === s2.root && s1.baseId === s2.baseId;\n}\n// resolve and compile the references ($ref)\n// TODO returns AnySchemaObject (if the schema can be inlined) or validation function\nfunction resolve(root, // information about the root schema for the current schema\nref // reference to resolve\n) {\n    let sch;\n    while (typeof (sch = this.refs[ref]) == \"string\")\n        ref = sch;\n    return sch || this.schemas[ref] || resolveSchema.call(this, root, ref);\n}\n// Resolve schema, its root and baseId\nfunction resolveSchema(root, // root object with properties schema, refs TODO below SchemaEnv is assigned to it\nref // reference to resolve\n) {\n    const p = this.opts.uriResolver.parse(ref);\n    const refPath = (0, resolve_1._getFullPath)(this.opts.uriResolver, p);\n    let baseId = (0, resolve_1.getFullPath)(this.opts.uriResolver, root.baseId, undefined);\n    // TODO `Object.keys(root.schema).length > 0` should not be needed - but removing breaks 2 tests\n    if (Object.keys(root.schema).length > 0 && refPath === baseId) {\n        return getJsonPointer.call(this, p, root);\n    }\n    const id = (0, resolve_1.normalizeId)(refPath);\n    const schOrRef = this.refs[id] || this.schemas[id];\n    if (typeof schOrRef == \"string\") {\n        const sch = resolveSchema.call(this, root, schOrRef);\n        if (typeof (sch === null || sch === void 0 ? void 0 : sch.schema) !== \"object\")\n            return;\n        return getJsonPointer.call(this, p, sch);\n    }\n    if (typeof (schOrRef === null || schOrRef === void 0 ? void 0 : schOrRef.schema) !== \"object\")\n        return;\n    if (!schOrRef.validate)\n        compileSchema.call(this, schOrRef);\n    if (id === (0, resolve_1.normalizeId)(ref)) {\n        const { schema } = schOrRef;\n        const { schemaId } = this.opts;\n        const schId = schema[schemaId];\n        if (schId)\n            baseId = (0, resolve_1.resolveUrl)(this.opts.uriResolver, baseId, schId);\n        return new SchemaEnv({ schema, schemaId, root, baseId });\n    }\n    return getJsonPointer.call(this, p, schOrRef);\n}\nexports.resolveSchema = resolveSchema;\nconst PREVENT_SCOPE_CHANGE = new Set([\n    \"properties\",\n    \"patternProperties\",\n    \"enum\",\n    \"dependencies\",\n    \"definitions\",\n]);\nfunction getJsonPointer(parsedRef, { baseId, schema, root }) {\n    var _a;\n    if (((_a = parsedRef.fragment) === null || _a === void 0 ? void 0 : _a[0]) !== \"/\")\n        return;\n    for (const part of parsedRef.fragment.slice(1).split(\"/\")) {\n        if (typeof schema === \"boolean\")\n            return;\n        const partSchema = schema[(0, util_1.unescapeFragment)(part)];\n        if (partSchema === undefined)\n            return;\n        schema = partSchema;\n        // TODO PREVENT_SCOPE_CHANGE could be defined in keyword def?\n        const schId = typeof schema === \"object\" && schema[this.opts.schemaId];\n        if (!PREVENT_SCOPE_CHANGE.has(part) && schId) {\n            baseId = (0, resolve_1.resolveUrl)(this.opts.uriResolver, baseId, schId);\n        }\n    }\n    let env;\n    if (typeof schema != \"boolean\" && schema.$ref && !(0, util_1.schemaHasRulesButRef)(schema, this.RULES)) {\n        const $ref = (0, resolve_1.resolveUrl)(this.opts.uriResolver, baseId, schema.$ref);\n        env = resolveSchema.call(this, root, $ref);\n    }\n    // even though resolution failed we need to return SchemaEnv to throw exception\n    // so that compileAsync loads missing schema.\n    const { schemaId } = this.opts;\n    env = env || new SchemaEnv({ schema, schemaId, root, baseId });\n    if (env.schema !== env.root.schema)\n        return env;\n    return undefined;\n}\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"./codegen\");\nconst names = {\n    // validation function arguments\n    data: new codegen_1.Name(\"data\"), // data passed to validation function\n    // args passed from referencing schema\n    valCxt: new codegen_1.Name(\"valCxt\"), // validation/data context - should not be used directly, it is destructured to the names below\n    instancePath: new codegen_1.Name(\"instancePath\"),\n    parentData: new codegen_1.Name(\"parentData\"),\n    parentDataProperty: new codegen_1.Name(\"parentDataProperty\"),\n    rootData: new codegen_1.Name(\"rootData\"), // root data - same as the data passed to the first/top validation function\n    dynamicAnchors: new codegen_1.Name(\"dynamicAnchors\"), // used to support recursiveRef and dynamicRef\n    // function scoped variables\n    vErrors: new codegen_1.Name(\"vErrors\"), // null or array of validation errors\n    errors: new codegen_1.Name(\"errors\"), // counter of validation errors\n    this: new codegen_1.Name(\"this\"),\n    // \"globals\"\n    self: new codegen_1.Name(\"self\"),\n    scope: new codegen_1.Name(\"scope\"),\n    // JTD serialize/parse name for JSON string and position\n    json: new codegen_1.Name(\"json\"),\n    jsonPos: new codegen_1.Name(\"jsonPos\"),\n    jsonLen: new codegen_1.Name(\"jsonLen\"),\n    jsonPart: new codegen_1.Name(\"jsonPart\"),\n};\nexports.default = names;\n//# sourceMappingURL=names.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst resolve_1 = require(\"./resolve\");\nclass MissingRefError extends Error {\n    constructor(resolver, baseId, ref, msg) {\n        super(msg || `can't resolve reference ${ref} from id ${baseId}`);\n        this.missingRef = (0, resolve_1.resolveUrl)(resolver, baseId, ref);\n        this.missingSchema = (0, resolve_1.normalizeId)((0, resolve_1.getFullPath)(resolver, this.missingRef));\n    }\n}\nexports.default = MissingRefError;\n//# sourceMappingURL=ref_error.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getSchemaRefs = exports.resolveUrl = exports.normalizeId = exports._getFullPath = exports.getFullPath = exports.inlineRef = void 0;\nconst util_1 = require(\"./util\");\nconst equal = require(\"fast-deep-equal\");\nconst traverse = require(\"json-schema-traverse\");\n// TODO refactor to use keyword definitions\nconst SIMPLE_INLINED = new Set([\n    \"type\",\n    \"format\",\n    \"pattern\",\n    \"maxLength\",\n    \"minLength\",\n    \"maxProperties\",\n    \"minProperties\",\n    \"maxItems\",\n    \"minItems\",\n    \"maximum\",\n    \"minimum\",\n    \"uniqueItems\",\n    \"multipleOf\",\n    \"required\",\n    \"enum\",\n    \"const\",\n]);\nfunction inlineRef(schema, limit = true) {\n    if (typeof schema == \"boolean\")\n        return true;\n    if (limit === true)\n        return !hasRef(schema);\n    if (!limit)\n        return false;\n    return countKeys(schema) <= limit;\n}\nexports.inlineRef = inlineRef;\nconst REF_KEYWORDS = new Set([\n    \"$ref\",\n    \"$recursiveRef\",\n    \"$recursiveAnchor\",\n    \"$dynamicRef\",\n    \"$dynamicAnchor\",\n]);\nfunction hasRef(schema) {\n    for (const key in schema) {\n        if (REF_KEYWORDS.has(key))\n            return true;\n        const sch = schema[key];\n        if (Array.isArray(sch) && sch.some(hasRef))\n            return true;\n        if (typeof sch == \"object\" && hasRef(sch))\n            return true;\n    }\n    return false;\n}\nfunction countKeys(schema) {\n    let count = 0;\n    for (const key in schema) {\n        if (key === \"$ref\")\n            return Infinity;\n        count++;\n        if (SIMPLE_INLINED.has(key))\n            continue;\n        if (typeof schema[key] == \"object\") {\n            (0, util_1.eachItem)(schema[key], (sch) => (count += countKeys(sch)));\n        }\n        if (count === Infinity)\n            return Infinity;\n    }\n    return count;\n}\nfunction getFullPath(resolver, id = \"\", normalize) {\n    if (normalize !== false)\n        id = normalizeId(id);\n    const p = resolver.parse(id);\n    return _getFullPath(resolver, p);\n}\nexports.getFullPath = getFullPath;\nfunction _getFullPath(resolver, p) {\n    const serialized = resolver.serialize(p);\n    return serialized.split(\"#\")[0] + \"#\";\n}\nexports._getFullPath = _getFullPath;\nconst TRAILING_SLASH_HASH = /#\\/?$/;\nfunction normalizeId(id) {\n    return id ? id.replace(TRAILING_SLASH_HASH, \"\") : \"\";\n}\nexports.normalizeId = normalizeId;\nfunction resolveUrl(resolver, baseId, id) {\n    id = normalizeId(id);\n    return resolver.resolve(baseId, id);\n}\nexports.resolveUrl = resolveUrl;\nconst ANCHOR = /^[a-z_][-a-z0-9._]*$/i;\nfunction getSchemaRefs(schema, baseId) {\n    if (typeof schema == \"boolean\")\n        return {};\n    const { schemaId, uriResolver } = this.opts;\n    const schId = normalizeId(schema[schemaId] || baseId);\n    const baseIds = { \"\": schId };\n    const pathPrefix = getFullPath(uriResolver, schId, false);\n    const localRefs = {};\n    const schemaRefs = new Set();\n    traverse(schema, { allKeys: true }, (sch, jsonPtr, _, parentJsonPtr) => {\n        if (parentJsonPtr === undefined)\n            return;\n        const fullPath = pathPrefix + jsonPtr;\n        let innerBaseId = baseIds[parentJsonPtr];\n        if (typeof sch[schemaId] == \"string\")\n            innerBaseId = addRef.call(this, sch[schemaId]);\n        addAnchor.call(this, sch.$anchor);\n        addAnchor.call(this, sch.$dynamicAnchor);\n        baseIds[jsonPtr] = innerBaseId;\n        function addRef(ref) {\n            // eslint-disable-next-line @typescript-eslint/unbound-method\n            const _resolve = this.opts.uriResolver.resolve;\n            ref = normalizeId(innerBaseId ? _resolve(innerBaseId, ref) : ref);\n            if (schemaRefs.has(ref))\n                throw ambiguos(ref);\n            schemaRefs.add(ref);\n            let schOrRef = this.refs[ref];\n            if (typeof schOrRef == \"string\")\n                schOrRef = this.refs[schOrRef];\n            if (typeof schOrRef == \"object\") {\n                checkAmbiguosRef(sch, schOrRef.schema, ref);\n            }\n            else if (ref !== normalizeId(fullPath)) {\n                if (ref[0] === \"#\") {\n                    checkAmbiguosRef(sch, localRefs[ref], ref);\n                    localRefs[ref] = sch;\n                }\n                else {\n                    this.refs[ref] = fullPath;\n                }\n            }\n            return ref;\n        }\n        function addAnchor(anchor) {\n            if (typeof anchor == \"string\") {\n                if (!ANCHOR.test(anchor))\n                    throw new Error(`invalid anchor \"${anchor}\"`);\n                addRef.call(this, `#${anchor}`);\n            }\n        }\n    });\n    return localRefs;\n    function checkAmbiguosRef(sch1, sch2, ref) {\n        if (sch2 !== undefined && !equal(sch1, sch2))\n            throw ambiguos(ref);\n    }\n    function ambiguos(ref) {\n        return new Error(`reference \"${ref}\" resolves to more than one schema`);\n    }\n}\nexports.getSchemaRefs = getSchemaRefs;\n//# sourceMappingURL=resolve.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getRules = exports.isJSONType = void 0;\nconst _jsonTypes = [\"string\", \"number\", \"integer\", \"boolean\", \"null\", \"object\", \"array\"];\nconst jsonTypes = new Set(_jsonTypes);\nfunction isJSONType(x) {\n    return typeof x == \"string\" && jsonTypes.has(x);\n}\nexports.isJSONType = isJSONType;\nfunction getRules() {\n    const groups = {\n        number: { type: \"number\", rules: [] },\n        string: { type: \"string\", rules: [] },\n        array: { type: \"array\", rules: [] },\n        object: { type: \"object\", rules: [] },\n    };\n    return {\n        types: { ...groups, integer: true, boolean: true, null: true },\n        rules: [{ rules: [] }, groups.number, groups.string, groups.array, groups.object],\n        post: { rules: [] },\n        all: {},\n        keywords: {},\n    };\n}\nexports.getRules = getRules;\n//# sourceMappingURL=rules.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.checkStrictMode = exports.getErrorPath = exports.Type = exports.useFunc = exports.setEvaluated = exports.evaluatedPropsToName = exports.mergeEvaluated = exports.eachItem = exports.unescapeJsonPointer = exports.escapeJsonPointer = exports.escapeFragment = exports.unescapeFragment = exports.schemaRefOrVal = exports.schemaHasRulesButRef = exports.schemaHasRules = exports.checkUnknownRules = exports.alwaysValidSchema = exports.toHash = void 0;\nconst codegen_1 = require(\"./codegen\");\nconst code_1 = require(\"./codegen/code\");\n// TODO refactor to use Set\nfunction toHash(arr) {\n    const hash = {};\n    for (const item of arr)\n        hash[item] = true;\n    return hash;\n}\nexports.toHash = toHash;\nfunction alwaysValidSchema(it, schema) {\n    if (typeof schema == \"boolean\")\n        return schema;\n    if (Object.keys(schema).length === 0)\n        return true;\n    checkUnknownRules(it, schema);\n    return !schemaHasRules(schema, it.self.RULES.all);\n}\nexports.alwaysValidSchema = alwaysValidSchema;\nfunction checkUnknownRules(it, schema = it.schema) {\n    const { opts, self } = it;\n    if (!opts.strictSchema)\n        return;\n    if (typeof schema === \"boolean\")\n        return;\n    const rules = self.RULES.keywords;\n    for (const key in schema) {\n        if (!rules[key])\n            checkStrictMode(it, `unknown keyword: \"${key}\"`);\n    }\n}\nexports.checkUnknownRules = checkUnknownRules;\nfunction schemaHasRules(schema, rules) {\n    if (typeof schema == \"boolean\")\n        return !schema;\n    for (const key in schema)\n        if (rules[key])\n            return true;\n    return false;\n}\nexports.schemaHasRules = schemaHasRules;\nfunction schemaHasRulesButRef(schema, RULES) {\n    if (typeof schema == \"boolean\")\n        return !schema;\n    for (const key in schema)\n        if (key !== \"$ref\" && RULES.all[key])\n            return true;\n    return false;\n}\nexports.schemaHasRulesButRef = schemaHasRulesButRef;\nfunction schemaRefOrVal({ topSchemaRef, schemaPath }, schema, keyword, $data) {\n    if (!$data) {\n        if (typeof schema == \"number\" || typeof schema == \"boolean\")\n            return schema;\n        if (typeof schema == \"string\")\n            return (0, codegen_1._) `${schema}`;\n    }\n    return (0, codegen_1._) `${topSchemaRef}${schemaPath}${(0, codegen_1.getProperty)(keyword)}`;\n}\nexports.schemaRefOrVal = schemaRefOrVal;\nfunction unescapeFragment(str) {\n    return unescapeJsonPointer(decodeURIComponent(str));\n}\nexports.unescapeFragment = unescapeFragment;\nfunction escapeFragment(str) {\n    return encodeURIComponent(escapeJsonPointer(str));\n}\nexports.escapeFragment = escapeFragment;\nfunction escapeJsonPointer(str) {\n    if (typeof str == \"number\")\n        return `${str}`;\n    return str.replace(/~/g, \"~0\").replace(/\\//g, \"~1\");\n}\nexports.escapeJsonPointer = escapeJsonPointer;\nfunction unescapeJsonPointer(str) {\n    return str.replace(/~1/g, \"/\").replace(/~0/g, \"~\");\n}\nexports.unescapeJsonPointer = unescapeJsonPointer;\nfunction eachItem(xs, f) {\n    if (Array.isArray(xs)) {\n        for (const x of xs)\n            f(x);\n    }\n    else {\n        f(xs);\n    }\n}\nexports.eachItem = eachItem;\nfunction makeMergeEvaluated({ mergeNames, mergeToName, mergeValues, resultToName, }) {\n    return (gen, from, to, toName) => {\n        const res = to === undefined\n            ? from\n            : to instanceof codegen_1.Name\n                ? (from instanceof codegen_1.Name ? mergeNames(gen, from, to) : mergeToName(gen, from, to), to)\n                : from instanceof codegen_1.Name\n                    ? (mergeToName(gen, to, from), from)\n                    : mergeValues(from, to);\n        return toName === codegen_1.Name && !(res instanceof codegen_1.Name) ? resultToName(gen, res) : res;\n    };\n}\nexports.mergeEvaluated = {\n    props: makeMergeEvaluated({\n        mergeNames: (gen, from, to) => gen.if((0, codegen_1._) `${to} !== true && ${from} !== undefined`, () => {\n            gen.if((0, codegen_1._) `${from} === true`, () => gen.assign(to, true), () => gen.assign(to, (0, codegen_1._) `${to} || {}`).code((0, codegen_1._) `Object.assign(${to}, ${from})`));\n        }),\n        mergeToName: (gen, from, to) => gen.if((0, codegen_1._) `${to} !== true`, () => {\n            if (from === true) {\n                gen.assign(to, true);\n            }\n            else {\n                gen.assign(to, (0, codegen_1._) `${to} || {}`);\n                setEvaluated(gen, to, from);\n            }\n        }),\n        mergeValues: (from, to) => (from === true ? true : { ...from, ...to }),\n        resultToName: evaluatedPropsToName,\n    }),\n    items: makeMergeEvaluated({\n        mergeNames: (gen, from, to) => gen.if((0, codegen_1._) `${to} !== true && ${from} !== undefined`, () => gen.assign(to, (0, codegen_1._) `${from} === true ? true : ${to} > ${from} ? ${to} : ${from}`)),\n        mergeToName: (gen, from, to) => gen.if((0, codegen_1._) `${to} !== true`, () => gen.assign(to, from === true ? true : (0, codegen_1._) `${to} > ${from} ? ${to} : ${from}`)),\n        mergeValues: (from, to) => (from === true ? true : Math.max(from, to)),\n        resultToName: (gen, items) => gen.var(\"items\", items),\n    }),\n};\nfunction evaluatedPropsToName(gen, ps) {\n    if (ps === true)\n        return gen.var(\"props\", true);\n    const props = gen.var(\"props\", (0, codegen_1._) `{}`);\n    if (ps !== undefined)\n        setEvaluated(gen, props, ps);\n    return props;\n}\nexports.evaluatedPropsToName = evaluatedPropsToName;\nfunction setEvaluated(gen, props, ps) {\n    Object.keys(ps).forEach((p) => gen.assign((0, codegen_1._) `${props}${(0, codegen_1.getProperty)(p)}`, true));\n}\nexports.setEvaluated = setEvaluated;\nconst snippets = {};\nfunction useFunc(gen, f) {\n    return gen.scopeValue(\"func\", {\n        ref: f,\n        code: snippets[f.code] || (snippets[f.code] = new code_1._Code(f.code)),\n    });\n}\nexports.useFunc = useFunc;\nvar Type;\n(function (Type) {\n    Type[Type[\"Num\"] = 0] = \"Num\";\n    Type[Type[\"Str\"] = 1] = \"Str\";\n})(Type || (exports.Type = Type = {}));\nfunction getErrorPath(dataProp, dataPropType, jsPropertySyntax) {\n    // let path\n    if (dataProp instanceof codegen_1.Name) {\n        const isNumber = dataPropType === Type.Num;\n        return jsPropertySyntax\n            ? isNumber\n                ? (0, codegen_1._) `\"[\" + ${dataProp} + \"]\"`\n                : (0, codegen_1._) `\"['\" + ${dataProp} + \"']\"`\n            : isNumber\n                ? (0, codegen_1._) `\"/\" + ${dataProp}`\n                : (0, codegen_1._) `\"/\" + ${dataProp}.replace(/~/g, \"~0\").replace(/\\\\//g, \"~1\")`; // TODO maybe use global escapePointer\n    }\n    return jsPropertySyntax ? (0, codegen_1.getProperty)(dataProp).toString() : \"/\" + escapeJsonPointer(dataProp);\n}\nexports.getErrorPath = getErrorPath;\nfunction checkStrictMode(it, msg, mode = it.opts.strictSchema) {\n    if (!mode)\n        return;\n    msg = `strict mode: ${msg}`;\n    if (mode === true)\n        throw new Error(msg);\n    it.self.logger.warn(msg);\n}\nexports.checkStrictMode = checkStrictMode;\n//# sourceMappingURL=util.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.shouldUseRule = exports.shouldUseGroup = exports.schemaHasRulesForType = void 0;\nfunction schemaHasRulesForType({ schema, self }, type) {\n    const group = self.RULES.types[type];\n    return group && group !== true && shouldUseGroup(schema, group);\n}\nexports.schemaHasRulesForType = schemaHasRulesForType;\nfunction shouldUseGroup(schema, group) {\n    return group.rules.some((rule) => shouldUseRule(schema, rule));\n}\nexports.shouldUseGroup = shouldUseGroup;\nfunction shouldUseRule(schema, rule) {\n    var _a;\n    return (schema[rule.keyword] !== undefined ||\n        ((_a = rule.definition.implements) === null || _a === void 0 ? void 0 : _a.some((kwd) => schema[kwd] !== undefined)));\n}\nexports.shouldUseRule = shouldUseRule;\n//# sourceMappingURL=applicability.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.boolOrEmptySchema = exports.topBoolOrEmptySchema = void 0;\nconst errors_1 = require(\"../errors\");\nconst codegen_1 = require(\"../codegen\");\nconst names_1 = require(\"../names\");\nconst boolError = {\n    message: \"boolean schema is false\",\n};\nfunction topBoolOrEmptySchema(it) {\n    const { gen, schema, validateName } = it;\n    if (schema === false) {\n        falseSchemaError(it, false);\n    }\n    else if (typeof schema == \"object\" && schema.$async === true) {\n        gen.return(names_1.default.data);\n    }\n    else {\n        gen.assign((0, codegen_1._) `${validateName}.errors`, null);\n        gen.return(true);\n    }\n}\nexports.topBoolOrEmptySchema = topBoolOrEmptySchema;\nfunction boolOrEmptySchema(it, valid) {\n    const { gen, schema } = it;\n    if (schema === false) {\n        gen.var(valid, false); // TODO var\n        falseSchemaError(it);\n    }\n    else {\n        gen.var(valid, true); // TODO var\n    }\n}\nexports.boolOrEmptySchema = boolOrEmptySchema;\nfunction falseSchemaError(it, overrideAllErrors) {\n    const { gen, data } = it;\n    // TODO maybe some other interface should be used for non-keyword validation errors...\n    const cxt = {\n        gen,\n        keyword: \"false schema\",\n        data,\n        schema: false,\n        schemaCode: false,\n        schemaValue: false,\n        params: {},\n        it,\n    };\n    (0, errors_1.reportError)(cxt, boolError, undefined, overrideAllErrors);\n}\n//# sourceMappingURL=boolSchema.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.reportTypeError = exports.checkDataTypes = exports.checkDataType = exports.coerceAndCheckDataType = exports.getJSONTypes = exports.getSchemaTypes = exports.DataType = void 0;\nconst rules_1 = require(\"../rules\");\nconst applicability_1 = require(\"./applicability\");\nconst errors_1 = require(\"../errors\");\nconst codegen_1 = require(\"../codegen\");\nconst util_1 = require(\"../util\");\nvar DataType;\n(function (DataType) {\n    DataType[DataType[\"Correct\"] = 0] = \"Correct\";\n    DataType[DataType[\"Wrong\"] = 1] = \"Wrong\";\n})(DataType || (exports.DataType = DataType = {}));\nfunction getSchemaTypes(schema) {\n    const types = getJSONTypes(schema.type);\n    const hasNull = types.includes(\"null\");\n    if (hasNull) {\n        if (schema.nullable === false)\n            throw new Error(\"type: null contradicts nullable: false\");\n    }\n    else {\n        if (!types.length && schema.nullable !== undefined) {\n            throw new Error('\"nullable\" cannot be used without \"type\"');\n        }\n        if (schema.nullable === true)\n            types.push(\"null\");\n    }\n    return types;\n}\nexports.getSchemaTypes = getSchemaTypes;\n// eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents\nfunction getJSONTypes(ts) {\n    const types = Array.isArray(ts) ? ts : ts ? [ts] : [];\n    if (types.every(rules_1.isJSONType))\n        return types;\n    throw new Error(\"type must be JSONType or JSONType[]: \" + types.join(\",\"));\n}\nexports.getJSONTypes = getJSONTypes;\nfunction coerceAndCheckDataType(it, types) {\n    const { gen, data, opts } = it;\n    const coerceTo = coerceToTypes(types, opts.coerceTypes);\n    const checkTypes = types.length > 0 &&\n        !(coerceTo.length === 0 && types.length === 1 && (0, applicability_1.schemaHasRulesForType)(it, types[0]));\n    if (checkTypes) {\n        const wrongType = checkDataTypes(types, data, opts.strictNumbers, DataType.Wrong);\n        gen.if(wrongType, () => {\n            if (coerceTo.length)\n                coerceData(it, types, coerceTo);\n            else\n                reportTypeError(it);\n        });\n    }\n    return checkTypes;\n}\nexports.coerceAndCheckDataType = coerceAndCheckDataType;\nconst COERCIBLE = new Set([\"string\", \"number\", \"integer\", \"boolean\", \"null\"]);\nfunction coerceToTypes(types, coerceTypes) {\n    return coerceTypes\n        ? types.filter((t) => COERCIBLE.has(t) || (coerceTypes === \"array\" && t === \"array\"))\n        : [];\n}\nfunction coerceData(it, types, coerceTo) {\n    const { gen, data, opts } = it;\n    const dataType = gen.let(\"dataType\", (0, codegen_1._) `typeof ${data}`);\n    const coerced = gen.let(\"coerced\", (0, codegen_1._) `undefined`);\n    if (opts.coerceTypes === \"array\") {\n        gen.if((0, codegen_1._) `${dataType} == 'object' && Array.isArray(${data}) && ${data}.length == 1`, () => gen\n            .assign(data, (0, codegen_1._) `${data}[0]`)\n            .assign(dataType, (0, codegen_1._) `typeof ${data}`)\n            .if(checkDataTypes(types, data, opts.strictNumbers), () => gen.assign(coerced, data)));\n    }\n    gen.if((0, codegen_1._) `${coerced} !== undefined`);\n    for (const t of coerceTo) {\n        if (COERCIBLE.has(t) || (t === \"array\" && opts.coerceTypes === \"array\")) {\n            coerceSpecificType(t);\n        }\n    }\n    gen.else();\n    reportTypeError(it);\n    gen.endIf();\n    gen.if((0, codegen_1._) `${coerced} !== undefined`, () => {\n        gen.assign(data, coerced);\n        assignParentData(it, coerced);\n    });\n    function coerceSpecificType(t) {\n        switch (t) {\n            case \"string\":\n                gen\n                    .elseIf((0, codegen_1._) `${dataType} == \"number\" || ${dataType} == \"boolean\"`)\n                    .assign(coerced, (0, codegen_1._) `\"\" + ${data}`)\n                    .elseIf((0, codegen_1._) `${data} === null`)\n                    .assign(coerced, (0, codegen_1._) `\"\"`);\n                return;\n            case \"number\":\n                gen\n                    .elseIf((0, codegen_1._) `${dataType} == \"boolean\" || ${data} === null\n              || (${dataType} == \"string\" && ${data} && ${data} == +${data})`)\n                    .assign(coerced, (0, codegen_1._) `+${data}`);\n                return;\n            case \"integer\":\n                gen\n                    .elseIf((0, codegen_1._) `${dataType} === \"boolean\" || ${data} === null\n              || (${dataType} === \"string\" && ${data} && ${data} == +${data} && !(${data} % 1))`)\n                    .assign(coerced, (0, codegen_1._) `+${data}`);\n                return;\n            case \"boolean\":\n                gen\n                    .elseIf((0, codegen_1._) `${data} === \"false\" || ${data} === 0 || ${data} === null`)\n                    .assign(coerced, false)\n                    .elseIf((0, codegen_1._) `${data} === \"true\" || ${data} === 1`)\n                    .assign(coerced, true);\n                return;\n            case \"null\":\n                gen.elseIf((0, codegen_1._) `${data} === \"\" || ${data} === 0 || ${data} === false`);\n                gen.assign(coerced, null);\n                return;\n            case \"array\":\n                gen\n                    .elseIf((0, codegen_1._) `${dataType} === \"string\" || ${dataType} === \"number\"\n              || ${dataType} === \"boolean\" || ${data} === null`)\n                    .assign(coerced, (0, codegen_1._) `[${data}]`);\n        }\n    }\n}\nfunction assignParentData({ gen, parentData, parentDataProperty }, expr) {\n    // TODO use gen.property\n    gen.if((0, codegen_1._) `${parentData} !== undefined`, () => gen.assign((0, codegen_1._) `${parentData}[${parentDataProperty}]`, expr));\n}\nfunction checkDataType(dataType, data, strictNums, correct = DataType.Correct) {\n    const EQ = correct === DataType.Correct ? codegen_1.operators.EQ : codegen_1.operators.NEQ;\n    let cond;\n    switch (dataType) {\n        case \"null\":\n            return (0, codegen_1._) `${data} ${EQ} null`;\n        case \"array\":\n            cond = (0, codegen_1._) `Array.isArray(${data})`;\n            break;\n        case \"object\":\n            cond = (0, codegen_1._) `${data} && typeof ${data} == \"object\" && !Array.isArray(${data})`;\n            break;\n        case \"integer\":\n            cond = numCond((0, codegen_1._) `!(${data} % 1) && !isNaN(${data})`);\n            break;\n        case \"number\":\n            cond = numCond();\n            break;\n        default:\n            return (0, codegen_1._) `typeof ${data} ${EQ} ${dataType}`;\n    }\n    return correct === DataType.Correct ? cond : (0, codegen_1.not)(cond);\n    function numCond(_cond = codegen_1.nil) {\n        return (0, codegen_1.and)((0, codegen_1._) `typeof ${data} == \"number\"`, _cond, strictNums ? (0, codegen_1._) `isFinite(${data})` : codegen_1.nil);\n    }\n}\nexports.checkDataType = checkDataType;\nfunction checkDataTypes(dataTypes, data, strictNums, correct) {\n    if (dataTypes.length === 1) {\n        return checkDataType(dataTypes[0], data, strictNums, correct);\n    }\n    let cond;\n    const types = (0, util_1.toHash)(dataTypes);\n    if (types.array && types.object) {\n        const notObj = (0, codegen_1._) `typeof ${data} != \"object\"`;\n        cond = types.null ? notObj : (0, codegen_1._) `!${data} || ${notObj}`;\n        delete types.null;\n        delete types.array;\n        delete types.object;\n    }\n    else {\n        cond = codegen_1.nil;\n    }\n    if (types.number)\n        delete types.integer;\n    for (const t in types)\n        cond = (0, codegen_1.and)(cond, checkDataType(t, data, strictNums, correct));\n    return cond;\n}\nexports.checkDataTypes = checkDataTypes;\nconst typeError = {\n    message: ({ schema }) => `must be ${schema}`,\n    params: ({ schema, schemaValue }) => typeof schema == \"string\" ? (0, codegen_1._) `{type: ${schema}}` : (0, codegen_1._) `{type: ${schemaValue}}`,\n};\nfunction reportTypeError(it) {\n    const cxt = getTypeErrorContext(it);\n    (0, errors_1.reportError)(cxt, typeError);\n}\nexports.reportTypeError = reportTypeError;\nfunction getTypeErrorContext(it) {\n    const { gen, data, schema } = it;\n    const schemaCode = (0, util_1.schemaRefOrVal)(it, schema, \"type\");\n    return {\n        gen,\n        keyword: \"type\",\n        data,\n        schema: schema.type,\n        schemaCode,\n        schemaValue: schemaCode,\n        parentSchema: schema,\n        params: {},\n        it,\n    };\n}\n//# sourceMappingURL=dataType.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.assignDefaults = void 0;\nconst codegen_1 = require(\"../codegen\");\nconst util_1 = require(\"../util\");\nfunction assignDefaults(it, ty) {\n    const { properties, items } = it.schema;\n    if (ty === \"object\" && properties) {\n        for (const key in properties) {\n            assignDefault(it, key, properties[key].default);\n        }\n    }\n    else if (ty === \"array\" && Array.isArray(items)) {\n        items.forEach((sch, i) => assignDefault(it, i, sch.default));\n    }\n}\nexports.assignDefaults = assignDefaults;\nfunction assignDefault(it, prop, defaultValue) {\n    const { gen, compositeRule, data, opts } = it;\n    if (defaultValue === undefined)\n        return;\n    const childData = (0, codegen_1._) `${data}${(0, codegen_1.getProperty)(prop)}`;\n    if (compositeRule) {\n        (0, util_1.checkStrictMode)(it, `default is ignored for: ${childData}`);\n        return;\n    }\n    let condition = (0, codegen_1._) `${childData} === undefined`;\n    if (opts.useDefaults === \"empty\") {\n        condition = (0, codegen_1._) `${condition} || ${childData} === null || ${childData} === \"\"`;\n    }\n    // `${childData} === undefined` +\n    // (opts.useDefaults === \"empty\" ? ` || ${childData} === null || ${childData} === \"\"` : \"\")\n    gen.if(condition, (0, codegen_1._) `${childData} = ${(0, codegen_1.stringify)(defaultValue)}`);\n}\n//# sourceMappingURL=defaults.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getData = exports.KeywordCxt = exports.validateFunctionCode = void 0;\nconst boolSchema_1 = require(\"./boolSchema\");\nconst dataType_1 = require(\"./dataType\");\nconst applicability_1 = require(\"./applicability\");\nconst dataType_2 = require(\"./dataType\");\nconst defaults_1 = require(\"./defaults\");\nconst keyword_1 = require(\"./keyword\");\nconst subschema_1 = require(\"./subschema\");\nconst codegen_1 = require(\"../codegen\");\nconst names_1 = require(\"../names\");\nconst resolve_1 = require(\"../resolve\");\nconst util_1 = require(\"../util\");\nconst errors_1 = require(\"../errors\");\n// schema compilation - generates validation function, subschemaCode (below) is used for subschemas\nfunction validateFunctionCode(it) {\n    if (isSchemaObj(it)) {\n        checkKeywords(it);\n        if (schemaCxtHasRules(it)) {\n            topSchemaObjCode(it);\n            return;\n        }\n    }\n    validateFunction(it, () => (0, boolSchema_1.topBoolOrEmptySchema)(it));\n}\nexports.validateFunctionCode = validateFunctionCode;\nfunction validateFunction({ gen, validateName, schema, schemaEnv, opts }, body) {\n    if (opts.code.es5) {\n        gen.func(validateName, (0, codegen_1._) `${names_1.default.data}, ${names_1.default.valCxt}`, schemaEnv.$async, () => {\n            gen.code((0, codegen_1._) `\"use strict\"; ${funcSourceUrl(schema, opts)}`);\n            destructureValCxtES5(gen, opts);\n            gen.code(body);\n        });\n    }\n    else {\n        gen.func(validateName, (0, codegen_1._) `${names_1.default.data}, ${destructureValCxt(opts)}`, schemaEnv.$async, () => gen.code(funcSourceUrl(schema, opts)).code(body));\n    }\n}\nfunction destructureValCxt(opts) {\n    return (0, codegen_1._) `{${names_1.default.instancePath}=\"\", ${names_1.default.parentData}, ${names_1.default.parentDataProperty}, ${names_1.default.rootData}=${names_1.default.data}${opts.dynamicRef ? (0, codegen_1._) `, ${names_1.default.dynamicAnchors}={}` : codegen_1.nil}}={}`;\n}\nfunction destructureValCxtES5(gen, opts) {\n    gen.if(names_1.default.valCxt, () => {\n        gen.var(names_1.default.instancePath, (0, codegen_1._) `${names_1.default.valCxt}.${names_1.default.instancePath}`);\n        gen.var(names_1.default.parentData, (0, codegen_1._) `${names_1.default.valCxt}.${names_1.default.parentData}`);\n        gen.var(names_1.default.parentDataProperty, (0, codegen_1._) `${names_1.default.valCxt}.${names_1.default.parentDataProperty}`);\n        gen.var(names_1.default.rootData, (0, codegen_1._) `${names_1.default.valCxt}.${names_1.default.rootData}`);\n        if (opts.dynamicRef)\n            gen.var(names_1.default.dynamicAnchors, (0, codegen_1._) `${names_1.default.valCxt}.${names_1.default.dynamicAnchors}`);\n    }, () => {\n        gen.var(names_1.default.instancePath, (0, codegen_1._) `\"\"`);\n        gen.var(names_1.default.parentData, (0, codegen_1._) `undefined`);\n        gen.var(names_1.default.parentDataProperty, (0, codegen_1._) `undefined`);\n        gen.var(names_1.default.rootData, names_1.default.data);\n        if (opts.dynamicRef)\n            gen.var(names_1.default.dynamicAnchors, (0, codegen_1._) `{}`);\n    });\n}\nfunction topSchemaObjCode(it) {\n    const { schema, opts, gen } = it;\n    validateFunction(it, () => {\n        if (opts.$comment && schema.$comment)\n            commentKeyword(it);\n        checkNoDefault(it);\n        gen.let(names_1.default.vErrors, null);\n        gen.let(names_1.default.errors, 0);\n        if (opts.unevaluated)\n            resetEvaluated(it);\n        typeAndKeywords(it);\n        returnResults(it);\n    });\n    return;\n}\nfunction resetEvaluated(it) {\n    // TODO maybe some hook to execute it in the end to check whether props/items are Name, as in assignEvaluated\n    const { gen, validateName } = it;\n    it.evaluated = gen.const(\"evaluated\", (0, codegen_1._) `${validateName}.evaluated`);\n    gen.if((0, codegen_1._) `${it.evaluated}.dynamicProps`, () => gen.assign((0, codegen_1._) `${it.evaluated}.props`, (0, codegen_1._) `undefined`));\n    gen.if((0, codegen_1._) `${it.evaluated}.dynamicItems`, () => gen.assign((0, codegen_1._) `${it.evaluated}.items`, (0, codegen_1._) `undefined`));\n}\nfunction funcSourceUrl(schema, opts) {\n    const schId = typeof schema == \"object\" && schema[opts.schemaId];\n    return schId && (opts.code.source || opts.code.process) ? (0, codegen_1._) `/*# sourceURL=${schId} */` : codegen_1.nil;\n}\n// schema compilation - this function is used recursively to generate code for sub-schemas\nfunction subschemaCode(it, valid) {\n    if (isSchemaObj(it)) {\n        checkKeywords(it);\n        if (schemaCxtHasRules(it)) {\n            subSchemaObjCode(it, valid);\n            return;\n        }\n    }\n    (0, boolSchema_1.boolOrEmptySchema)(it, valid);\n}\nfunction schemaCxtHasRules({ schema, self }) {\n    if (typeof schema == \"boolean\")\n        return !schema;\n    for (const key in schema)\n        if (self.RULES.all[key])\n            return true;\n    return false;\n}\nfunction isSchemaObj(it) {\n    return typeof it.schema != \"boolean\";\n}\nfunction subSchemaObjCode(it, valid) {\n    const { schema, gen, opts } = it;\n    if (opts.$comment && schema.$comment)\n        commentKeyword(it);\n    updateContext(it);\n    checkAsyncSchema(it);\n    const errsCount = gen.const(\"_errs\", names_1.default.errors);\n    typeAndKeywords(it, errsCount);\n    // TODO var\n    gen.var(valid, (0, codegen_1._) `${errsCount} === ${names_1.default.errors}`);\n}\nfunction checkKeywords(it) {\n    (0, util_1.checkUnknownRules)(it);\n    checkRefsAndKeywords(it);\n}\nfunction typeAndKeywords(it, errsCount) {\n    if (it.opts.jtd)\n        return schemaKeywords(it, [], false, errsCount);\n    const types = (0, dataType_1.getSchemaTypes)(it.schema);\n    const checkedTypes = (0, dataType_1.coerceAndCheckDataType)(it, types);\n    schemaKeywords(it, types, !checkedTypes, errsCount);\n}\nfunction checkRefsAndKeywords(it) {\n    const { schema, errSchemaPath, opts, self } = it;\n    if (schema.$ref && opts.ignoreKeywordsWithRef && (0, util_1.schemaHasRulesButRef)(schema, self.RULES)) {\n        self.logger.warn(`$ref: keywords ignored in schema at path \"${errSchemaPath}\"`);\n    }\n}\nfunction checkNoDefault(it) {\n    const { schema, opts } = it;\n    if (schema.default !== undefined && opts.useDefaults && opts.strictSchema) {\n        (0, util_1.checkStrictMode)(it, \"default is ignored in the schema root\");\n    }\n}\nfunction updateContext(it) {\n    const schId = it.schema[it.opts.schemaId];\n    if (schId)\n        it.baseId = (0, resolve_1.resolveUrl)(it.opts.uriResolver, it.baseId, schId);\n}\nfunction checkAsyncSchema(it) {\n    if (it.schema.$async && !it.schemaEnv.$async)\n        throw new Error(\"async schema in sync schema\");\n}\nfunction commentKeyword({ gen, schemaEnv, schema, errSchemaPath, opts }) {\n    const msg = schema.$comment;\n    if (opts.$comment === true) {\n        gen.code((0, codegen_1._) `${names_1.default.self}.logger.log(${msg})`);\n    }\n    else if (typeof opts.$comment == \"function\") {\n        const schemaPath = (0, codegen_1.str) `${errSchemaPath}/$comment`;\n        const rootName = gen.scopeValue(\"root\", { ref: schemaEnv.root });\n        gen.code((0, codegen_1._) `${names_1.default.self}.opts.$comment(${msg}, ${schemaPath}, ${rootName}.schema)`);\n    }\n}\nfunction returnResults(it) {\n    const { gen, schemaEnv, validateName, ValidationError, opts } = it;\n    if (schemaEnv.$async) {\n        // TODO assign unevaluated\n        gen.if((0, codegen_1._) `${names_1.default.errors} === 0`, () => gen.return(names_1.default.data), () => gen.throw((0, codegen_1._) `new ${ValidationError}(${names_1.default.vErrors})`));\n    }\n    else {\n        gen.assign((0, codegen_1._) `${validateName}.errors`, names_1.default.vErrors);\n        if (opts.unevaluated)\n            assignEvaluated(it);\n        gen.return((0, codegen_1._) `${names_1.default.errors} === 0`);\n    }\n}\nfunction assignEvaluated({ gen, evaluated, props, items }) {\n    if (props instanceof codegen_1.Name)\n        gen.assign((0, codegen_1._) `${evaluated}.props`, props);\n    if (items instanceof codegen_1.Name)\n        gen.assign((0, codegen_1._) `${evaluated}.items`, items);\n}\nfunction schemaKeywords(it, types, typeErrors, errsCount) {\n    const { gen, schema, data, allErrors, opts, self } = it;\n    const { RULES } = self;\n    if (schema.$ref && (opts.ignoreKeywordsWithRef || !(0, util_1.schemaHasRulesButRef)(schema, RULES))) {\n        gen.block(() => keywordCode(it, \"$ref\", RULES.all.$ref.definition)); // TODO typecast\n        return;\n    }\n    if (!opts.jtd)\n        checkStrictTypes(it, types);\n    gen.block(() => {\n        for (const group of RULES.rules)\n            groupKeywords(group);\n        groupKeywords(RULES.post);\n    });\n    function groupKeywords(group) {\n        if (!(0, applicability_1.shouldUseGroup)(schema, group))\n            return;\n        if (group.type) {\n            gen.if((0, dataType_2.checkDataType)(group.type, data, opts.strictNumbers));\n            iterateKeywords(it, group);\n            if (types.length === 1 && types[0] === group.type && typeErrors) {\n                gen.else();\n                (0, dataType_2.reportTypeError)(it);\n            }\n            gen.endIf();\n        }\n        else {\n            iterateKeywords(it, group);\n        }\n        // TODO make it \"ok\" call?\n        if (!allErrors)\n            gen.if((0, codegen_1._) `${names_1.default.errors} === ${errsCount || 0}`);\n    }\n}\nfunction iterateKeywords(it, group) {\n    const { gen, schema, opts: { useDefaults }, } = it;\n    if (useDefaults)\n        (0, defaults_1.assignDefaults)(it, group.type);\n    gen.block(() => {\n        for (const rule of group.rules) {\n            if ((0, applicability_1.shouldUseRule)(schema, rule)) {\n                keywordCode(it, rule.keyword, rule.definition, group.type);\n            }\n        }\n    });\n}\nfunction checkStrictTypes(it, types) {\n    if (it.schemaEnv.meta || !it.opts.strictTypes)\n        return;\n    checkContextTypes(it, types);\n    if (!it.opts.allowUnionTypes)\n        checkMultipleTypes(it, types);\n    checkKeywordTypes(it, it.dataTypes);\n}\nfunction checkContextTypes(it, types) {\n    if (!types.length)\n        return;\n    if (!it.dataTypes.length) {\n        it.dataTypes = types;\n        return;\n    }\n    types.forEach((t) => {\n        if (!includesType(it.dataTypes, t)) {\n            strictTypesError(it, `type \"${t}\" not allowed by context \"${it.dataTypes.join(\",\")}\"`);\n        }\n    });\n    narrowSchemaTypes(it, types);\n}\nfunction checkMultipleTypes(it, ts) {\n    if (ts.length > 1 && !(ts.length === 2 && ts.includes(\"null\"))) {\n        strictTypesError(it, \"use allowUnionTypes to allow union type keyword\");\n    }\n}\nfunction checkKeywordTypes(it, ts) {\n    const rules = it.self.RULES.all;\n    for (const keyword in rules) {\n        const rule = rules[keyword];\n        if (typeof rule == \"object\" && (0, applicability_1.shouldUseRule)(it.schema, rule)) {\n            const { type } = rule.definition;\n            if (type.length && !type.some((t) => hasApplicableType(ts, t))) {\n                strictTypesError(it, `missing type \"${type.join(\",\")}\" for keyword \"${keyword}\"`);\n            }\n        }\n    }\n}\nfunction hasApplicableType(schTs, kwdT) {\n    return schTs.includes(kwdT) || (kwdT === \"number\" && schTs.includes(\"integer\"));\n}\nfunction includesType(ts, t) {\n    return ts.includes(t) || (t === \"integer\" && ts.includes(\"number\"));\n}\nfunction narrowSchemaTypes(it, withTypes) {\n    const ts = [];\n    for (const t of it.dataTypes) {\n        if (includesType(withTypes, t))\n            ts.push(t);\n        else if (withTypes.includes(\"integer\") && t === \"number\")\n            ts.push(\"integer\");\n    }\n    it.dataTypes = ts;\n}\nfunction strictTypesError(it, msg) {\n    const schemaPath = it.schemaEnv.baseId + it.errSchemaPath;\n    msg += ` at \"${schemaPath}\" (strictTypes)`;\n    (0, util_1.checkStrictMode)(it, msg, it.opts.strictTypes);\n}\nclass KeywordCxt {\n    constructor(it, def, keyword) {\n        (0, keyword_1.validateKeywordUsage)(it, def, keyword);\n        this.gen = it.gen;\n        this.allErrors = it.allErrors;\n        this.keyword = keyword;\n        this.data = it.data;\n        this.schema = it.schema[keyword];\n        this.$data = def.$data && it.opts.$data && this.schema && this.schema.$data;\n        this.schemaValue = (0, util_1.schemaRefOrVal)(it, this.schema, keyword, this.$data);\n        this.schemaType = def.schemaType;\n        this.parentSchema = it.schema;\n        this.params = {};\n        this.it = it;\n        this.def = def;\n        if (this.$data) {\n            this.schemaCode = it.gen.const(\"vSchema\", getData(this.$data, it));\n        }\n        else {\n            this.schemaCode = this.schemaValue;\n            if (!(0, keyword_1.validSchemaType)(this.schema, def.schemaType, def.allowUndefined)) {\n                throw new Error(`${keyword} value must be ${JSON.stringify(def.schemaType)}`);\n            }\n        }\n        if (\"code\" in def ? def.trackErrors : def.errors !== false) {\n            this.errsCount = it.gen.const(\"_errs\", names_1.default.errors);\n        }\n    }\n    result(condition, successAction, failAction) {\n        this.failResult((0, codegen_1.not)(condition), successAction, failAction);\n    }\n    failResult(condition, successAction, failAction) {\n        this.gen.if(condition);\n        if (failAction)\n            failAction();\n        else\n            this.error();\n        if (successAction) {\n            this.gen.else();\n            successAction();\n            if (this.allErrors)\n                this.gen.endIf();\n        }\n        else {\n            if (this.allErrors)\n                this.gen.endIf();\n            else\n                this.gen.else();\n        }\n    }\n    pass(condition, failAction) {\n        this.failResult((0, codegen_1.not)(condition), undefined, failAction);\n    }\n    fail(condition) {\n        if (condition === undefined) {\n            this.error();\n            if (!this.allErrors)\n                this.gen.if(false); // this branch will be removed by gen.optimize\n            return;\n        }\n        this.gen.if(condition);\n        this.error();\n        if (this.allErrors)\n            this.gen.endIf();\n        else\n            this.gen.else();\n    }\n    fail$data(condition) {\n        if (!this.$data)\n            return this.fail(condition);\n        const { schemaCode } = this;\n        this.fail((0, codegen_1._) `${schemaCode} !== undefined && (${(0, codegen_1.or)(this.invalid$data(), condition)})`);\n    }\n    error(append, errorParams, errorPaths) {\n        if (errorParams) {\n            this.setParams(errorParams);\n            this._error(append, errorPaths);\n            this.setParams({});\n            return;\n        }\n        this._error(append, errorPaths);\n    }\n    _error(append, errorPaths) {\n        ;\n        (append ? errors_1.reportExtraError : errors_1.reportError)(this, this.def.error, errorPaths);\n    }\n    $dataError() {\n        (0, errors_1.reportError)(this, this.def.$dataError || errors_1.keyword$DataError);\n    }\n    reset() {\n        if (this.errsCount === undefined)\n            throw new Error('add \"trackErrors\" to keyword definition');\n        (0, errors_1.resetErrorsCount)(this.gen, this.errsCount);\n    }\n    ok(cond) {\n        if (!this.allErrors)\n            this.gen.if(cond);\n    }\n    setParams(obj, assign) {\n        if (assign)\n            Object.assign(this.params, obj);\n        else\n            this.params = obj;\n    }\n    block$data(valid, codeBlock, $dataValid = codegen_1.nil) {\n        this.gen.block(() => {\n            this.check$data(valid, $dataValid);\n            codeBlock();\n        });\n    }\n    check$data(valid = codegen_1.nil, $dataValid = codegen_1.nil) {\n        if (!this.$data)\n            return;\n        const { gen, schemaCode, schemaType, def } = this;\n        gen.if((0, codegen_1.or)((0, codegen_1._) `${schemaCode} === undefined`, $dataValid));\n        if (valid !== codegen_1.nil)\n            gen.assign(valid, true);\n        if (schemaType.length || def.validateSchema) {\n            gen.elseIf(this.invalid$data());\n            this.$dataError();\n            if (valid !== codegen_1.nil)\n                gen.assign(valid, false);\n        }\n        gen.else();\n    }\n    invalid$data() {\n        const { gen, schemaCode, schemaType, def, it } = this;\n        return (0, codegen_1.or)(wrong$DataType(), invalid$DataSchema());\n        function wrong$DataType() {\n            if (schemaType.length) {\n                /* istanbul ignore if */\n                if (!(schemaCode instanceof codegen_1.Name))\n                    throw new Error(\"ajv implementation error\");\n                const st = Array.isArray(schemaType) ? schemaType : [schemaType];\n                return (0, codegen_1._) `${(0, dataType_2.checkDataTypes)(st, schemaCode, it.opts.strictNumbers, dataType_2.DataType.Wrong)}`;\n            }\n            return codegen_1.nil;\n        }\n        function invalid$DataSchema() {\n            if (def.validateSchema) {\n                const validateSchemaRef = gen.scopeValue(\"validate$data\", { ref: def.validateSchema }); // TODO value.code for standalone\n                return (0, codegen_1._) `!${validateSchemaRef}(${schemaCode})`;\n            }\n            return codegen_1.nil;\n        }\n    }\n    subschema(appl, valid) {\n        const subschema = (0, subschema_1.getSubschema)(this.it, appl);\n        (0, subschema_1.extendSubschemaData)(subschema, this.it, appl);\n        (0, subschema_1.extendSubschemaMode)(subschema, appl);\n        const nextContext = { ...this.it, ...subschema, items: undefined, props: undefined };\n        subschemaCode(nextContext, valid);\n        return nextContext;\n    }\n    mergeEvaluated(schemaCxt, toName) {\n        const { it, gen } = this;\n        if (!it.opts.unevaluated)\n            return;\n        if (it.props !== true && schemaCxt.props !== undefined) {\n            it.props = util_1.mergeEvaluated.props(gen, schemaCxt.props, it.props, toName);\n        }\n        if (it.items !== true && schemaCxt.items !== undefined) {\n            it.items = util_1.mergeEvaluated.items(gen, schemaCxt.items, it.items, toName);\n        }\n    }\n    mergeValidEvaluated(schemaCxt, valid) {\n        const { it, gen } = this;\n        if (it.opts.unevaluated && (it.props !== true || it.items !== true)) {\n            gen.if(valid, () => this.mergeEvaluated(schemaCxt, codegen_1.Name));\n            return true;\n        }\n    }\n}\nexports.KeywordCxt = KeywordCxt;\nfunction keywordCode(it, keyword, def, ruleType) {\n    const cxt = new KeywordCxt(it, def, keyword);\n    if (\"code\" in def) {\n        def.code(cxt, ruleType);\n    }\n    else if (cxt.$data && def.validate) {\n        (0, keyword_1.funcKeywordCode)(cxt, def);\n    }\n    else if (\"macro\" in def) {\n        (0, keyword_1.macroKeywordCode)(cxt, def);\n    }\n    else if (def.compile || def.validate) {\n        (0, keyword_1.funcKeywordCode)(cxt, def);\n    }\n}\nconst JSON_POINTER = /^\\/(?:[^~]|~0|~1)*$/;\nconst RELATIVE_JSON_POINTER = /^([0-9]+)(#|\\/(?:[^~]|~0|~1)*)?$/;\nfunction getData($data, { dataLevel, dataNames, dataPathArr }) {\n    let jsonPointer;\n    let data;\n    if ($data === \"\")\n        return names_1.default.rootData;\n    if ($data[0] === \"/\") {\n        if (!JSON_POINTER.test($data))\n            throw new Error(`Invalid JSON-pointer: ${$data}`);\n        jsonPointer = $data;\n        data = names_1.default.rootData;\n    }\n    else {\n        const matches = RELATIVE_JSON_POINTER.exec($data);\n        if (!matches)\n            throw new Error(`Invalid JSON-pointer: ${$data}`);\n        const up = +matches[1];\n        jsonPointer = matches[2];\n        if (jsonPointer === \"#\") {\n            if (up >= dataLevel)\n                throw new Error(errorMsg(\"property/index\", up));\n            return dataPathArr[dataLevel - up];\n        }\n        if (up > dataLevel)\n            throw new Error(errorMsg(\"data\", up));\n        data = dataNames[dataLevel - up];\n        if (!jsonPointer)\n            return data;\n    }\n    let expr = data;\n    const segments = jsonPointer.split(\"/\");\n    for (const segment of segments) {\n        if (segment) {\n            data = (0, codegen_1._) `${data}${(0, codegen_1.getProperty)((0, util_1.unescapeJsonPointer)(segment))}`;\n            expr = (0, codegen_1._) `${expr} && ${data}`;\n        }\n    }\n    return expr;\n    function errorMsg(pointerType, up) {\n        return `Cannot access ${pointerType} ${up} levels up, current level is ${dataLevel}`;\n    }\n}\nexports.getData = getData;\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateKeywordUsage = exports.validSchemaType = exports.funcKeywordCode = exports.macroKeywordCode = void 0;\nconst codegen_1 = require(\"../codegen\");\nconst names_1 = require(\"../names\");\nconst code_1 = require(\"../../vocabularies/code\");\nconst errors_1 = require(\"../errors\");\nfunction macroKeywordCode(cxt, def) {\n    const { gen, keyword, schema, parentSchema, it } = cxt;\n    const macroSchema = def.macro.call(it.self, schema, parentSchema, it);\n    const schemaRef = useKeyword(gen, keyword, macroSchema);\n    if (it.opts.validateSchema !== false)\n        it.self.validateSchema(macroSchema, true);\n    const valid = gen.name(\"valid\");\n    cxt.subschema({\n        schema: macroSchema,\n        schemaPath: codegen_1.nil,\n        errSchemaPath: `${it.errSchemaPath}/${keyword}`,\n        topSchemaRef: schemaRef,\n        compositeRule: true,\n    }, valid);\n    cxt.pass(valid, () => cxt.error(true));\n}\nexports.macroKeywordCode = macroKeywordCode;\nfunction funcKeywordCode(cxt, def) {\n    var _a;\n    const { gen, keyword, schema, parentSchema, $data, it } = cxt;\n    checkAsyncKeyword(it, def);\n    const validate = !$data && def.compile ? def.compile.call(it.self, schema, parentSchema, it) : def.validate;\n    const validateRef = useKeyword(gen, keyword, validate);\n    const valid = gen.let(\"valid\");\n    cxt.block$data(valid, validateKeyword);\n    cxt.ok((_a = def.valid) !== null && _a !== void 0 ? _a : valid);\n    function validateKeyword() {\n        if (def.errors === false) {\n            assignValid();\n            if (def.modifying)\n                modifyData(cxt);\n            reportErrs(() => cxt.error());\n        }\n        else {\n            const ruleErrs = def.async ? validateAsync() : validateSync();\n            if (def.modifying)\n                modifyData(cxt);\n            reportErrs(() => addErrs(cxt, ruleErrs));\n        }\n    }\n    function validateAsync() {\n        const ruleErrs = gen.let(\"ruleErrs\", null);\n        gen.try(() => assignValid((0, codegen_1._) `await `), (e) => gen.assign(valid, false).if((0, codegen_1._) `${e} instanceof ${it.ValidationError}`, () => gen.assign(ruleErrs, (0, codegen_1._) `${e}.errors`), () => gen.throw(e)));\n        return ruleErrs;\n    }\n    function validateSync() {\n        const validateErrs = (0, codegen_1._) `${validateRef}.errors`;\n        gen.assign(validateErrs, null);\n        assignValid(codegen_1.nil);\n        return validateErrs;\n    }\n    function assignValid(_await = def.async ? (0, codegen_1._) `await ` : codegen_1.nil) {\n        const passCxt = it.opts.passContext ? names_1.default.this : names_1.default.self;\n        const passSchema = !((\"compile\" in def && !$data) || def.schema === false);\n        gen.assign(valid, (0, codegen_1._) `${_await}${(0, code_1.callValidateCode)(cxt, validateRef, passCxt, passSchema)}`, def.modifying);\n    }\n    function reportErrs(errors) {\n        var _a;\n        gen.if((0, codegen_1.not)((_a = def.valid) !== null && _a !== void 0 ? _a : valid), errors);\n    }\n}\nexports.funcKeywordCode = funcKeywordCode;\nfunction modifyData(cxt) {\n    const { gen, data, it } = cxt;\n    gen.if(it.parentData, () => gen.assign(data, (0, codegen_1._) `${it.parentData}[${it.parentDataProperty}]`));\n}\nfunction addErrs(cxt, errs) {\n    const { gen } = cxt;\n    gen.if((0, codegen_1._) `Array.isArray(${errs})`, () => {\n        gen\n            .assign(names_1.default.vErrors, (0, codegen_1._) `${names_1.default.vErrors} === null ? ${errs} : ${names_1.default.vErrors}.concat(${errs})`)\n            .assign(names_1.default.errors, (0, codegen_1._) `${names_1.default.vErrors}.length`);\n        (0, errors_1.extendErrors)(cxt);\n    }, () => cxt.error());\n}\nfunction checkAsyncKeyword({ schemaEnv }, def) {\n    if (def.async && !schemaEnv.$async)\n        throw new Error(\"async keyword in sync schema\");\n}\nfunction useKeyword(gen, keyword, result) {\n    if (result === undefined)\n        throw new Error(`keyword \"${keyword}\" failed to compile`);\n    return gen.scopeValue(\"keyword\", typeof result == \"function\" ? { ref: result } : { ref: result, code: (0, codegen_1.stringify)(result) });\n}\nfunction validSchemaType(schema, schemaType, allowUndefined = false) {\n    // TODO add tests\n    return (!schemaType.length ||\n        schemaType.some((st) => st === \"array\"\n            ? Array.isArray(schema)\n            : st === \"object\"\n                ? schema && typeof schema == \"object\" && !Array.isArray(schema)\n                : typeof schema == st || (allowUndefined && typeof schema == \"undefined\")));\n}\nexports.validSchemaType = validSchemaType;\nfunction validateKeywordUsage({ schema, opts, self, errSchemaPath }, def, keyword) {\n    /* istanbul ignore if */\n    if (Array.isArray(def.keyword) ? !def.keyword.includes(keyword) : def.keyword !== keyword) {\n        throw new Error(\"ajv implementation error\");\n    }\n    const deps = def.dependencies;\n    if (deps === null || deps === void 0 ? void 0 : deps.some((kwd) => !Object.prototype.hasOwnProperty.call(schema, kwd))) {\n        throw new Error(`parent schema must have dependencies of ${keyword}: ${deps.join(\",\")}`);\n    }\n    if (def.validateSchema) {\n        const valid = def.validateSchema(schema[keyword]);\n        if (!valid) {\n            const msg = `keyword \"${keyword}\" value is invalid at path \"${errSchemaPath}\": ` +\n                self.errorsText(def.validateSchema.errors);\n            if (opts.validateSchema === \"log\")\n                self.logger.error(msg);\n            else\n                throw new Error(msg);\n        }\n    }\n}\nexports.validateKeywordUsage = validateKeywordUsage;\n//# sourceMappingURL=keyword.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.extendSubschemaMode = exports.extendSubschemaData = exports.getSubschema = void 0;\nconst codegen_1 = require(\"../codegen\");\nconst util_1 = require(\"../util\");\nfunction getSubschema(it, { keyword, schemaProp, schema, schemaPath, errSchemaPath, topSchemaRef }) {\n    if (keyword !== undefined && schema !== undefined) {\n        throw new Error('both \"keyword\" and \"schema\" passed, only one allowed');\n    }\n    if (keyword !== undefined) {\n        const sch = it.schema[keyword];\n        return schemaProp === undefined\n            ? {\n                schema: sch,\n                schemaPath: (0, codegen_1._) `${it.schemaPath}${(0, codegen_1.getProperty)(keyword)}`,\n                errSchemaPath: `${it.errSchemaPath}/${keyword}`,\n            }\n            : {\n                schema: sch[schemaProp],\n                schemaPath: (0, codegen_1._) `${it.schemaPath}${(0, codegen_1.getProperty)(keyword)}${(0, codegen_1.getProperty)(schemaProp)}`,\n                errSchemaPath: `${it.errSchemaPath}/${keyword}/${(0, util_1.escapeFragment)(schemaProp)}`,\n            };\n    }\n    if (schema !== undefined) {\n        if (schemaPath === undefined || errSchemaPath === undefined || topSchemaRef === undefined) {\n            throw new Error('\"schemaPath\", \"errSchemaPath\" and \"topSchemaRef\" are required with \"schema\"');\n        }\n        return {\n            schema,\n            schemaPath,\n            topSchemaRef,\n            errSchemaPath,\n        };\n    }\n    throw new Error('either \"keyword\" or \"schema\" must be passed');\n}\nexports.getSubschema = getSubschema;\nfunction extendSubschemaData(subschema, it, { dataProp, dataPropType: dpType, data, dataTypes, propertyName }) {\n    if (data !== undefined && dataProp !== undefined) {\n        throw new Error('both \"data\" and \"dataProp\" passed, only one allowed');\n    }\n    const { gen } = it;\n    if (dataProp !== undefined) {\n        const { errorPath, dataPathArr, opts } = it;\n        const nextData = gen.let(\"data\", (0, codegen_1._) `${it.data}${(0, codegen_1.getProperty)(dataProp)}`, true);\n        dataContextProps(nextData);\n        subschema.errorPath = (0, codegen_1.str) `${errorPath}${(0, util_1.getErrorPath)(dataProp, dpType, opts.jsPropertySyntax)}`;\n        subschema.parentDataProperty = (0, codegen_1._) `${dataProp}`;\n        subschema.dataPathArr = [...dataPathArr, subschema.parentDataProperty];\n    }\n    if (data !== undefined) {\n        const nextData = data instanceof codegen_1.Name ? data : gen.let(\"data\", data, true); // replaceable if used once?\n        dataContextProps(nextData);\n        if (propertyName !== undefined)\n            subschema.propertyName = propertyName;\n        // TODO something is possibly wrong here with not changing parentDataProperty and not appending dataPathArr\n    }\n    if (dataTypes)\n        subschema.dataTypes = dataTypes;\n    function dataContextProps(_nextData) {\n        subschema.data = _nextData;\n        subschema.dataLevel = it.dataLevel + 1;\n        subschema.dataTypes = [];\n        it.definedProperties = new Set();\n        subschema.parentData = it.data;\n        subschema.dataNames = [...it.dataNames, _nextData];\n    }\n}\nexports.extendSubschemaData = extendSubschemaData;\nfunction extendSubschemaMode(subschema, { jtdDiscriminator, jtdMetadata, compositeRule, createErrors, allErrors }) {\n    if (compositeRule !== undefined)\n        subschema.compositeRule = compositeRule;\n    if (createErrors !== undefined)\n        subschema.createErrors = createErrors;\n    if (allErrors !== undefined)\n        subschema.allErrors = allErrors;\n    subschema.jtdDiscriminator = jtdDiscriminator; // not inherited\n    subschema.jtdMetadata = jtdMetadata; // not inherited\n}\nexports.extendSubschemaMode = extendSubschemaMode;\n//# sourceMappingURL=subschema.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CodeGen = exports.Name = exports.nil = exports.stringify = exports.str = exports._ = exports.KeywordCxt = void 0;\nvar validate_1 = require(\"./compile/validate\");\nObject.defineProperty(exports, \"KeywordCxt\", { enumerable: true, get: function () { return validate_1.KeywordCxt; } });\nvar codegen_1 = require(\"./compile/codegen\");\nObject.defineProperty(exports, \"_\", { enumerable: true, get: function () { return codegen_1._; } });\nObject.defineProperty(exports, \"str\", { enumerable: true, get: function () { return codegen_1.str; } });\nObject.defineProperty(exports, \"stringify\", { enumerable: true, get: function () { return codegen_1.stringify; } });\nObject.defineProperty(exports, \"nil\", { enumerable: true, get: function () { return codegen_1.nil; } });\nObject.defineProperty(exports, \"Name\", { enumerable: true, get: function () { return codegen_1.Name; } });\nObject.defineProperty(exports, \"CodeGen\", { enumerable: true, get: function () { return codegen_1.CodeGen; } });\nconst validation_error_1 = require(\"./runtime/validation_error\");\nconst ref_error_1 = require(\"./compile/ref_error\");\nconst rules_1 = require(\"./compile/rules\");\nconst compile_1 = require(\"./compile\");\nconst codegen_2 = require(\"./compile/codegen\");\nconst resolve_1 = require(\"./compile/resolve\");\nconst dataType_1 = require(\"./compile/validate/dataType\");\nconst util_1 = require(\"./compile/util\");\nconst $dataRefSchema = require(\"./refs/data.json\");\nconst uri_1 = require(\"./runtime/uri\");\nconst defaultRegExp = (str, flags) => new RegExp(str, flags);\ndefaultRegExp.code = \"new RegExp\";\nconst META_IGNORE_OPTIONS = [\"removeAdditional\", \"useDefaults\", \"coerceTypes\"];\nconst EXT_SCOPE_NAMES = new Set([\n    \"validate\",\n    \"serialize\",\n    \"parse\",\n    \"wrapper\",\n    \"root\",\n    \"schema\",\n    \"keyword\",\n    \"pattern\",\n    \"formats\",\n    \"validate$data\",\n    \"func\",\n    \"obj\",\n    \"Error\",\n]);\nconst removedOptions = {\n    errorDataPath: \"\",\n    format: \"`validateFormats: false` can be used instead.\",\n    nullable: '\"nullable\" keyword is supported by default.',\n    jsonPointers: \"Deprecated jsPropertySyntax can be used instead.\",\n    extendRefs: \"Deprecated ignoreKeywordsWithRef can be used instead.\",\n    missingRefs: \"Pass empty schema with $id that should be ignored to ajv.addSchema.\",\n    processCode: \"Use option `code: {process: (code, schemaEnv: object) => string}`\",\n    sourceCode: \"Use option `code: {source: true}`\",\n    strictDefaults: \"It is default now, see option `strict`.\",\n    strictKeywords: \"It is default now, see option `strict`.\",\n    uniqueItems: '\"uniqueItems\" keyword is always validated.',\n    unknownFormats: \"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).\",\n    cache: \"Map is used as cache, schema object as key.\",\n    serialize: \"Map is used as cache, schema object as key.\",\n    ajvErrors: \"It is default now.\",\n};\nconst deprecatedOptions = {\n    ignoreKeywordsWithRef: \"\",\n    jsPropertySyntax: \"\",\n    unicode: '\"minLength\"/\"maxLength\" account for unicode characters by default.',\n};\nconst MAX_EXPRESSION = 200;\n// eslint-disable-next-line complexity\nfunction requiredOptions(o) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0;\n    const s = o.strict;\n    const _optz = (_a = o.code) === null || _a === void 0 ? void 0 : _a.optimize;\n    const optimize = _optz === true || _optz === undefined ? 1 : _optz || 0;\n    const regExp = (_c = (_b = o.code) === null || _b === void 0 ? void 0 : _b.regExp) !== null && _c !== void 0 ? _c : defaultRegExp;\n    const uriResolver = (_d = o.uriResolver) !== null && _d !== void 0 ? _d : uri_1.default;\n    return {\n        strictSchema: (_f = (_e = o.strictSchema) !== null && _e !== void 0 ? _e : s) !== null && _f !== void 0 ? _f : true,\n        strictNumbers: (_h = (_g = o.strictNumbers) !== null && _g !== void 0 ? _g : s) !== null && _h !== void 0 ? _h : true,\n        strictTypes: (_k = (_j = o.strictTypes) !== null && _j !== void 0 ? _j : s) !== null && _k !== void 0 ? _k : \"log\",\n        strictTuples: (_m = (_l = o.strictTuples) !== null && _l !== void 0 ? _l : s) !== null && _m !== void 0 ? _m : \"log\",\n        strictRequired: (_p = (_o = o.strictRequired) !== null && _o !== void 0 ? _o : s) !== null && _p !== void 0 ? _p : false,\n        code: o.code ? { ...o.code, optimize, regExp } : { optimize, regExp },\n        loopRequired: (_q = o.loopRequired) !== null && _q !== void 0 ? _q : MAX_EXPRESSION,\n        loopEnum: (_r = o.loopEnum) !== null && _r !== void 0 ? _r : MAX_EXPRESSION,\n        meta: (_s = o.meta) !== null && _s !== void 0 ? _s : true,\n        messages: (_t = o.messages) !== null && _t !== void 0 ? _t : true,\n        inlineRefs: (_u = o.inlineRefs) !== null && _u !== void 0 ? _u : true,\n        schemaId: (_v = o.schemaId) !== null && _v !== void 0 ? _v : \"$id\",\n        addUsedSchema: (_w = o.addUsedSchema) !== null && _w !== void 0 ? _w : true,\n        validateSchema: (_x = o.validateSchema) !== null && _x !== void 0 ? _x : true,\n        validateFormats: (_y = o.validateFormats) !== null && _y !== void 0 ? _y : true,\n        unicodeRegExp: (_z = o.unicodeRegExp) !== null && _z !== void 0 ? _z : true,\n        int32range: (_0 = o.int32range) !== null && _0 !== void 0 ? _0 : true,\n        uriResolver: uriResolver,\n    };\n}\nclass Ajv {\n    constructor(opts = {}) {\n        this.schemas = {};\n        this.refs = {};\n        this.formats = {};\n        this._compilations = new Set();\n        this._loading = {};\n        this._cache = new Map();\n        opts = this.opts = { ...opts, ...requiredOptions(opts) };\n        const { es5, lines } = this.opts.code;\n        this.scope = new codegen_2.ValueScope({ scope: {}, prefixes: EXT_SCOPE_NAMES, es5, lines });\n        this.logger = getLogger(opts.logger);\n        const formatOpt = opts.validateFormats;\n        opts.validateFormats = false;\n        this.RULES = (0, rules_1.getRules)();\n        checkOptions.call(this, removedOptions, opts, \"NOT SUPPORTED\");\n        checkOptions.call(this, deprecatedOptions, opts, \"DEPRECATED\", \"warn\");\n        this._metaOpts = getMetaSchemaOptions.call(this);\n        if (opts.formats)\n            addInitialFormats.call(this);\n        this._addVocabularies();\n        this._addDefaultMetaSchema();\n        if (opts.keywords)\n            addInitialKeywords.call(this, opts.keywords);\n        if (typeof opts.meta == \"object\")\n            this.addMetaSchema(opts.meta);\n        addInitialSchemas.call(this);\n        opts.validateFormats = formatOpt;\n    }\n    _addVocabularies() {\n        this.addKeyword(\"$async\");\n    }\n    _addDefaultMetaSchema() {\n        const { $data, meta, schemaId } = this.opts;\n        let _dataRefSchema = $dataRefSchema;\n        if (schemaId === \"id\") {\n            _dataRefSchema = { ...$dataRefSchema };\n            _dataRefSchema.id = _dataRefSchema.$id;\n            delete _dataRefSchema.$id;\n        }\n        if (meta && $data)\n            this.addMetaSchema(_dataRefSchema, _dataRefSchema[schemaId], false);\n    }\n    defaultMeta() {\n        const { meta, schemaId } = this.opts;\n        return (this.opts.defaultMeta = typeof meta == \"object\" ? meta[schemaId] || meta : undefined);\n    }\n    validate(schemaKeyRef, // key, ref or schema object\n    // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents\n    data // to be validated\n    ) {\n        let v;\n        if (typeof schemaKeyRef == \"string\") {\n            v = this.getSchema(schemaKeyRef);\n            if (!v)\n                throw new Error(`no schema with key or ref \"${schemaKeyRef}\"`);\n        }\n        else {\n            v = this.compile(schemaKeyRef);\n        }\n        const valid = v(data);\n        if (!(\"$async\" in v))\n            this.errors = v.errors;\n        return valid;\n    }\n    compile(schema, _meta) {\n        const sch = this._addSchema(schema, _meta);\n        return (sch.validate || this._compileSchemaEnv(sch));\n    }\n    compileAsync(schema, meta) {\n        if (typeof this.opts.loadSchema != \"function\") {\n            throw new Error(\"options.loadSchema should be a function\");\n        }\n        const { loadSchema } = this.opts;\n        return runCompileAsync.call(this, schema, meta);\n        async function runCompileAsync(_schema, _meta) {\n            await loadMetaSchema.call(this, _schema.$schema);\n            const sch = this._addSchema(_schema, _meta);\n            return sch.validate || _compileAsync.call(this, sch);\n        }\n        async function loadMetaSchema($ref) {\n            if ($ref && !this.getSchema($ref)) {\n                await runCompileAsync.call(this, { $ref }, true);\n            }\n        }\n        async function _compileAsync(sch) {\n            try {\n                return this._compileSchemaEnv(sch);\n            }\n            catch (e) {\n                if (!(e instanceof ref_error_1.default))\n                    throw e;\n                checkLoaded.call(this, e);\n                await loadMissingSchema.call(this, e.missingSchema);\n                return _compileAsync.call(this, sch);\n            }\n        }\n        function checkLoaded({ missingSchema: ref, missingRef }) {\n            if (this.refs[ref]) {\n                throw new Error(`AnySchema ${ref} is loaded but ${missingRef} cannot be resolved`);\n            }\n        }\n        async function loadMissingSchema(ref) {\n            const _schema = await _loadSchema.call(this, ref);\n            if (!this.refs[ref])\n                await loadMetaSchema.call(this, _schema.$schema);\n            if (!this.refs[ref])\n                this.addSchema(_schema, ref, meta);\n        }\n        async function _loadSchema(ref) {\n            const p = this._loading[ref];\n            if (p)\n                return p;\n            try {\n                return await (this._loading[ref] = loadSchema(ref));\n            }\n            finally {\n                delete this._loading[ref];\n            }\n        }\n    }\n    // Adds schema to the instance\n    addSchema(schema, // If array is passed, `key` will be ignored\n    key, // Optional schema key. Can be passed to `validate` method instead of schema object or id/ref. One schema per instance can have empty `id` and `key`.\n    _meta, // true if schema is a meta-schema. Used internally, addMetaSchema should be used instead.\n    _validateSchema = this.opts.validateSchema // false to skip schema validation. Used internally, option validateSchema should be used instead.\n    ) {\n        if (Array.isArray(schema)) {\n            for (const sch of schema)\n                this.addSchema(sch, undefined, _meta, _validateSchema);\n            return this;\n        }\n        let id;\n        if (typeof schema === \"object\") {\n            const { schemaId } = this.opts;\n            id = schema[schemaId];\n            if (id !== undefined && typeof id != \"string\") {\n                throw new Error(`schema ${schemaId} must be string`);\n            }\n        }\n        key = (0, resolve_1.normalizeId)(key || id);\n        this._checkUnique(key);\n        this.schemas[key] = this._addSchema(schema, _meta, key, _validateSchema, true);\n        return this;\n    }\n    // Add schema that will be used to validate other schemas\n    // options in META_IGNORE_OPTIONS are alway set to false\n    addMetaSchema(schema, key, // schema key\n    _validateSchema = this.opts.validateSchema // false to skip schema validation, can be used to override validateSchema option for meta-schema\n    ) {\n        this.addSchema(schema, key, true, _validateSchema);\n        return this;\n    }\n    //  Validate schema against its meta-schema\n    validateSchema(schema, throwOrLogError) {\n        if (typeof schema == \"boolean\")\n            return true;\n        let $schema;\n        $schema = schema.$schema;\n        if ($schema !== undefined && typeof $schema != \"string\") {\n            throw new Error(\"$schema must be a string\");\n        }\n        $schema = $schema || this.opts.defaultMeta || this.defaultMeta();\n        if (!$schema) {\n            this.logger.warn(\"meta-schema not available\");\n            this.errors = null;\n            return true;\n        }\n        const valid = this.validate($schema, schema);\n        if (!valid && throwOrLogError) {\n            const message = \"schema is invalid: \" + this.errorsText();\n            if (this.opts.validateSchema === \"log\")\n                this.logger.error(message);\n            else\n                throw new Error(message);\n        }\n        return valid;\n    }\n    // Get compiled schema by `key` or `ref`.\n    // (`key` that was passed to `addSchema` or full schema reference - `schema.$id` or resolved id)\n    getSchema(keyRef) {\n        let sch;\n        while (typeof (sch = getSchEnv.call(this, keyRef)) == \"string\")\n            keyRef = sch;\n        if (sch === undefined) {\n            const { schemaId } = this.opts;\n            const root = new compile_1.SchemaEnv({ schema: {}, schemaId });\n            sch = compile_1.resolveSchema.call(this, root, keyRef);\n            if (!sch)\n                return;\n            this.refs[keyRef] = sch;\n        }\n        return (sch.validate || this._compileSchemaEnv(sch));\n    }\n    // Remove cached schema(s).\n    // If no parameter is passed all schemas but meta-schemas are removed.\n    // If RegExp is passed all schemas with key/id matching pattern but meta-schemas are removed.\n    // Even if schema is referenced by other schemas it still can be removed as other schemas have local references.\n    removeSchema(schemaKeyRef) {\n        if (schemaKeyRef instanceof RegExp) {\n            this._removeAllSchemas(this.schemas, schemaKeyRef);\n            this._removeAllSchemas(this.refs, schemaKeyRef);\n            return this;\n        }\n        switch (typeof schemaKeyRef) {\n            case \"undefined\":\n                this._removeAllSchemas(this.schemas);\n                this._removeAllSchemas(this.refs);\n                this._cache.clear();\n                return this;\n            case \"string\": {\n                const sch = getSchEnv.call(this, schemaKeyRef);\n                if (typeof sch == \"object\")\n                    this._cache.delete(sch.schema);\n                delete this.schemas[schemaKeyRef];\n                delete this.refs[schemaKeyRef];\n                return this;\n            }\n            case \"object\": {\n                const cacheKey = schemaKeyRef;\n                this._cache.delete(cacheKey);\n                let id = schemaKeyRef[this.opts.schemaId];\n                if (id) {\n                    id = (0, resolve_1.normalizeId)(id);\n                    delete this.schemas[id];\n                    delete this.refs[id];\n                }\n                return this;\n            }\n            default:\n                throw new Error(\"ajv.removeSchema: invalid parameter\");\n        }\n    }\n    // add \"vocabulary\" - a collection of keywords\n    addVocabulary(definitions) {\n        for (const def of definitions)\n            this.addKeyword(def);\n        return this;\n    }\n    addKeyword(kwdOrDef, def // deprecated\n    ) {\n        let keyword;\n        if (typeof kwdOrDef == \"string\") {\n            keyword = kwdOrDef;\n            if (typeof def == \"object\") {\n                this.logger.warn(\"these parameters are deprecated, see docs for addKeyword\");\n                def.keyword = keyword;\n            }\n        }\n        else if (typeof kwdOrDef == \"object\" && def === undefined) {\n            def = kwdOrDef;\n            keyword = def.keyword;\n            if (Array.isArray(keyword) && !keyword.length) {\n                throw new Error(\"addKeywords: keyword must be string or non-empty array\");\n            }\n        }\n        else {\n            throw new Error(\"invalid addKeywords parameters\");\n        }\n        checkKeyword.call(this, keyword, def);\n        if (!def) {\n            (0, util_1.eachItem)(keyword, (kwd) => addRule.call(this, kwd));\n            return this;\n        }\n        keywordMetaschema.call(this, def);\n        const definition = {\n            ...def,\n            type: (0, dataType_1.getJSONTypes)(def.type),\n            schemaType: (0, dataType_1.getJSONTypes)(def.schemaType),\n        };\n        (0, util_1.eachItem)(keyword, definition.type.length === 0\n            ? (k) => addRule.call(this, k, definition)\n            : (k) => definition.type.forEach((t) => addRule.call(this, k, definition, t)));\n        return this;\n    }\n    getKeyword(keyword) {\n        const rule = this.RULES.all[keyword];\n        return typeof rule == \"object\" ? rule.definition : !!rule;\n    }\n    // Remove keyword\n    removeKeyword(keyword) {\n        // TODO return type should be Ajv\n        const { RULES } = this;\n        delete RULES.keywords[keyword];\n        delete RULES.all[keyword];\n        for (const group of RULES.rules) {\n            const i = group.rules.findIndex((rule) => rule.keyword === keyword);\n            if (i >= 0)\n                group.rules.splice(i, 1);\n        }\n        return this;\n    }\n    // Add format\n    addFormat(name, format) {\n        if (typeof format == \"string\")\n            format = new RegExp(format);\n        this.formats[name] = format;\n        return this;\n    }\n    errorsText(errors = this.errors, // optional array of validation errors\n    { separator = \", \", dataVar = \"data\" } = {} // optional options with properties `separator` and `dataVar`\n    ) {\n        if (!errors || errors.length === 0)\n            return \"No errors\";\n        return errors\n            .map((e) => `${dataVar}${e.instancePath} ${e.message}`)\n            .reduce((text, msg) => text + separator + msg);\n    }\n    $dataMetaSchema(metaSchema, keywordsJsonPointers) {\n        const rules = this.RULES.all;\n        metaSchema = JSON.parse(JSON.stringify(metaSchema));\n        for (const jsonPointer of keywordsJsonPointers) {\n            const segments = jsonPointer.split(\"/\").slice(1); // first segment is an empty string\n            let keywords = metaSchema;\n            for (const seg of segments)\n                keywords = keywords[seg];\n            for (const key in rules) {\n                const rule = rules[key];\n                if (typeof rule != \"object\")\n                    continue;\n                const { $data } = rule.definition;\n                const schema = keywords[key];\n                if ($data && schema)\n                    keywords[key] = schemaOrData(schema);\n            }\n        }\n        return metaSchema;\n    }\n    _removeAllSchemas(schemas, regex) {\n        for (const keyRef in schemas) {\n            const sch = schemas[keyRef];\n            if (!regex || regex.test(keyRef)) {\n                if (typeof sch == \"string\") {\n                    delete schemas[keyRef];\n                }\n                else if (sch && !sch.meta) {\n                    this._cache.delete(sch.schema);\n                    delete schemas[keyRef];\n                }\n            }\n        }\n    }\n    _addSchema(schema, meta, baseId, validateSchema = this.opts.validateSchema, addSchema = this.opts.addUsedSchema) {\n        let id;\n        const { schemaId } = this.opts;\n        if (typeof schema == \"object\") {\n            id = schema[schemaId];\n        }\n        else {\n            if (this.opts.jtd)\n                throw new Error(\"schema must be object\");\n            else if (typeof schema != \"boolean\")\n                throw new Error(\"schema must be object or boolean\");\n        }\n        let sch = this._cache.get(schema);\n        if (sch !== undefined)\n            return sch;\n        baseId = (0, resolve_1.normalizeId)(id || baseId);\n        const localRefs = resolve_1.getSchemaRefs.call(this, schema, baseId);\n        sch = new compile_1.SchemaEnv({ schema, schemaId, meta, baseId, localRefs });\n        this._cache.set(sch.schema, sch);\n        if (addSchema && !baseId.startsWith(\"#\")) {\n            // TODO atm it is allowed to overwrite schemas without id (instead of not adding them)\n            if (baseId)\n                this._checkUnique(baseId);\n            this.refs[baseId] = sch;\n        }\n        if (validateSchema)\n            this.validateSchema(schema, true);\n        return sch;\n    }\n    _checkUnique(id) {\n        if (this.schemas[id] || this.refs[id]) {\n            throw new Error(`schema with key or id \"${id}\" already exists`);\n        }\n    }\n    _compileSchemaEnv(sch) {\n        if (sch.meta)\n            this._compileMetaSchema(sch);\n        else\n            compile_1.compileSchema.call(this, sch);\n        /* istanbul ignore if */\n        if (!sch.validate)\n            throw new Error(\"ajv implementation error\");\n        return sch.validate;\n    }\n    _compileMetaSchema(sch) {\n        const currentOpts = this.opts;\n        this.opts = this._metaOpts;\n        try {\n            compile_1.compileSchema.call(this, sch);\n        }\n        finally {\n            this.opts = currentOpts;\n        }\n    }\n}\nAjv.ValidationError = validation_error_1.default;\nAjv.MissingRefError = ref_error_1.default;\nexports.default = Ajv;\nfunction checkOptions(checkOpts, options, msg, log = \"error\") {\n    for (const key in checkOpts) {\n        const opt = key;\n        if (opt in options)\n            this.logger[log](`${msg}: option ${key}. ${checkOpts[opt]}`);\n    }\n}\nfunction getSchEnv(keyRef) {\n    keyRef = (0, resolve_1.normalizeId)(keyRef); // TODO tests fail without this line\n    return this.schemas[keyRef] || this.refs[keyRef];\n}\nfunction addInitialSchemas() {\n    const optsSchemas = this.opts.schemas;\n    if (!optsSchemas)\n        return;\n    if (Array.isArray(optsSchemas))\n        this.addSchema(optsSchemas);\n    else\n        for (const key in optsSchemas)\n            this.addSchema(optsSchemas[key], key);\n}\nfunction addInitialFormats() {\n    for (const name in this.opts.formats) {\n        const format = this.opts.formats[name];\n        if (format)\n            this.addFormat(name, format);\n    }\n}\nfunction addInitialKeywords(defs) {\n    if (Array.isArray(defs)) {\n        this.addVocabulary(defs);\n        return;\n    }\n    this.logger.warn(\"keywords option as map is deprecated, pass array\");\n    for (const keyword in defs) {\n        const def = defs[keyword];\n        if (!def.keyword)\n            def.keyword = keyword;\n        this.addKeyword(def);\n    }\n}\nfunction getMetaSchemaOptions() {\n    const metaOpts = { ...this.opts };\n    for (const opt of META_IGNORE_OPTIONS)\n        delete metaOpts[opt];\n    return metaOpts;\n}\nconst noLogs = { log() { }, warn() { }, error() { } };\nfunction getLogger(logger) {\n    if (logger === false)\n        return noLogs;\n    if (logger === undefined)\n        return console;\n    if (logger.log && logger.warn && logger.error)\n        return logger;\n    throw new Error(\"logger must implement log, warn and error methods\");\n}\nconst KEYWORD_NAME = /^[a-z_$][a-z0-9_$:-]*$/i;\nfunction checkKeyword(keyword, def) {\n    const { RULES } = this;\n    (0, util_1.eachItem)(keyword, (kwd) => {\n        if (RULES.keywords[kwd])\n            throw new Error(`Keyword ${kwd} is already defined`);\n        if (!KEYWORD_NAME.test(kwd))\n            throw new Error(`Keyword ${kwd} has invalid name`);\n    });\n    if (!def)\n        return;\n    if (def.$data && !(\"code\" in def || \"validate\" in def)) {\n        throw new Error('$data keyword must have \"code\" or \"validate\" function');\n    }\n}\nfunction addRule(keyword, definition, dataType) {\n    var _a;\n    const post = definition === null || definition === void 0 ? void 0 : definition.post;\n    if (dataType && post)\n        throw new Error('keyword with \"post\" flag cannot have \"type\"');\n    const { RULES } = this;\n    let ruleGroup = post ? RULES.post : RULES.rules.find(({ type: t }) => t === dataType);\n    if (!ruleGroup) {\n        ruleGroup = { type: dataType, rules: [] };\n        RULES.rules.push(ruleGroup);\n    }\n    RULES.keywords[keyword] = true;\n    if (!definition)\n        return;\n    const rule = {\n        keyword,\n        definition: {\n            ...definition,\n            type: (0, dataType_1.getJSONTypes)(definition.type),\n            schemaType: (0, dataType_1.getJSONTypes)(definition.schemaType),\n        },\n    };\n    if (definition.before)\n        addBeforeRule.call(this, ruleGroup, rule, definition.before);\n    else\n        ruleGroup.rules.push(rule);\n    RULES.all[keyword] = rule;\n    (_a = definition.implements) === null || _a === void 0 ? void 0 : _a.forEach((kwd) => this.addKeyword(kwd));\n}\nfunction addBeforeRule(ruleGroup, rule, before) {\n    const i = ruleGroup.rules.findIndex((_rule) => _rule.keyword === before);\n    if (i >= 0) {\n        ruleGroup.rules.splice(i, 0, rule);\n    }\n    else {\n        ruleGroup.rules.push(rule);\n        this.logger.warn(`rule ${before} is not defined`);\n    }\n}\nfunction keywordMetaschema(def) {\n    let { metaSchema } = def;\n    if (metaSchema === undefined)\n        return;\n    if (def.$data && this.opts.$data)\n        metaSchema = schemaOrData(metaSchema);\n    def.validateSchema = this.compile(metaSchema, true);\n}\nconst $dataRef = {\n    $ref: \"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#\",\n};\nfunction schemaOrData(schema) {\n    return { anyOf: [schema, $dataRef] };\n}\n//# sourceMappingURL=core.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n// https://github.com/ajv-validator/ajv/issues/889\nconst equal = require(\"fast-deep-equal\");\nequal.code = 'require(\"ajv/dist/runtime/equal\").default';\nexports.default = equal;\n//# sourceMappingURL=equal.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n// https://mathiasbynens.be/notes/javascript-encoding\n// https://github.com/bestiejs/punycode.js - punycode.ucs2.decode\nfunction ucs2length(str) {\n    const len = str.length;\n    let length = 0;\n    let pos = 0;\n    let value;\n    while (pos < len) {\n        length++;\n        value = str.charCodeAt(pos++);\n        if (value >= 0xd800 && value <= 0xdbff && pos < len) {\n            // high surrogate, and there is a next character\n            value = str.charCodeAt(pos);\n            if ((value & 0xfc00) === 0xdc00)\n                pos++; // low surrogate\n        }\n    }\n    return length;\n}\nexports.default = ucs2length;\nucs2length.code = 'require(\"ajv/dist/runtime/ucs2length\").default';\n//# sourceMappingURL=ucs2length.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst uri = require(\"fast-uri\");\nuri.code = 'require(\"ajv/dist/runtime/uri\").default';\nexports.default = uri;\n//# sourceMappingURL=uri.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nclass ValidationError extends Error {\n    constructor(errors) {\n        super(\"validation failed\");\n        this.errors = errors;\n        this.ajv = this.validation = true;\n    }\n}\nexports.default = ValidationError;\n//# sourceMappingURL=validation_error.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateAdditionalItems = void 0;\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: ({ params: { len } }) => (0, codegen_1.str) `must NOT have more than ${len} items`,\n    params: ({ params: { len } }) => (0, codegen_1._) `{limit: ${len}}`,\n};\nconst def = {\n    keyword: \"additionalItems\",\n    type: \"array\",\n    schemaType: [\"boolean\", \"object\"],\n    before: \"uniqueItems\",\n    error,\n    code(cxt) {\n        const { parentSchema, it } = cxt;\n        const { items } = parentSchema;\n        if (!Array.isArray(items)) {\n            (0, util_1.checkStrictMode)(it, '\"additionalItems\" is ignored when \"items\" is not an array of schemas');\n            return;\n        }\n        validateAdditionalItems(cxt, items);\n    },\n};\nfunction validateAdditionalItems(cxt, items) {\n    const { gen, schema, data, keyword, it } = cxt;\n    it.items = true;\n    const len = gen.const(\"len\", (0, codegen_1._) `${data}.length`);\n    if (schema === false) {\n        cxt.setParams({ len: items.length });\n        cxt.pass((0, codegen_1._) `${len} <= ${items.length}`);\n    }\n    else if (typeof schema == \"object\" && !(0, util_1.alwaysValidSchema)(it, schema)) {\n        const valid = gen.var(\"valid\", (0, codegen_1._) `${len} <= ${items.length}`); // TODO var\n        gen.if((0, codegen_1.not)(valid), () => validateItems(valid));\n        cxt.ok(valid);\n    }\n    function validateItems(valid) {\n        gen.forRange(\"i\", items.length, len, (i) => {\n            cxt.subschema({ keyword, dataProp: i, dataPropType: util_1.Type.Num }, valid);\n            if (!it.allErrors)\n                gen.if((0, codegen_1.not)(valid), () => gen.break());\n        });\n    }\n}\nexports.validateAdditionalItems = validateAdditionalItems;\nexports.default = def;\n//# sourceMappingURL=additionalItems.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst names_1 = require(\"../../compile/names\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: \"must NOT have additional properties\",\n    params: ({ params }) => (0, codegen_1._) `{additionalProperty: ${params.additionalProperty}}`,\n};\nconst def = {\n    keyword: \"additionalProperties\",\n    type: [\"object\"],\n    schemaType: [\"boolean\", \"object\"],\n    allowUndefined: true,\n    trackErrors: true,\n    error,\n    code(cxt) {\n        const { gen, schema, parentSchema, data, errsCount, it } = cxt;\n        /* istanbul ignore if */\n        if (!errsCount)\n            throw new Error(\"ajv implementation error\");\n        const { allErrors, opts } = it;\n        it.props = true;\n        if (opts.removeAdditional !== \"all\" && (0, util_1.alwaysValidSchema)(it, schema))\n            return;\n        const props = (0, code_1.allSchemaProperties)(parentSchema.properties);\n        const patProps = (0, code_1.allSchemaProperties)(parentSchema.patternProperties);\n        checkAdditionalProperties();\n        cxt.ok((0, codegen_1._) `${errsCount} === ${names_1.default.errors}`);\n        function checkAdditionalProperties() {\n            gen.forIn(\"key\", data, (key) => {\n                if (!props.length && !patProps.length)\n                    additionalPropertyCode(key);\n                else\n                    gen.if(isAdditional(key), () => additionalPropertyCode(key));\n            });\n        }\n        function isAdditional(key) {\n            let definedProp;\n            if (props.length > 8) {\n                // TODO maybe an option instead of hard-coded 8?\n                const propsSchema = (0, util_1.schemaRefOrVal)(it, parentSchema.properties, \"properties\");\n                definedProp = (0, code_1.isOwnProperty)(gen, propsSchema, key);\n            }\n            else if (props.length) {\n                definedProp = (0, codegen_1.or)(...props.map((p) => (0, codegen_1._) `${key} === ${p}`));\n            }\n            else {\n                definedProp = codegen_1.nil;\n            }\n            if (patProps.length) {\n                definedProp = (0, codegen_1.or)(definedProp, ...patProps.map((p) => (0, codegen_1._) `${(0, code_1.usePattern)(cxt, p)}.test(${key})`));\n            }\n            return (0, codegen_1.not)(definedProp);\n        }\n        function deleteAdditional(key) {\n            gen.code((0, codegen_1._) `delete ${data}[${key}]`);\n        }\n        function additionalPropertyCode(key) {\n            if (opts.removeAdditional === \"all\" || (opts.removeAdditional && schema === false)) {\n                deleteAdditional(key);\n                return;\n            }\n            if (schema === false) {\n                cxt.setParams({ additionalProperty: key });\n                cxt.error();\n                if (!allErrors)\n                    gen.break();\n                return;\n            }\n            if (typeof schema == \"object\" && !(0, util_1.alwaysValidSchema)(it, schema)) {\n                const valid = gen.name(\"valid\");\n                if (opts.removeAdditional === \"failing\") {\n                    applyAdditionalSchema(key, valid, false);\n                    gen.if((0, codegen_1.not)(valid), () => {\n                        cxt.reset();\n                        deleteAdditional(key);\n                    });\n                }\n                else {\n                    applyAdditionalSchema(key, valid);\n                    if (!allErrors)\n                        gen.if((0, codegen_1.not)(valid), () => gen.break());\n                }\n            }\n        }\n        function applyAdditionalSchema(key, valid, errors) {\n            const subschema = {\n                keyword: \"additionalProperties\",\n                dataProp: key,\n                dataPropType: util_1.Type.Str,\n            };\n            if (errors === false) {\n                Object.assign(subschema, {\n                    compositeRule: true,\n                    createErrors: false,\n                    allErrors: false,\n                });\n            }\n            cxt.subschema(subschema, valid);\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=additionalProperties.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_1 = require(\"../../compile/util\");\nconst def = {\n    keyword: \"allOf\",\n    schemaType: \"array\",\n    code(cxt) {\n        const { gen, schema, it } = cxt;\n        /* istanbul ignore if */\n        if (!Array.isArray(schema))\n            throw new Error(\"ajv implementation error\");\n        const valid = gen.name(\"valid\");\n        schema.forEach((sch, i) => {\n            if ((0, util_1.alwaysValidSchema)(it, sch))\n                return;\n            const schCxt = cxt.subschema({ keyword: \"allOf\", schemaProp: i }, valid);\n            cxt.ok(valid);\n            cxt.mergeEvaluated(schCxt);\n        });\n    },\n};\nexports.default = def;\n//# sourceMappingURL=allOf.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst def = {\n    keyword: \"anyOf\",\n    schemaType: \"array\",\n    trackErrors: true,\n    code: code_1.validateUnion,\n    error: { message: \"must match a schema in anyOf\" },\n};\nexports.default = def;\n//# sourceMappingURL=anyOf.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: ({ params: { min, max } }) => max === undefined\n        ? (0, codegen_1.str) `must contain at least ${min} valid item(s)`\n        : (0, codegen_1.str) `must contain at least ${min} and no more than ${max} valid item(s)`,\n    params: ({ params: { min, max } }) => max === undefined ? (0, codegen_1._) `{minContains: ${min}}` : (0, codegen_1._) `{minContains: ${min}, maxContains: ${max}}`,\n};\nconst def = {\n    keyword: \"contains\",\n    type: \"array\",\n    schemaType: [\"object\", \"boolean\"],\n    before: \"uniqueItems\",\n    trackErrors: true,\n    error,\n    code(cxt) {\n        const { gen, schema, parentSchema, data, it } = cxt;\n        let min;\n        let max;\n        const { minContains, maxContains } = parentSchema;\n        if (it.opts.next) {\n            min = minContains === undefined ? 1 : minContains;\n            max = maxContains;\n        }\n        else {\n            min = 1;\n        }\n        const len = gen.const(\"len\", (0, codegen_1._) `${data}.length`);\n        cxt.setParams({ min, max });\n        if (max === undefined && min === 0) {\n            (0, util_1.checkStrictMode)(it, `\"minContains\" == 0 without \"maxContains\": \"contains\" keyword ignored`);\n            return;\n        }\n        if (max !== undefined && min > max) {\n            (0, util_1.checkStrictMode)(it, `\"minContains\" > \"maxContains\" is always invalid`);\n            cxt.fail();\n            return;\n        }\n        if ((0, util_1.alwaysValidSchema)(it, schema)) {\n            let cond = (0, codegen_1._) `${len} >= ${min}`;\n            if (max !== undefined)\n                cond = (0, codegen_1._) `${cond} && ${len} <= ${max}`;\n            cxt.pass(cond);\n            return;\n        }\n        it.items = true;\n        const valid = gen.name(\"valid\");\n        if (max === undefined && min === 1) {\n            validateItems(valid, () => gen.if(valid, () => gen.break()));\n        }\n        else if (min === 0) {\n            gen.let(valid, true);\n            if (max !== undefined)\n                gen.if((0, codegen_1._) `${data}.length > 0`, validateItemsWithCount);\n        }\n        else {\n            gen.let(valid, false);\n            validateItemsWithCount();\n        }\n        cxt.result(valid, () => cxt.reset());\n        function validateItemsWithCount() {\n            const schValid = gen.name(\"_valid\");\n            const count = gen.let(\"count\", 0);\n            validateItems(schValid, () => gen.if(schValid, () => checkLimits(count)));\n        }\n        function validateItems(_valid, block) {\n            gen.forRange(\"i\", 0, len, (i) => {\n                cxt.subschema({\n                    keyword: \"contains\",\n                    dataProp: i,\n                    dataPropType: util_1.Type.Num,\n                    compositeRule: true,\n                }, _valid);\n                block();\n            });\n        }\n        function checkLimits(count) {\n            gen.code((0, codegen_1._) `${count}++`);\n            if (max === undefined) {\n                gen.if((0, codegen_1._) `${count} >= ${min}`, () => gen.assign(valid, true).break());\n            }\n            else {\n                gen.if((0, codegen_1._) `${count} > ${max}`, () => gen.assign(valid, false).break());\n                if (min === 1)\n                    gen.assign(valid, true);\n                else\n                    gen.if((0, codegen_1._) `${count} >= ${min}`, () => gen.assign(valid, true));\n            }\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=contains.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateSchemaDeps = exports.validatePropertyDeps = exports.error = void 0;\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst code_1 = require(\"../code\");\nexports.error = {\n    message: ({ params: { property, depsCount, deps } }) => {\n        const property_ies = depsCount === 1 ? \"property\" : \"properties\";\n        return (0, codegen_1.str) `must have ${property_ies} ${deps} when property ${property} is present`;\n    },\n    params: ({ params: { property, depsCount, deps, missingProperty } }) => (0, codegen_1._) `{property: ${property},\n    missingProperty: ${missingProperty},\n    depsCount: ${depsCount},\n    deps: ${deps}}`, // TODO change to reference\n};\nconst def = {\n    keyword: \"dependencies\",\n    type: \"object\",\n    schemaType: \"object\",\n    error: exports.error,\n    code(cxt) {\n        const [propDeps, schDeps] = splitDependencies(cxt);\n        validatePropertyDeps(cxt, propDeps);\n        validateSchemaDeps(cxt, schDeps);\n    },\n};\nfunction splitDependencies({ schema }) {\n    const propertyDeps = {};\n    const schemaDeps = {};\n    for (const key in schema) {\n        if (key === \"__proto__\")\n            continue;\n        const deps = Array.isArray(schema[key]) ? propertyDeps : schemaDeps;\n        deps[key] = schema[key];\n    }\n    return [propertyDeps, schemaDeps];\n}\nfunction validatePropertyDeps(cxt, propertyDeps = cxt.schema) {\n    const { gen, data, it } = cxt;\n    if (Object.keys(propertyDeps).length === 0)\n        return;\n    const missing = gen.let(\"missing\");\n    for (const prop in propertyDeps) {\n        const deps = propertyDeps[prop];\n        if (deps.length === 0)\n            continue;\n        const hasProperty = (0, code_1.propertyInData)(gen, data, prop, it.opts.ownProperties);\n        cxt.setParams({\n            property: prop,\n            depsCount: deps.length,\n            deps: deps.join(\", \"),\n        });\n        if (it.allErrors) {\n            gen.if(hasProperty, () => {\n                for (const depProp of deps) {\n                    (0, code_1.checkReportMissingProp)(cxt, depProp);\n                }\n            });\n        }\n        else {\n            gen.if((0, codegen_1._) `${hasProperty} && (${(0, code_1.checkMissingProp)(cxt, deps, missing)})`);\n            (0, code_1.reportMissingProp)(cxt, missing);\n            gen.else();\n        }\n    }\n}\nexports.validatePropertyDeps = validatePropertyDeps;\nfunction validateSchemaDeps(cxt, schemaDeps = cxt.schema) {\n    const { gen, data, keyword, it } = cxt;\n    const valid = gen.name(\"valid\");\n    for (const prop in schemaDeps) {\n        if ((0, util_1.alwaysValidSchema)(it, schemaDeps[prop]))\n            continue;\n        gen.if((0, code_1.propertyInData)(gen, data, prop, it.opts.ownProperties), () => {\n            const schCxt = cxt.subschema({ keyword, schemaProp: prop }, valid);\n            cxt.mergeValidEvaluated(schCxt, valid);\n        }, () => gen.var(valid, true) // TODO var\n        );\n        cxt.ok(valid);\n    }\n}\nexports.validateSchemaDeps = validateSchemaDeps;\nexports.default = def;\n//# sourceMappingURL=dependencies.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: ({ params }) => (0, codegen_1.str) `must match \"${params.ifClause}\" schema`,\n    params: ({ params }) => (0, codegen_1._) `{failingKeyword: ${params.ifClause}}`,\n};\nconst def = {\n    keyword: \"if\",\n    schemaType: [\"object\", \"boolean\"],\n    trackErrors: true,\n    error,\n    code(cxt) {\n        const { gen, parentSchema, it } = cxt;\n        if (parentSchema.then === undefined && parentSchema.else === undefined) {\n            (0, util_1.checkStrictMode)(it, '\"if\" without \"then\" and \"else\" is ignored');\n        }\n        const hasThen = hasSchema(it, \"then\");\n        const hasElse = hasSchema(it, \"else\");\n        if (!hasThen && !hasElse)\n            return;\n        const valid = gen.let(\"valid\", true);\n        const schValid = gen.name(\"_valid\");\n        validateIf();\n        cxt.reset();\n        if (hasThen && hasElse) {\n            const ifClause = gen.let(\"ifClause\");\n            cxt.setParams({ ifClause });\n            gen.if(schValid, validateClause(\"then\", ifClause), validateClause(\"else\", ifClause));\n        }\n        else if (hasThen) {\n            gen.if(schValid, validateClause(\"then\"));\n        }\n        else {\n            gen.if((0, codegen_1.not)(schValid), validateClause(\"else\"));\n        }\n        cxt.pass(valid, () => cxt.error(true));\n        function validateIf() {\n            const schCxt = cxt.subschema({\n                keyword: \"if\",\n                compositeRule: true,\n                createErrors: false,\n                allErrors: false,\n            }, schValid);\n            cxt.mergeEvaluated(schCxt);\n        }\n        function validateClause(keyword, ifClause) {\n            return () => {\n                const schCxt = cxt.subschema({ keyword }, schValid);\n                gen.assign(valid, schValid);\n                cxt.mergeValidEvaluated(schCxt, valid);\n                if (ifClause)\n                    gen.assign(ifClause, (0, codegen_1._) `${keyword}`);\n                else\n                    cxt.setParams({ ifClause: keyword });\n            };\n        }\n    },\n};\nfunction hasSchema(it, keyword) {\n    const schema = it.schema[keyword];\n    return schema !== undefined && !(0, util_1.alwaysValidSchema)(it, schema);\n}\nexports.default = def;\n//# sourceMappingURL=if.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst additionalItems_1 = require(\"./additionalItems\");\nconst prefixItems_1 = require(\"./prefixItems\");\nconst items_1 = require(\"./items\");\nconst items2020_1 = require(\"./items2020\");\nconst contains_1 = require(\"./contains\");\nconst dependencies_1 = require(\"./dependencies\");\nconst propertyNames_1 = require(\"./propertyNames\");\nconst additionalProperties_1 = require(\"./additionalProperties\");\nconst properties_1 = require(\"./properties\");\nconst patternProperties_1 = require(\"./patternProperties\");\nconst not_1 = require(\"./not\");\nconst anyOf_1 = require(\"./anyOf\");\nconst oneOf_1 = require(\"./oneOf\");\nconst allOf_1 = require(\"./allOf\");\nconst if_1 = require(\"./if\");\nconst thenElse_1 = require(\"./thenElse\");\nfunction getApplicator(draft2020 = false) {\n    const applicator = [\n        // any\n        not_1.default,\n        anyOf_1.default,\n        oneOf_1.default,\n        allOf_1.default,\n        if_1.default,\n        thenElse_1.default,\n        // object\n        propertyNames_1.default,\n        additionalProperties_1.default,\n        dependencies_1.default,\n        properties_1.default,\n        patternProperties_1.default,\n    ];\n    // array\n    if (draft2020)\n        applicator.push(prefixItems_1.default, items2020_1.default);\n    else\n        applicator.push(additionalItems_1.default, items_1.default);\n    applicator.push(contains_1.default);\n    return applicator;\n}\nexports.default = getApplicator;\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateTuple = void 0;\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst code_1 = require(\"../code\");\nconst def = {\n    keyword: \"items\",\n    type: \"array\",\n    schemaType: [\"object\", \"array\", \"boolean\"],\n    before: \"uniqueItems\",\n    code(cxt) {\n        const { schema, it } = cxt;\n        if (Array.isArray(schema))\n            return validateTuple(cxt, \"additionalItems\", schema);\n        it.items = true;\n        if ((0, util_1.alwaysValidSchema)(it, schema))\n            return;\n        cxt.ok((0, code_1.validateArray)(cxt));\n    },\n};\nfunction validateTuple(cxt, extraItems, schArr = cxt.schema) {\n    const { gen, parentSchema, data, keyword, it } = cxt;\n    checkStrictTuple(parentSchema);\n    if (it.opts.unevaluated && schArr.length && it.items !== true) {\n        it.items = util_1.mergeEvaluated.items(gen, schArr.length, it.items);\n    }\n    const valid = gen.name(\"valid\");\n    const len = gen.const(\"len\", (0, codegen_1._) `${data}.length`);\n    schArr.forEach((sch, i) => {\n        if ((0, util_1.alwaysValidSchema)(it, sch))\n            return;\n        gen.if((0, codegen_1._) `${len} > ${i}`, () => cxt.subschema({\n            keyword,\n            schemaProp: i,\n            dataProp: i,\n        }, valid));\n        cxt.ok(valid);\n    });\n    function checkStrictTuple(sch) {\n        const { opts, errSchemaPath } = it;\n        const l = schArr.length;\n        const fullTuple = l === sch.minItems && (l === sch.maxItems || sch[extraItems] === false);\n        if (opts.strictTuples && !fullTuple) {\n            const msg = `\"${keyword}\" is ${l}-tuple, but minItems or maxItems/${extraItems} are not specified or different at path \"${errSchemaPath}\"`;\n            (0, util_1.checkStrictMode)(it, msg, opts.strictTuples);\n        }\n    }\n}\nexports.validateTuple = validateTuple;\nexports.default = def;\n//# sourceMappingURL=items.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst code_1 = require(\"../code\");\nconst additionalItems_1 = require(\"./additionalItems\");\nconst error = {\n    message: ({ params: { len } }) => (0, codegen_1.str) `must NOT have more than ${len} items`,\n    params: ({ params: { len } }) => (0, codegen_1._) `{limit: ${len}}`,\n};\nconst def = {\n    keyword: \"items\",\n    type: \"array\",\n    schemaType: [\"object\", \"boolean\"],\n    before: \"uniqueItems\",\n    error,\n    code(cxt) {\n        const { schema, parentSchema, it } = cxt;\n        const { prefixItems } = parentSchema;\n        it.items = true;\n        if ((0, util_1.alwaysValidSchema)(it, schema))\n            return;\n        if (prefixItems)\n            (0, additionalItems_1.validateAdditionalItems)(cxt, prefixItems);\n        else\n            cxt.ok((0, code_1.validateArray)(cxt));\n    },\n};\nexports.default = def;\n//# sourceMappingURL=items2020.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_1 = require(\"../../compile/util\");\nconst def = {\n    keyword: \"not\",\n    schemaType: [\"object\", \"boolean\"],\n    trackErrors: true,\n    code(cxt) {\n        const { gen, schema, it } = cxt;\n        if ((0, util_1.alwaysValidSchema)(it, schema)) {\n            cxt.fail();\n            return;\n        }\n        const valid = gen.name(\"valid\");\n        cxt.subschema({\n            keyword: \"not\",\n            compositeRule: true,\n            createErrors: false,\n            allErrors: false,\n        }, valid);\n        cxt.failResult(valid, () => cxt.reset(), () => cxt.error());\n    },\n    error: { message: \"must NOT be valid\" },\n};\nexports.default = def;\n//# sourceMappingURL=not.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: \"must match exactly one schema in oneOf\",\n    params: ({ params }) => (0, codegen_1._) `{passingSchemas: ${params.passing}}`,\n};\nconst def = {\n    keyword: \"oneOf\",\n    schemaType: \"array\",\n    trackErrors: true,\n    error,\n    code(cxt) {\n        const { gen, schema, parentSchema, it } = cxt;\n        /* istanbul ignore if */\n        if (!Array.isArray(schema))\n            throw new Error(\"ajv implementation error\");\n        if (it.opts.discriminator && parentSchema.discriminator)\n            return;\n        const schArr = schema;\n        const valid = gen.let(\"valid\", false);\n        const passing = gen.let(\"passing\", null);\n        const schValid = gen.name(\"_valid\");\n        cxt.setParams({ passing });\n        // TODO possibly fail straight away (with warning or exception) if there are two empty always valid schemas\n        gen.block(validateOneOf);\n        cxt.result(valid, () => cxt.reset(), () => cxt.error(true));\n        function validateOneOf() {\n            schArr.forEach((sch, i) => {\n                let schCxt;\n                if ((0, util_1.alwaysValidSchema)(it, sch)) {\n                    gen.var(schValid, true);\n                }\n                else {\n                    schCxt = cxt.subschema({\n                        keyword: \"oneOf\",\n                        schemaProp: i,\n                        compositeRule: true,\n                    }, schValid);\n                }\n                if (i > 0) {\n                    gen\n                        .if((0, codegen_1._) `${schValid} && ${valid}`)\n                        .assign(valid, false)\n                        .assign(passing, (0, codegen_1._) `[${passing}, ${i}]`)\n                        .else();\n                }\n                gen.if(schValid, () => {\n                    gen.assign(valid, true);\n                    gen.assign(passing, i);\n                    if (schCxt)\n                        cxt.mergeEvaluated(schCxt, codegen_1.Name);\n                });\n            });\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=oneOf.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst util_2 = require(\"../../compile/util\");\nconst def = {\n    keyword: \"patternProperties\",\n    type: \"object\",\n    schemaType: \"object\",\n    code(cxt) {\n        const { gen, schema, data, parentSchema, it } = cxt;\n        const { opts } = it;\n        const patterns = (0, code_1.allSchemaProperties)(schema);\n        const alwaysValidPatterns = patterns.filter((p) => (0, util_1.alwaysValidSchema)(it, schema[p]));\n        if (patterns.length === 0 ||\n            (alwaysValidPatterns.length === patterns.length &&\n                (!it.opts.unevaluated || it.props === true))) {\n            return;\n        }\n        const checkProperties = opts.strictSchema && !opts.allowMatchingProperties && parentSchema.properties;\n        const valid = gen.name(\"valid\");\n        if (it.props !== true && !(it.props instanceof codegen_1.Name)) {\n            it.props = (0, util_2.evaluatedPropsToName)(gen, it.props);\n        }\n        const { props } = it;\n        validatePatternProperties();\n        function validatePatternProperties() {\n            for (const pat of patterns) {\n                if (checkProperties)\n                    checkMatchingProperties(pat);\n                if (it.allErrors) {\n                    validateProperties(pat);\n                }\n                else {\n                    gen.var(valid, true); // TODO var\n                    validateProperties(pat);\n                    gen.if(valid);\n                }\n            }\n        }\n        function checkMatchingProperties(pat) {\n            for (const prop in checkProperties) {\n                if (new RegExp(pat).test(prop)) {\n                    (0, util_1.checkStrictMode)(it, `property ${prop} matches pattern ${pat} (use allowMatchingProperties)`);\n                }\n            }\n        }\n        function validateProperties(pat) {\n            gen.forIn(\"key\", data, (key) => {\n                gen.if((0, codegen_1._) `${(0, code_1.usePattern)(cxt, pat)}.test(${key})`, () => {\n                    const alwaysValid = alwaysValidPatterns.includes(pat);\n                    if (!alwaysValid) {\n                        cxt.subschema({\n                            keyword: \"patternProperties\",\n                            schemaProp: pat,\n                            dataProp: key,\n                            dataPropType: util_2.Type.Str,\n                        }, valid);\n                    }\n                    if (it.opts.unevaluated && props !== true) {\n                        gen.assign((0, codegen_1._) `${props}[${key}]`, true);\n                    }\n                    else if (!alwaysValid && !it.allErrors) {\n                        // can short-circuit if `unevaluatedProperties` is not supported (opts.next === false)\n                        // or if all properties were evaluated (props === true)\n                        gen.if((0, codegen_1.not)(valid), () => gen.break());\n                    }\n                });\n            });\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=patternProperties.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst items_1 = require(\"./items\");\nconst def = {\n    keyword: \"prefixItems\",\n    type: \"array\",\n    schemaType: [\"array\"],\n    before: \"uniqueItems\",\n    code: (cxt) => (0, items_1.validateTuple)(cxt, \"items\"),\n};\nexports.default = def;\n//# sourceMappingURL=prefixItems.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst validate_1 = require(\"../../compile/validate\");\nconst code_1 = require(\"../code\");\nconst util_1 = require(\"../../compile/util\");\nconst additionalProperties_1 = require(\"./additionalProperties\");\nconst def = {\n    keyword: \"properties\",\n    type: \"object\",\n    schemaType: \"object\",\n    code(cxt) {\n        const { gen, schema, parentSchema, data, it } = cxt;\n        if (it.opts.removeAdditional === \"all\" && parentSchema.additionalProperties === undefined) {\n            additionalProperties_1.default.code(new validate_1.KeywordCxt(it, additionalProperties_1.default, \"additionalProperties\"));\n        }\n        const allProps = (0, code_1.allSchemaProperties)(schema);\n        for (const prop of allProps) {\n            it.definedProperties.add(prop);\n        }\n        if (it.opts.unevaluated && allProps.length && it.props !== true) {\n            it.props = util_1.mergeEvaluated.props(gen, (0, util_1.toHash)(allProps), it.props);\n        }\n        const properties = allProps.filter((p) => !(0, util_1.alwaysValidSchema)(it, schema[p]));\n        if (properties.length === 0)\n            return;\n        const valid = gen.name(\"valid\");\n        for (const prop of properties) {\n            if (hasDefault(prop)) {\n                applyPropertySchema(prop);\n            }\n            else {\n                gen.if((0, code_1.propertyInData)(gen, data, prop, it.opts.ownProperties));\n                applyPropertySchema(prop);\n                if (!it.allErrors)\n                    gen.else().var(valid, true);\n                gen.endIf();\n            }\n            cxt.it.definedProperties.add(prop);\n            cxt.ok(valid);\n        }\n        function hasDefault(prop) {\n            return it.opts.useDefaults && !it.compositeRule && schema[prop].default !== undefined;\n        }\n        function applyPropertySchema(prop) {\n            cxt.subschema({\n                keyword: \"properties\",\n                schemaProp: prop,\n                dataProp: prop,\n            }, valid);\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=properties.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: \"property name must be valid\",\n    params: ({ params }) => (0, codegen_1._) `{propertyName: ${params.propertyName}}`,\n};\nconst def = {\n    keyword: \"propertyNames\",\n    type: \"object\",\n    schemaType: [\"object\", \"boolean\"],\n    error,\n    code(cxt) {\n        const { gen, schema, data, it } = cxt;\n        if ((0, util_1.alwaysValidSchema)(it, schema))\n            return;\n        const valid = gen.name(\"valid\");\n        gen.forIn(\"key\", data, (key) => {\n            cxt.setParams({ propertyName: key });\n            cxt.subschema({\n                keyword: \"propertyNames\",\n                data: key,\n                dataTypes: [\"string\"],\n                propertyName: key,\n                compositeRule: true,\n            }, valid);\n            gen.if((0, codegen_1.not)(valid), () => {\n                cxt.error(true);\n                if (!it.allErrors)\n                    gen.break();\n            });\n        });\n        cxt.ok(valid);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=propertyNames.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_1 = require(\"../../compile/util\");\nconst def = {\n    keyword: [\"then\", \"else\"],\n    schemaType: [\"object\", \"boolean\"],\n    code({ keyword, parentSchema, it }) {\n        if (parentSchema.if === undefined)\n            (0, util_1.checkStrictMode)(it, `\"${keyword}\" without \"if\" is ignored`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=thenElse.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateUnion = exports.validateArray = exports.usePattern = exports.callValidateCode = exports.schemaProperties = exports.allSchemaProperties = exports.noPropertyInData = exports.propertyInData = exports.isOwnProperty = exports.hasPropFunc = exports.reportMissingProp = exports.checkMissingProp = exports.checkReportMissingProp = void 0;\nconst codegen_1 = require(\"../compile/codegen\");\nconst util_1 = require(\"../compile/util\");\nconst names_1 = require(\"../compile/names\");\nconst util_2 = require(\"../compile/util\");\nfunction checkReportMissingProp(cxt, prop) {\n    const { gen, data, it } = cxt;\n    gen.if(noPropertyInData(gen, data, prop, it.opts.ownProperties), () => {\n        cxt.setParams({ missingProperty: (0, codegen_1._) `${prop}` }, true);\n        cxt.error();\n    });\n}\nexports.checkReportMissingProp = checkReportMissingProp;\nfunction checkMissingProp({ gen, data, it: { opts } }, properties, missing) {\n    return (0, codegen_1.or)(...properties.map((prop) => (0, codegen_1.and)(noPropertyInData(gen, data, prop, opts.ownProperties), (0, codegen_1._) `${missing} = ${prop}`)));\n}\nexports.checkMissingProp = checkMissingProp;\nfunction reportMissingProp(cxt, missing) {\n    cxt.setParams({ missingProperty: missing }, true);\n    cxt.error();\n}\nexports.reportMissingProp = reportMissingProp;\nfunction hasPropFunc(gen) {\n    return gen.scopeValue(\"func\", {\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        ref: Object.prototype.hasOwnProperty,\n        code: (0, codegen_1._) `Object.prototype.hasOwnProperty`,\n    });\n}\nexports.hasPropFunc = hasPropFunc;\nfunction isOwnProperty(gen, data, property) {\n    return (0, codegen_1._) `${hasPropFunc(gen)}.call(${data}, ${property})`;\n}\nexports.isOwnProperty = isOwnProperty;\nfunction propertyInData(gen, data, property, ownProperties) {\n    const cond = (0, codegen_1._) `${data}${(0, codegen_1.getProperty)(property)} !== undefined`;\n    return ownProperties ? (0, codegen_1._) `${cond} && ${isOwnProperty(gen, data, property)}` : cond;\n}\nexports.propertyInData = propertyInData;\nfunction noPropertyInData(gen, data, property, ownProperties) {\n    const cond = (0, codegen_1._) `${data}${(0, codegen_1.getProperty)(property)} === undefined`;\n    return ownProperties ? (0, codegen_1.or)(cond, (0, codegen_1.not)(isOwnProperty(gen, data, property))) : cond;\n}\nexports.noPropertyInData = noPropertyInData;\nfunction allSchemaProperties(schemaMap) {\n    return schemaMap ? Object.keys(schemaMap).filter((p) => p !== \"__proto__\") : [];\n}\nexports.allSchemaProperties = allSchemaProperties;\nfunction schemaProperties(it, schemaMap) {\n    return allSchemaProperties(schemaMap).filter((p) => !(0, util_1.alwaysValidSchema)(it, schemaMap[p]));\n}\nexports.schemaProperties = schemaProperties;\nfunction callValidateCode({ schemaCode, data, it: { gen, topSchemaRef, schemaPath, errorPath }, it }, func, context, passSchema) {\n    const dataAndSchema = passSchema ? (0, codegen_1._) `${schemaCode}, ${data}, ${topSchemaRef}${schemaPath}` : data;\n    const valCxt = [\n        [names_1.default.instancePath, (0, codegen_1.strConcat)(names_1.default.instancePath, errorPath)],\n        [names_1.default.parentData, it.parentData],\n        [names_1.default.parentDataProperty, it.parentDataProperty],\n        [names_1.default.rootData, names_1.default.rootData],\n    ];\n    if (it.opts.dynamicRef)\n        valCxt.push([names_1.default.dynamicAnchors, names_1.default.dynamicAnchors]);\n    const args = (0, codegen_1._) `${dataAndSchema}, ${gen.object(...valCxt)}`;\n    return context !== codegen_1.nil ? (0, codegen_1._) `${func}.call(${context}, ${args})` : (0, codegen_1._) `${func}(${args})`;\n}\nexports.callValidateCode = callValidateCode;\nconst newRegExp = (0, codegen_1._) `new RegExp`;\nfunction usePattern({ gen, it: { opts } }, pattern) {\n    const u = opts.unicodeRegExp ? \"u\" : \"\";\n    const { regExp } = opts.code;\n    const rx = regExp(pattern, u);\n    return gen.scopeValue(\"pattern\", {\n        key: rx.toString(),\n        ref: rx,\n        code: (0, codegen_1._) `${regExp.code === \"new RegExp\" ? newRegExp : (0, util_2.useFunc)(gen, regExp)}(${pattern}, ${u})`,\n    });\n}\nexports.usePattern = usePattern;\nfunction validateArray(cxt) {\n    const { gen, data, keyword, it } = cxt;\n    const valid = gen.name(\"valid\");\n    if (it.allErrors) {\n        const validArr = gen.let(\"valid\", true);\n        validateItems(() => gen.assign(validArr, false));\n        return validArr;\n    }\n    gen.var(valid, true);\n    validateItems(() => gen.break());\n    return valid;\n    function validateItems(notValid) {\n        const len = gen.const(\"len\", (0, codegen_1._) `${data}.length`);\n        gen.forRange(\"i\", 0, len, (i) => {\n            cxt.subschema({\n                keyword,\n                dataProp: i,\n                dataPropType: util_1.Type.Num,\n            }, valid);\n            gen.if((0, codegen_1.not)(valid), notValid);\n        });\n    }\n}\nexports.validateArray = validateArray;\nfunction validateUnion(cxt) {\n    const { gen, schema, keyword, it } = cxt;\n    /* istanbul ignore if */\n    if (!Array.isArray(schema))\n        throw new Error(\"ajv implementation error\");\n    const alwaysValid = schema.some((sch) => (0, util_1.alwaysValidSchema)(it, sch));\n    if (alwaysValid && !it.opts.unevaluated)\n        return;\n    const valid = gen.let(\"valid\", false);\n    const schValid = gen.name(\"_valid\");\n    gen.block(() => schema.forEach((_sch, i) => {\n        const schCxt = cxt.subschema({\n            keyword,\n            schemaProp: i,\n            compositeRule: true,\n        }, schValid);\n        gen.assign(valid, (0, codegen_1._) `${valid} || ${schValid}`);\n        const merged = cxt.mergeValidEvaluated(schCxt, schValid);\n        // can short-circuit if `unevaluatedProperties/Items` not supported (opts.unevaluated !== true)\n        // or if all properties and items were evaluated (it.props === true && it.items === true)\n        if (!merged)\n            gen.if((0, codegen_1.not)(valid));\n    }));\n    cxt.result(valid, () => cxt.reset(), () => cxt.error(true));\n}\nexports.validateUnion = validateUnion;\n//# sourceMappingURL=code.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst def = {\n    keyword: \"id\",\n    code() {\n        throw new Error('NOT SUPPORTED: keyword \"id\", use \"$id\" for schema ID');\n    },\n};\nexports.default = def;\n//# sourceMappingURL=id.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst id_1 = require(\"./id\");\nconst ref_1 = require(\"./ref\");\nconst core = [\n    \"$schema\",\n    \"$id\",\n    \"$defs\",\n    \"$vocabulary\",\n    { keyword: \"$comment\" },\n    \"definitions\",\n    id_1.default,\n    ref_1.default,\n];\nexports.default = core;\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.callRef = exports.getValidate = void 0;\nconst ref_error_1 = require(\"../../compile/ref_error\");\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst names_1 = require(\"../../compile/names\");\nconst compile_1 = require(\"../../compile\");\nconst util_1 = require(\"../../compile/util\");\nconst def = {\n    keyword: \"$ref\",\n    schemaType: \"string\",\n    code(cxt) {\n        const { gen, schema: $ref, it } = cxt;\n        const { baseId, schemaEnv: env, validateName, opts, self } = it;\n        const { root } = env;\n        if (($ref === \"#\" || $ref === \"#/\") && baseId === root.baseId)\n            return callRootRef();\n        const schOrEnv = compile_1.resolveRef.call(self, root, baseId, $ref);\n        if (schOrEnv === undefined)\n            throw new ref_error_1.default(it.opts.uriResolver, baseId, $ref);\n        if (schOrEnv instanceof compile_1.SchemaEnv)\n            return callValidate(schOrEnv);\n        return inlineRefSchema(schOrEnv);\n        function callRootRef() {\n            if (env === root)\n                return callRef(cxt, validateName, env, env.$async);\n            const rootName = gen.scopeValue(\"root\", { ref: root });\n            return callRef(cxt, (0, codegen_1._) `${rootName}.validate`, root, root.$async);\n        }\n        function callValidate(sch) {\n            const v = getValidate(cxt, sch);\n            callRef(cxt, v, sch, sch.$async);\n        }\n        function inlineRefSchema(sch) {\n            const schName = gen.scopeValue(\"schema\", opts.code.source === true ? { ref: sch, code: (0, codegen_1.stringify)(sch) } : { ref: sch });\n            const valid = gen.name(\"valid\");\n            const schCxt = cxt.subschema({\n                schema: sch,\n                dataTypes: [],\n                schemaPath: codegen_1.nil,\n                topSchemaRef: schName,\n                errSchemaPath: $ref,\n            }, valid);\n            cxt.mergeEvaluated(schCxt);\n            cxt.ok(valid);\n        }\n    },\n};\nfunction getValidate(cxt, sch) {\n    const { gen } = cxt;\n    return sch.validate\n        ? gen.scopeValue(\"validate\", { ref: sch.validate })\n        : (0, codegen_1._) `${gen.scopeValue(\"wrapper\", { ref: sch })}.validate`;\n}\nexports.getValidate = getValidate;\nfunction callRef(cxt, v, sch, $async) {\n    const { gen, it } = cxt;\n    const { allErrors, schemaEnv: env, opts } = it;\n    const passCxt = opts.passContext ? names_1.default.this : codegen_1.nil;\n    if ($async)\n        callAsyncRef();\n    else\n        callSyncRef();\n    function callAsyncRef() {\n        if (!env.$async)\n            throw new Error(\"async schema referenced by sync schema\");\n        const valid = gen.let(\"valid\");\n        gen.try(() => {\n            gen.code((0, codegen_1._) `await ${(0, code_1.callValidateCode)(cxt, v, passCxt)}`);\n            addEvaluatedFrom(v); // TODO will not work with async, it has to be returned with the result\n            if (!allErrors)\n                gen.assign(valid, true);\n        }, (e) => {\n            gen.if((0, codegen_1._) `!(${e} instanceof ${it.ValidationError})`, () => gen.throw(e));\n            addErrorsFrom(e);\n            if (!allErrors)\n                gen.assign(valid, false);\n        });\n        cxt.ok(valid);\n    }\n    function callSyncRef() {\n        cxt.result((0, code_1.callValidateCode)(cxt, v, passCxt), () => addEvaluatedFrom(v), () => addErrorsFrom(v));\n    }\n    function addErrorsFrom(source) {\n        const errs = (0, codegen_1._) `${source}.errors`;\n        gen.assign(names_1.default.vErrors, (0, codegen_1._) `${names_1.default.vErrors} === null ? ${errs} : ${names_1.default.vErrors}.concat(${errs})`); // TODO tagged\n        gen.assign(names_1.default.errors, (0, codegen_1._) `${names_1.default.vErrors}.length`);\n    }\n    function addEvaluatedFrom(source) {\n        var _a;\n        if (!it.opts.unevaluated)\n            return;\n        const schEvaluated = (_a = sch === null || sch === void 0 ? void 0 : sch.validate) === null || _a === void 0 ? void 0 : _a.evaluated;\n        // TODO refactor\n        if (it.props !== true) {\n            if (schEvaluated && !schEvaluated.dynamicProps) {\n                if (schEvaluated.props !== undefined) {\n                    it.props = util_1.mergeEvaluated.props(gen, schEvaluated.props, it.props);\n                }\n            }\n            else {\n                const props = gen.var(\"props\", (0, codegen_1._) `${source}.evaluated.props`);\n                it.props = util_1.mergeEvaluated.props(gen, props, it.props, codegen_1.Name);\n            }\n        }\n        if (it.items !== true) {\n            if (schEvaluated && !schEvaluated.dynamicItems) {\n                if (schEvaluated.items !== undefined) {\n                    it.items = util_1.mergeEvaluated.items(gen, schEvaluated.items, it.items);\n                }\n            }\n            else {\n                const items = gen.var(\"items\", (0, codegen_1._) `${source}.evaluated.items`);\n                it.items = util_1.mergeEvaluated.items(gen, items, it.items, codegen_1.Name);\n            }\n        }\n    }\n}\nexports.callRef = callRef;\nexports.default = def;\n//# sourceMappingURL=ref.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst types_1 = require(\"../discriminator/types\");\nconst compile_1 = require(\"../../compile\");\nconst ref_error_1 = require(\"../../compile/ref_error\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: ({ params: { discrError, tagName } }) => discrError === types_1.DiscrError.Tag\n        ? `tag \"${tagName}\" must be string`\n        : `value of tag \"${tagName}\" must be in oneOf`,\n    params: ({ params: { discrError, tag, tagName } }) => (0, codegen_1._) `{error: ${discrError}, tag: ${tagName}, tagValue: ${tag}}`,\n};\nconst def = {\n    keyword: \"discriminator\",\n    type: \"object\",\n    schemaType: \"object\",\n    error,\n    code(cxt) {\n        const { gen, data, schema, parentSchema, it } = cxt;\n        const { oneOf } = parentSchema;\n        if (!it.opts.discriminator) {\n            throw new Error(\"discriminator: requires discriminator option\");\n        }\n        const tagName = schema.propertyName;\n        if (typeof tagName != \"string\")\n            throw new Error(\"discriminator: requires propertyName\");\n        if (schema.mapping)\n            throw new Error(\"discriminator: mapping is not supported\");\n        if (!oneOf)\n            throw new Error(\"discriminator: requires oneOf keyword\");\n        const valid = gen.let(\"valid\", false);\n        const tag = gen.const(\"tag\", (0, codegen_1._) `${data}${(0, codegen_1.getProperty)(tagName)}`);\n        gen.if((0, codegen_1._) `typeof ${tag} == \"string\"`, () => validateMapping(), () => cxt.error(false, { discrError: types_1.DiscrError.Tag, tag, tagName }));\n        cxt.ok(valid);\n        function validateMapping() {\n            const mapping = getMapping();\n            gen.if(false);\n            for (const tagValue in mapping) {\n                gen.elseIf((0, codegen_1._) `${tag} === ${tagValue}`);\n                gen.assign(valid, applyTagSchema(mapping[tagValue]));\n            }\n            gen.else();\n            cxt.error(false, { discrError: types_1.DiscrError.Mapping, tag, tagName });\n            gen.endIf();\n        }\n        function applyTagSchema(schemaProp) {\n            const _valid = gen.name(\"valid\");\n            const schCxt = cxt.subschema({ keyword: \"oneOf\", schemaProp }, _valid);\n            cxt.mergeEvaluated(schCxt, codegen_1.Name);\n            return _valid;\n        }\n        function getMapping() {\n            var _a;\n            const oneOfMapping = {};\n            const topRequired = hasRequired(parentSchema);\n            let tagRequired = true;\n            for (let i = 0; i < oneOf.length; i++) {\n                let sch = oneOf[i];\n                if ((sch === null || sch === void 0 ? void 0 : sch.$ref) && !(0, util_1.schemaHasRulesButRef)(sch, it.self.RULES)) {\n                    const ref = sch.$ref;\n                    sch = compile_1.resolveRef.call(it.self, it.schemaEnv.root, it.baseId, ref);\n                    if (sch instanceof compile_1.SchemaEnv)\n                        sch = sch.schema;\n                    if (sch === undefined)\n                        throw new ref_error_1.default(it.opts.uriResolver, it.baseId, ref);\n                }\n                const propSch = (_a = sch === null || sch === void 0 ? void 0 : sch.properties) === null || _a === void 0 ? void 0 : _a[tagName];\n                if (typeof propSch != \"object\") {\n                    throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have \"properties/${tagName}\"`);\n                }\n                tagRequired = tagRequired && (topRequired || hasRequired(sch));\n                addMappings(propSch, i);\n            }\n            if (!tagRequired)\n                throw new Error(`discriminator: \"${tagName}\" must be required`);\n            return oneOfMapping;\n            function hasRequired({ required }) {\n                return Array.isArray(required) && required.includes(tagName);\n            }\n            function addMappings(sch, i) {\n                if (sch.const) {\n                    addMapping(sch.const, i);\n                }\n                else if (sch.enum) {\n                    for (const tagValue of sch.enum) {\n                        addMapping(tagValue, i);\n                    }\n                }\n                else {\n                    throw new Error(`discriminator: \"properties/${tagName}\" must have \"const\" or \"enum\"`);\n                }\n            }\n            function addMapping(tagValue, i) {\n                if (typeof tagValue != \"string\" || tagValue in oneOfMapping) {\n                    throw new Error(`discriminator: \"${tagName}\" values must be unique strings`);\n                }\n                oneOfMapping[tagValue] = i;\n            }\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DiscrError = void 0;\nvar DiscrError;\n(function (DiscrError) {\n    DiscrError[\"Tag\"] = \"tag\";\n    DiscrError[\"Mapping\"] = \"mapping\";\n})(DiscrError || (exports.DiscrError = DiscrError = {}));\n//# sourceMappingURL=types.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst core_1 = require(\"./core\");\nconst validation_1 = require(\"./validation\");\nconst applicator_1 = require(\"./applicator\");\nconst format_1 = require(\"./format\");\nconst metadata_1 = require(\"./metadata\");\nconst draft7Vocabularies = [\n    core_1.default,\n    validation_1.default,\n    (0, applicator_1.default)(),\n    format_1.default,\n    metadata_1.metadataVocabulary,\n    metadata_1.contentVocabulary,\n];\nexports.default = draft7Vocabularies;\n//# sourceMappingURL=draft7.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message: ({ schemaCode }) => (0, codegen_1.str) `must match format \"${schemaCode}\"`,\n    params: ({ schemaCode }) => (0, codegen_1._) `{format: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"format\",\n    type: [\"number\", \"string\"],\n    schemaType: \"string\",\n    $data: true,\n    error,\n    code(cxt, ruleType) {\n        const { gen, data, $data, schema, schemaCode, it } = cxt;\n        const { opts, errSchemaPath, schemaEnv, self } = it;\n        if (!opts.validateFormats)\n            return;\n        if ($data)\n            validate$DataFormat();\n        else\n            validateFormat();\n        function validate$DataFormat() {\n            const fmts = gen.scopeValue(\"formats\", {\n                ref: self.formats,\n                code: opts.code.formats,\n            });\n            const fDef = gen.const(\"fDef\", (0, codegen_1._) `${fmts}[${schemaCode}]`);\n            const fType = gen.let(\"fType\");\n            const format = gen.let(\"format\");\n            // TODO simplify\n            gen.if((0, codegen_1._) `typeof ${fDef} == \"object\" && !(${fDef} instanceof RegExp)`, () => gen.assign(fType, (0, codegen_1._) `${fDef}.type || \"string\"`).assign(format, (0, codegen_1._) `${fDef}.validate`), () => gen.assign(fType, (0, codegen_1._) `\"string\"`).assign(format, fDef));\n            cxt.fail$data((0, codegen_1.or)(unknownFmt(), invalidFmt()));\n            function unknownFmt() {\n                if (opts.strictSchema === false)\n                    return codegen_1.nil;\n                return (0, codegen_1._) `${schemaCode} && !${format}`;\n            }\n            function invalidFmt() {\n                const callFormat = schemaEnv.$async\n                    ? (0, codegen_1._) `(${fDef}.async ? await ${format}(${data}) : ${format}(${data}))`\n                    : (0, codegen_1._) `${format}(${data})`;\n                const validData = (0, codegen_1._) `(typeof ${format} == \"function\" ? ${callFormat} : ${format}.test(${data}))`;\n                return (0, codegen_1._) `${format} && ${format} !== true && ${fType} === ${ruleType} && !${validData}`;\n            }\n        }\n        function validateFormat() {\n            const formatDef = self.formats[schema];\n            if (!formatDef) {\n                unknownFormat();\n                return;\n            }\n            if (formatDef === true)\n                return;\n            const [fmtType, format, fmtRef] = getFormat(formatDef);\n            if (fmtType === ruleType)\n                cxt.pass(validCondition());\n            function unknownFormat() {\n                if (opts.strictSchema === false) {\n                    self.logger.warn(unknownMsg());\n                    return;\n                }\n                throw new Error(unknownMsg());\n                function unknownMsg() {\n                    return `unknown format \"${schema}\" ignored in schema at path \"${errSchemaPath}\"`;\n                }\n            }\n            function getFormat(fmtDef) {\n                const code = fmtDef instanceof RegExp\n                    ? (0, codegen_1.regexpCode)(fmtDef)\n                    : opts.code.formats\n                        ? (0, codegen_1._) `${opts.code.formats}${(0, codegen_1.getProperty)(schema)}`\n                        : undefined;\n                const fmt = gen.scopeValue(\"formats\", { key: schema, ref: fmtDef, code });\n                if (typeof fmtDef == \"object\" && !(fmtDef instanceof RegExp)) {\n                    return [fmtDef.type || \"string\", fmtDef.validate, (0, codegen_1._) `${fmt}.validate`];\n                }\n                return [\"string\", fmtDef, fmt];\n            }\n            function validCondition() {\n                if (typeof formatDef == \"object\" && !(formatDef instanceof RegExp) && formatDef.async) {\n                    if (!schemaEnv.$async)\n                        throw new Error(\"async format in sync schema\");\n                    return (0, codegen_1._) `await ${fmtRef}(${data})`;\n                }\n                return typeof format == \"function\" ? (0, codegen_1._) `${fmtRef}(${data})` : (0, codegen_1._) `${fmtRef}.test(${data})`;\n            }\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=format.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst format_1 = require(\"./format\");\nconst format = [format_1.default];\nexports.default = format;\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.contentVocabulary = exports.metadataVocabulary = void 0;\nexports.metadataVocabulary = [\n    \"title\",\n    \"description\",\n    \"default\",\n    \"deprecated\",\n    \"readOnly\",\n    \"writeOnly\",\n    \"examples\",\n];\nexports.contentVocabulary = [\n    \"contentMediaType\",\n    \"contentEncoding\",\n    \"contentSchema\",\n];\n//# sourceMappingURL=metadata.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst equal_1 = require(\"../../runtime/equal\");\nconst error = {\n    message: \"must be equal to constant\",\n    params: ({ schemaCode }) => (0, codegen_1._) `{allowedValue: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"const\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, data, $data, schemaCode, schema } = cxt;\n        if ($data || (schema && typeof schema == \"object\")) {\n            cxt.fail$data((0, codegen_1._) `!${(0, util_1.useFunc)(gen, equal_1.default)}(${data}, ${schemaCode})`);\n        }\n        else {\n            cxt.fail((0, codegen_1._) `${schema} !== ${data}`);\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=const.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst equal_1 = require(\"../../runtime/equal\");\nconst error = {\n    message: \"must be equal to one of the allowed values\",\n    params: ({ schemaCode }) => (0, codegen_1._) `{allowedValues: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"enum\",\n    schemaType: \"array\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, data, $data, schema, schemaCode, it } = cxt;\n        if (!$data && schema.length === 0)\n            throw new Error(\"enum must have non-empty array\");\n        const useLoop = schema.length >= it.opts.loopEnum;\n        let eql;\n        const getEql = () => (eql !== null && eql !== void 0 ? eql : (eql = (0, util_1.useFunc)(gen, equal_1.default)));\n        let valid;\n        if (useLoop || $data) {\n            valid = gen.let(\"valid\");\n            cxt.block$data(valid, loopEnum);\n        }\n        else {\n            /* istanbul ignore if */\n            if (!Array.isArray(schema))\n                throw new Error(\"ajv implementation error\");\n            const vSchema = gen.const(\"vSchema\", schemaCode);\n            valid = (0, codegen_1.or)(...schema.map((_x, i) => equalCode(vSchema, i)));\n        }\n        cxt.pass(valid);\n        function loopEnum() {\n            gen.assign(valid, false);\n            gen.forOf(\"v\", schemaCode, (v) => gen.if((0, codegen_1._) `${getEql()}(${data}, ${v})`, () => gen.assign(valid, true).break()));\n        }\n        function equalCode(vSchema, i) {\n            const sch = schema[i];\n            return typeof sch === \"object\" && sch !== null\n                ? (0, codegen_1._) `${getEql()}(${data}, ${vSchema}[${i}])`\n                : (0, codegen_1._) `${data} === ${sch}`;\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=enum.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst limitNumber_1 = require(\"./limitNumber\");\nconst multipleOf_1 = require(\"./multipleOf\");\nconst limitLength_1 = require(\"./limitLength\");\nconst pattern_1 = require(\"./pattern\");\nconst limitProperties_1 = require(\"./limitProperties\");\nconst required_1 = require(\"./required\");\nconst limitItems_1 = require(\"./limitItems\");\nconst uniqueItems_1 = require(\"./uniqueItems\");\nconst const_1 = require(\"./const\");\nconst enum_1 = require(\"./enum\");\nconst validation = [\n    // number\n    limitNumber_1.default,\n    multipleOf_1.default,\n    // string\n    limitLength_1.default,\n    pattern_1.default,\n    // object\n    limitProperties_1.default,\n    required_1.default,\n    // array\n    limitItems_1.default,\n    uniqueItems_1.default,\n    // any\n    { keyword: \"type\", schemaType: [\"string\", \"array\"] },\n    { keyword: \"nullable\", schemaType: \"boolean\" },\n    const_1.default,\n    enum_1.default,\n];\nexports.default = validation;\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message({ keyword, schemaCode }) {\n        const comp = keyword === \"maxItems\" ? \"more\" : \"fewer\";\n        return (0, codegen_1.str) `must NOT have ${comp} than ${schemaCode} items`;\n    },\n    params: ({ schemaCode }) => (0, codegen_1._) `{limit: ${schemaCode}}`,\n};\nconst def = {\n    keyword: [\"maxItems\", \"minItems\"],\n    type: \"array\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { keyword, data, schemaCode } = cxt;\n        const op = keyword === \"maxItems\" ? codegen_1.operators.GT : codegen_1.operators.LT;\n        cxt.fail$data((0, codegen_1._) `${data}.length ${op} ${schemaCode}`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=limitItems.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst ucs2length_1 = require(\"../../runtime/ucs2length\");\nconst error = {\n    message({ keyword, schemaCode }) {\n        const comp = keyword === \"maxLength\" ? \"more\" : \"fewer\";\n        return (0, codegen_1.str) `must NOT have ${comp} than ${schemaCode} characters`;\n    },\n    params: ({ schemaCode }) => (0, codegen_1._) `{limit: ${schemaCode}}`,\n};\nconst def = {\n    keyword: [\"maxLength\", \"minLength\"],\n    type: \"string\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { keyword, data, schemaCode, it } = cxt;\n        const op = keyword === \"maxLength\" ? codegen_1.operators.GT : codegen_1.operators.LT;\n        const len = it.opts.unicode === false ? (0, codegen_1._) `${data}.length` : (0, codegen_1._) `${(0, util_1.useFunc)(cxt.gen, ucs2length_1.default)}(${data})`;\n        cxt.fail$data((0, codegen_1._) `${len} ${op} ${schemaCode}`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=limitLength.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst ops = codegen_1.operators;\nconst KWDs = {\n    maximum: { okStr: \"<=\", ok: ops.LTE, fail: ops.GT },\n    minimum: { okStr: \">=\", ok: ops.GTE, fail: ops.LT },\n    exclusiveMaximum: { okStr: \"<\", ok: ops.LT, fail: ops.GTE },\n    exclusiveMinimum: { okStr: \">\", ok: ops.GT, fail: ops.LTE },\n};\nconst error = {\n    message: ({ keyword, schemaCode }) => (0, codegen_1.str) `must be ${KWDs[keyword].okStr} ${schemaCode}`,\n    params: ({ keyword, schemaCode }) => (0, codegen_1._) `{comparison: ${KWDs[keyword].okStr}, limit: ${schemaCode}}`,\n};\nconst def = {\n    keyword: Object.keys(KWDs),\n    type: \"number\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { keyword, data, schemaCode } = cxt;\n        cxt.fail$data((0, codegen_1._) `${data} ${KWDs[keyword].fail} ${schemaCode} || isNaN(${data})`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=limitNumber.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message({ keyword, schemaCode }) {\n        const comp = keyword === \"maxProperties\" ? \"more\" : \"fewer\";\n        return (0, codegen_1.str) `must NOT have ${comp} than ${schemaCode} properties`;\n    },\n    params: ({ schemaCode }) => (0, codegen_1._) `{limit: ${schemaCode}}`,\n};\nconst def = {\n    keyword: [\"maxProperties\", \"minProperties\"],\n    type: \"object\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { keyword, data, schemaCode } = cxt;\n        const op = keyword === \"maxProperties\" ? codegen_1.operators.GT : codegen_1.operators.LT;\n        cxt.fail$data((0, codegen_1._) `Object.keys(${data}).length ${op} ${schemaCode}`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=limitProperties.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message: ({ schemaCode }) => (0, codegen_1.str) `must be multiple of ${schemaCode}`,\n    params: ({ schemaCode }) => (0, codegen_1._) `{multipleOf: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"multipleOf\",\n    type: \"number\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, data, schemaCode, it } = cxt;\n        // const bdt = bad$DataType(schemaCode, <string>def.schemaType, $data)\n        const prec = it.opts.multipleOfPrecision;\n        const res = gen.let(\"res\");\n        const invalid = prec\n            ? (0, codegen_1._) `Math.abs(Math.round(${res}) - ${res}) > 1e-${prec}`\n            : (0, codegen_1._) `${res} !== parseInt(${res})`;\n        cxt.fail$data((0, codegen_1._) `(${schemaCode} === 0 || (${res} = ${data}/${schemaCode}, ${invalid}))`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=multipleOf.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message: ({ schemaCode }) => (0, codegen_1.str) `must match pattern \"${schemaCode}\"`,\n    params: ({ schemaCode }) => (0, codegen_1._) `{pattern: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"pattern\",\n    type: \"string\",\n    schemaType: \"string\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { data, $data, schema, schemaCode, it } = cxt;\n        // TODO regexp should be wrapped in try/catchs\n        const u = it.opts.unicodeRegExp ? \"u\" : \"\";\n        const regExp = $data ? (0, codegen_1._) `(new RegExp(${schemaCode}, ${u}))` : (0, code_1.usePattern)(cxt, schema);\n        cxt.fail$data((0, codegen_1._) `!${regExp}.test(${data})`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=pattern.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: ({ params: { missingProperty } }) => (0, codegen_1.str) `must have required property '${missingProperty}'`,\n    params: ({ params: { missingProperty } }) => (0, codegen_1._) `{missingProperty: ${missingProperty}}`,\n};\nconst def = {\n    keyword: \"required\",\n    type: \"object\",\n    schemaType: \"array\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, schema, schemaCode, data, $data, it } = cxt;\n        const { opts } = it;\n        if (!$data && schema.length === 0)\n            return;\n        const useLoop = schema.length >= opts.loopRequired;\n        if (it.allErrors)\n            allErrorsMode();\n        else\n            exitOnErrorMode();\n        if (opts.strictRequired) {\n            const props = cxt.parentSchema.properties;\n            const { definedProperties } = cxt.it;\n            for (const requiredKey of schema) {\n                if ((props === null || props === void 0 ? void 0 : props[requiredKey]) === undefined && !definedProperties.has(requiredKey)) {\n                    const schemaPath = it.schemaEnv.baseId + it.errSchemaPath;\n                    const msg = `required property \"${requiredKey}\" is not defined at \"${schemaPath}\" (strictRequired)`;\n                    (0, util_1.checkStrictMode)(it, msg, it.opts.strictRequired);\n                }\n            }\n        }\n        function allErrorsMode() {\n            if (useLoop || $data) {\n                cxt.block$data(codegen_1.nil, loopAllRequired);\n            }\n            else {\n                for (const prop of schema) {\n                    (0, code_1.checkReportMissingProp)(cxt, prop);\n                }\n            }\n        }\n        function exitOnErrorMode() {\n            const missing = gen.let(\"missing\");\n            if (useLoop || $data) {\n                const valid = gen.let(\"valid\", true);\n                cxt.block$data(valid, () => loopUntilMissing(missing, valid));\n                cxt.ok(valid);\n            }\n            else {\n                gen.if((0, code_1.checkMissingProp)(cxt, schema, missing));\n                (0, code_1.reportMissingProp)(cxt, missing);\n                gen.else();\n            }\n        }\n        function loopAllRequired() {\n            gen.forOf(\"prop\", schemaCode, (prop) => {\n                cxt.setParams({ missingProperty: prop });\n                gen.if((0, code_1.noPropertyInData)(gen, data, prop, opts.ownProperties), () => cxt.error());\n            });\n        }\n        function loopUntilMissing(missing, valid) {\n            cxt.setParams({ missingProperty: missing });\n            gen.forOf(missing, schemaCode, () => {\n                gen.assign(valid, (0, code_1.propertyInData)(gen, data, missing, opts.ownProperties));\n                gen.if((0, codegen_1.not)(valid), () => {\n                    cxt.error();\n                    gen.break();\n                });\n            }, codegen_1.nil);\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=required.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst dataType_1 = require(\"../../compile/validate/dataType\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst equal_1 = require(\"../../runtime/equal\");\nconst error = {\n    message: ({ params: { i, j } }) => (0, codegen_1.str) `must NOT have duplicate items (items ## ${j} and ${i} are identical)`,\n    params: ({ params: { i, j } }) => (0, codegen_1._) `{i: ${i}, j: ${j}}`,\n};\nconst def = {\n    keyword: \"uniqueItems\",\n    type: \"array\",\n    schemaType: \"boolean\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, data, $data, schema, parentSchema, schemaCode, it } = cxt;\n        if (!$data && !schema)\n            return;\n        const valid = gen.let(\"valid\");\n        const itemTypes = parentSchema.items ? (0, dataType_1.getSchemaTypes)(parentSchema.items) : [];\n        cxt.block$data(valid, validateUniqueItems, (0, codegen_1._) `${schemaCode} === false`);\n        cxt.ok(valid);\n        function validateUniqueItems() {\n            const i = gen.let(\"i\", (0, codegen_1._) `${data}.length`);\n            const j = gen.let(\"j\");\n            cxt.setParams({ i, j });\n            gen.assign(valid, true);\n            gen.if((0, codegen_1._) `${i} > 1`, () => (canOptimize() ? loopN : loopN2)(i, j));\n        }\n        function canOptimize() {\n            return itemTypes.length > 0 && !itemTypes.some((t) => t === \"object\" || t === \"array\");\n        }\n        function loopN(i, j) {\n            const item = gen.name(\"item\");\n            const wrongType = (0, dataType_1.checkDataTypes)(itemTypes, item, it.opts.strictNumbers, dataType_1.DataType.Wrong);\n            const indices = gen.const(\"indices\", (0, codegen_1._) `{}`);\n            gen.for((0, codegen_1._) `;${i}--;`, () => {\n                gen.let(item, (0, codegen_1._) `${data}[${i}]`);\n                gen.if(wrongType, (0, codegen_1._) `continue`);\n                if (itemTypes.length > 1)\n                    gen.if((0, codegen_1._) `typeof ${item} == \"string\"`, (0, codegen_1._) `${item} += \"_\"`);\n                gen\n                    .if((0, codegen_1._) `typeof ${indices}[${item}] == \"number\"`, () => {\n                    gen.assign(j, (0, codegen_1._) `${indices}[${item}]`);\n                    cxt.error();\n                    gen.assign(valid, false).break();\n                })\n                    .code((0, codegen_1._) `${indices}[${item}] = ${i}`);\n            });\n        }\n        function loopN2(i, j) {\n            const eql = (0, util_1.useFunc)(gen, equal_1.default);\n            const outer = gen.name(\"outer\");\n            gen.label(outer).for((0, codegen_1._) `;${i}--;`, () => gen.for((0, codegen_1._) `${j} = ${i}; ${j}--;`, () => gen.if((0, codegen_1._) `${eql}(${data}[${i}], ${data}[${j}])`, () => {\n                cxt.error();\n                gen.assign(valid, false).break(outer);\n            })));\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=uniqueItems.js.map", "var MapCache = require('./_MapCache'),\n    setCacheAdd = require('./_setCacheAdd'),\n    setCacheHas = require('./_setCacheHas');\n\n/**\n *\n * Creates an array cache object to store unique values.\n *\n * @private\n * @constructor\n * @param {Array} [values] The values to cache.\n */\nfunction SetCache(values) {\n  var index = -1,\n      length = values == null ? 0 : values.length;\n\n  this.__data__ = new MapCache;\n  while (++index < length) {\n    this.add(values[index]);\n  }\n}\n\n// Add methods to `SetCache`.\nSetCache.prototype.add = SetCache.prototype.push = setCacheAdd;\nSetCache.prototype.has = setCacheHas;\n\nmodule.exports = SetCache;\n", "/**\n * A specialized version of `_.some` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n */\nfunction arraySome(array, predicate) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  while (++index < length) {\n    if (predicate(array[index], index, array)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nmodule.exports = arraySome;\n", "var baseIsEqualDeep = require('./_baseIsEqualDeep'),\n    isObjectLike = require('./isObjectLike');\n\n/**\n * The base implementation of `_.isEqual` which supports partial comparisons\n * and tracks traversed objects.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @param {boolean} bitmask The bitmask flags.\n *  1 - Unordered comparison\n *  2 - Partial comparison\n * @param {Function} [customizer] The function to customize comparisons.\n * @param {Object} [stack] Tracks traversed `value` and `other` objects.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n */\nfunction baseIsEqual(value, other, bitmask, customizer, stack) {\n  if (value === other) {\n    return true;\n  }\n  if (value == null || other == null || (!isObjectLike(value) && !isObjectLike(other))) {\n    return value !== value && other !== other;\n  }\n  return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);\n}\n\nmodule.exports = baseIsEqual;\n", "var Stack = require('./_Stack'),\n    equalArrays = require('./_equalArrays'),\n    equalByTag = require('./_equalByTag'),\n    equalObjects = require('./_equalObjects'),\n    getTag = require('./_getTag'),\n    isArray = require('./isArray'),\n    isBuffer = require('./isBuffer'),\n    isTypedArray = require('./isTypedArray');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    arrayTag = '[object Array]',\n    objectTag = '[object Object]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * A specialized version of `baseIsEqual` for arrays and objects which performs\n * deep comparisons and tracks traversed objects enabling objects with circular\n * references to be compared.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} [stack] Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction baseIsEqualDeep(object, other, bitmask, customizer, equalFunc, stack) {\n  var objIsArr = isArray(object),\n      othIsArr = isArray(other),\n      objTag = objIsArr ? arrayTag : getTag(object),\n      othTag = othIsArr ? arrayTag : getTag(other);\n\n  objTag = objTag == argsTag ? objectTag : objTag;\n  othTag = othTag == argsTag ? objectTag : othTag;\n\n  var objIsObj = objTag == objectTag,\n      othIsObj = othTag == objectTag,\n      isSameTag = objTag == othTag;\n\n  if (isSameTag && isBuffer(object)) {\n    if (!isBuffer(other)) {\n      return false;\n    }\n    objIsArr = true;\n    objIsObj = false;\n  }\n  if (isSameTag && !objIsObj) {\n    stack || (stack = new Stack);\n    return (objIsArr || isTypedArray(object))\n      ? equalArrays(object, other, bitmask, customizer, equalFunc, stack)\n      : equalByTag(object, other, objTag, bitmask, customizer, equalFunc, stack);\n  }\n  if (!(bitmask & COMPARE_PARTIAL_FLAG)) {\n    var objIsWrapped = objIsObj && hasOwnProperty.call(object, '__wrapped__'),\n        othIsWrapped = othIsObj && hasOwnProperty.call(other, '__wrapped__');\n\n    if (objIsWrapped || othIsWrapped) {\n      var objUnwrapped = objIsWrapped ? object.value() : object,\n          othUnwrapped = othIsWrapped ? other.value() : other;\n\n      stack || (stack = new Stack);\n      return equalFunc(objUnwrapped, othUnwrapped, bitmask, customizer, stack);\n    }\n  }\n  if (!isSameTag) {\n    return false;\n  }\n  stack || (stack = new Stack);\n  return equalObjects(object, other, bitmask, customizer, equalFunc, stack);\n}\n\nmodule.exports = baseIsEqualDeep;\n", "/**\n * Checks if a `cache` value for `key` exists.\n *\n * @private\n * @param {Object} cache The cache to query.\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction cacheHas(cache, key) {\n  return cache.has(key);\n}\n\nmodule.exports = cacheHas;\n", "var SetCache = require('./_SetCache'),\n    arraySome = require('./_arraySome'),\n    cacheHas = require('./_cacheHas');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n\n/**\n * A specialized version of `baseIsEqualDeep` for arrays with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Array} array The array to compare.\n * @param {Array} other The other array to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `array` and `other` objects.\n * @returns {boolean} Returns `true` if the arrays are equivalent, else `false`.\n */\nfunction equalArrays(array, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\n      arrLength = array.length,\n      othLength = other.length;\n\n  if (arrLength != othLength && !(isPartial && othLength > arrLength)) {\n    return false;\n  }\n  // Check that cyclic values are equal.\n  var arrStacked = stack.get(array);\n  var othStacked = stack.get(other);\n  if (arrStacked && othStacked) {\n    return arrStacked == other && othStacked == array;\n  }\n  var index = -1,\n      result = true,\n      seen = (bitmask & COMPARE_UNORDERED_FLAG) ? new SetCache : undefined;\n\n  stack.set(array, other);\n  stack.set(other, array);\n\n  // Ignore non-index properties.\n  while (++index < arrLength) {\n    var arrValue = array[index],\n        othValue = other[index];\n\n    if (customizer) {\n      var compared = isPartial\n        ? customizer(othValue, arrValue, index, other, array, stack)\n        : customizer(arrValue, othValue, index, array, other, stack);\n    }\n    if (compared !== undefined) {\n      if (compared) {\n        continue;\n      }\n      result = false;\n      break;\n    }\n    // Recursively compare arrays (susceptible to call stack limits).\n    if (seen) {\n      if (!arraySome(other, function(othValue, othIndex) {\n            if (!cacheHas(seen, othIndex) &&\n                (arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n              return seen.push(othIndex);\n            }\n          })) {\n        result = false;\n        break;\n      }\n    } else if (!(\n          arrValue === othValue ||\n            equalFunc(arrValue, othValue, bitmask, customizer, stack)\n        )) {\n      result = false;\n      break;\n    }\n  }\n  stack['delete'](array);\n  stack['delete'](other);\n  return result;\n}\n\nmodule.exports = equalArrays;\n", "var Symbol = require('./_Symbol'),\n    Uint8Array = require('./_Uint8Array'),\n    eq = require('./eq'),\n    equalArrays = require('./_equalArrays'),\n    mapToArray = require('./_mapToArray'),\n    setToArray = require('./_setToArray');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n\n/** `Object#toString` result references. */\nvar boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    errorTag = '[object Error]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    symbolTag = '[object Symbol]';\n\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]';\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n    symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n\n/**\n * A specialized version of `baseIsEqualDeep` for comparing objects of\n * the same `toStringTag`.\n *\n * **Note:** This function only supports comparing values with tags of\n * `Boolean`, `Date`, `Error`, `Number`, `RegExp`, or `String`.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {string} tag The `toStringTag` of the objects to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction equalByTag(object, other, tag, bitmask, customizer, equalFunc, stack) {\n  switch (tag) {\n    case dataViewTag:\n      if ((object.byteLength != other.byteLength) ||\n          (object.byteOffset != other.byteOffset)) {\n        return false;\n      }\n      object = object.buffer;\n      other = other.buffer;\n\n    case arrayBufferTag:\n      if ((object.byteLength != other.byteLength) ||\n          !equalFunc(new Uint8Array(object), new Uint8Array(other))) {\n        return false;\n      }\n      return true;\n\n    case boolTag:\n    case dateTag:\n    case numberTag:\n      // Coerce booleans to `1` or `0` and dates to milliseconds.\n      // Invalid dates are coerced to `NaN`.\n      return eq(+object, +other);\n\n    case errorTag:\n      return object.name == other.name && object.message == other.message;\n\n    case regexpTag:\n    case stringTag:\n      // Coerce regexes to strings and treat strings, primitives and objects,\n      // as equal. See http://www.ecma-international.org/ecma-262/7.0/#sec-regexp.prototype.tostring\n      // for more details.\n      return object == (other + '');\n\n    case mapTag:\n      var convert = mapToArray;\n\n    case setTag:\n      var isPartial = bitmask & COMPARE_PARTIAL_FLAG;\n      convert || (convert = setToArray);\n\n      if (object.size != other.size && !isPartial) {\n        return false;\n      }\n      // Assume cyclic values are equal.\n      var stacked = stack.get(object);\n      if (stacked) {\n        return stacked == other;\n      }\n      bitmask |= COMPARE_UNORDERED_FLAG;\n\n      // Recursively compare objects (susceptible to call stack limits).\n      stack.set(object, other);\n      var result = equalArrays(convert(object), convert(other), bitmask, customizer, equalFunc, stack);\n      stack['delete'](object);\n      return result;\n\n    case symbolTag:\n      if (symbolValueOf) {\n        return symbolValueOf.call(object) == symbolValueOf.call(other);\n      }\n  }\n  return false;\n}\n\nmodule.exports = equalByTag;\n", "var getAllKeys = require('./_getAllKeys');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1;\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * A specialized version of `baseIsEqualDeep` for objects with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction equalObjects(object, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\n      objProps = getAllKeys(object),\n      objLength = objProps.length,\n      othProps = getAllKeys(other),\n      othLength = othProps.length;\n\n  if (objLength != othLength && !isPartial) {\n    return false;\n  }\n  var index = objLength;\n  while (index--) {\n    var key = objProps[index];\n    if (!(isPartial ? key in other : hasOwnProperty.call(other, key))) {\n      return false;\n    }\n  }\n  // Check that cyclic values are equal.\n  var objStacked = stack.get(object);\n  var othStacked = stack.get(other);\n  if (objStacked && othStacked) {\n    return objStacked == other && othStacked == object;\n  }\n  var result = true;\n  stack.set(object, other);\n  stack.set(other, object);\n\n  var skipCtor = isPartial;\n  while (++index < objLength) {\n    key = objProps[index];\n    var objValue = object[key],\n        othValue = other[key];\n\n    if (customizer) {\n      var compared = isPartial\n        ? customizer(othValue, objValue, key, other, object, stack)\n        : customizer(objValue, othValue, key, object, other, stack);\n    }\n    // Recursively compare objects (susceptible to call stack limits).\n    if (!(compared === undefined\n          ? (objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack))\n          : compared\n        )) {\n      result = false;\n      break;\n    }\n    skipCtor || (skipCtor = key == 'constructor');\n  }\n  if (result && !skipCtor) {\n    var objCtor = object.constructor,\n        othCtor = other.constructor;\n\n    // Non `Object` object instances with different constructors are not equal.\n    if (objCtor != othCtor &&\n        ('constructor' in object && 'constructor' in other) &&\n        !(typeof objCtor == 'function' && objCtor instanceof objCtor &&\n          typeof othCtor == 'function' && othCtor instanceof othCtor)) {\n      result = false;\n    }\n  }\n  stack['delete'](object);\n  stack['delete'](other);\n  return result;\n}\n\nmodule.exports = equalObjects;\n", "/**\n * Converts `map` to its key-value pairs.\n *\n * @private\n * @param {Object} map The map to convert.\n * @returns {Array} Returns the key-value pairs.\n */\nfunction mapToArray(map) {\n  var index = -1,\n      result = Array(map.size);\n\n  map.forEach(function(value, key) {\n    result[++index] = [key, value];\n  });\n  return result;\n}\n\nmodule.exports = mapToArray;\n", "/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/**\n * Adds `value` to the array cache.\n *\n * @private\n * @name add\n * @memberOf SetCache\n * @alias push\n * @param {*} value The value to cache.\n * @returns {Object} Returns the cache instance.\n */\nfunction setCacheAdd(value) {\n  this.__data__.set(value, HASH_UNDEFINED);\n  return this;\n}\n\nmodule.exports = setCacheAdd;\n", "/**\n * Checks if `value` is in the array cache.\n *\n * @private\n * @name has\n * @memberOf SetCache\n * @param {*} value The value to search for.\n * @returns {number} Returns `true` if `value` is found, else `false`.\n */\nfunction setCacheHas(value) {\n  return this.__data__.has(value);\n}\n\nmodule.exports = setCacheHas;\n", "/**\n * Converts `set` to an array of its values.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the values.\n */\nfunction setToArray(set) {\n  var index = -1,\n      result = Array(set.size);\n\n  set.forEach(function(value) {\n    result[++index] = value;\n  });\n  return result;\n}\n\nmodule.exports = setToArray;\n", "var baseIsEqual = require('./_baseIsEqual');\n\n/**\n * Performs a deep comparison between two values to determine if they are\n * equivalent.\n *\n * **Note:** This method supports comparing arrays, array buffers, booleans,\n * date objects, error objects, maps, numbers, `Object` objects, regexes,\n * sets, strings, symbols, and typed arrays. `Object` objects are compared\n * by their own, not inherited, enumerable properties. Functions and DOM\n * nodes are compared by strict equality, i.e. `===`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.isEqual(object, other);\n * // => true\n *\n * object === other;\n * // => false\n */\nfunction isEqual(value, other) {\n  return baseIsEqual(value, other);\n}\n\nmodule.exports = isEqual;\n"], "names": [], "sourceRoot": ""}