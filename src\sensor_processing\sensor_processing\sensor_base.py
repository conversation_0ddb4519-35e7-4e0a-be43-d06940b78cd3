"""
Base class for sensor processors with common functionality
Demonstrates object-oriented design and real-time processing patterns
"""

import threading
import time
import queue
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Callable
import numpy as np
import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy


class SensorProcessor(Node, ABC):
    """
    Abstract base class for all sensor processors
    Provides common functionality for real-time sensor data processing
    """
    
    def __init__(self, node_name: str, config: Dict[str, Any]):
        super().__init__(node_name)
        
        # Configuration
        self.config = config
        self.processing_rate = config.get('processing_rate', 30.0)  # Hz
        self.buffer_size = config.get('buffer_size', 100)
        self.enable_threading = config.get('enable_threading', True)
        
        # Threading components for real-time processing
        self.data_queue = queue.Queue(maxsize=self.buffer_size)
        self.processing_thread = None
        self.is_running = False
        
        # Performance monitoring
        self.processing_times = []
        self.frame_count = 0
        self.start_time = time.time()
        
        # QoS profile for real-time applications
        self.qos_profile = QoSProfile(
            reliability=ReliabilityPolicy.BEST_EFFORT,
            history=HistoryPolicy.KEEP_LAST,
            depth=10
        )
        
        # Initialize sensor-specific components
        self.initialize_sensor()
        
        # Start processing thread if enabled
        if self.enable_threading:
            self.start_processing_thread()
    
    @abstractmethod
    def initialize_sensor(self):
        """Initialize sensor-specific components"""
        pass
    
    @abstractmethod
    def process_data(self, data: Any) -> Any:
        """Process sensor data - must be implemented by subclasses"""
        pass
    
    def start_processing_thread(self):
        """Start the processing thread for real-time operation"""
        if self.processing_thread is None or not self.processing_thread.is_alive():
            self.is_running = True
            self.processing_thread = threading.Thread(
                target=self._processing_loop,
                daemon=True
            )
            self.processing_thread.start()
            self.get_logger().info(f"Started processing thread for {self.get_name()}")
    
    def stop_processing_thread(self):
        """Stop the processing thread"""
        self.is_running = False
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=1.0)
            self.get_logger().info(f"Stopped processing thread for {self.get_name()}")
    
    def _processing_loop(self):
        """Main processing loop running in separate thread"""
        rate = 1.0 / self.processing_rate
        
        while self.is_running:
            try:
                # Get data from queue with timeout
                data = self.data_queue.get(timeout=0.1)
                
                # Process data with timing
                start_time = time.perf_counter()
                processed_data = self.process_data(data)
                processing_time = time.perf_counter() - start_time
                
                # Update performance metrics
                self._update_performance_metrics(processing_time)
                
                # Publish processed data
                if processed_data is not None:
                    self.publish_processed_data(processed_data)
                
                # Mark task as done
                self.data_queue.task_done()
                
                # Rate limiting
                time.sleep(max(0, rate - processing_time))
                
            except queue.Empty:
                continue
            except Exception as e:
                self.get_logger().error(f"Error in processing loop: {e}")
    
    def add_data_to_queue(self, data: Any) -> bool:
        """Add data to processing queue"""
        try:
            if self.enable_threading:
                self.data_queue.put_nowait(data)
                return True
            else:
                # Process immediately if threading is disabled
                processed_data = self.process_data(data)
                if processed_data is not None:
                    self.publish_processed_data(processed_data)
                return True
        except queue.Full:
            self.get_logger().warn("Processing queue is full, dropping data")
            return False
        except Exception as e:
            self.get_logger().error(f"Error adding data to queue: {e}")
            return False
    
    @abstractmethod
    def publish_processed_data(self, data: Any):
        """Publish processed data - must be implemented by subclasses"""
        pass
    
    def _update_performance_metrics(self, processing_time: float):
        """Update performance monitoring metrics"""
        self.processing_times.append(processing_time)
        self.frame_count += 1
        
        # Keep only recent measurements
        if len(self.processing_times) > 1000:
            self.processing_times = self.processing_times[-1000:]
        
        # Log performance every 100 frames
        if self.frame_count % 100 == 0:
            self._log_performance_stats()
    
    def _log_performance_stats(self):
        """Log current performance statistics"""
        if not self.processing_times:
            return
        
        avg_time = np.mean(self.processing_times[-100:])
        max_time = np.max(self.processing_times[-100:])
        fps = 1.0 / avg_time if avg_time > 0 else 0
        
        elapsed_time = time.time() - self.start_time
        overall_fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0
        
        self.get_logger().info(
            f"Performance Stats - "
            f"Avg: {avg_time*1000:.2f}ms, "
            f"Max: {max_time*1000:.2f}ms, "
            f"FPS: {fps:.1f}, "
            f"Overall FPS: {overall_fps:.1f}"
        )
    
    def get_performance_stats(self) -> Dict[str, float]:
        """Get current performance statistics"""
        if not self.processing_times:
            return {}
        
        recent_times = self.processing_times[-100:]
        return {
            'avg_processing_time_ms': np.mean(recent_times) * 1000,
            'max_processing_time_ms': np.max(recent_times) * 1000,
            'min_processing_time_ms': np.min(recent_times) * 1000,
            'std_processing_time_ms': np.std(recent_times) * 1000,
            'current_fps': 1.0 / np.mean(recent_times) if recent_times else 0,
            'total_frames': self.frame_count,
            'queue_size': self.data_queue.qsize() if self.enable_threading else 0
        }
    
    def destroy_node(self):
        """Clean shutdown of the node"""
        self.stop_processing_thread()
        super().destroy_node()


class MultiThreadedProcessor:
    """
    Utility class for managing multiple processing threads
    Demonstrates advanced multithreading patterns for real-time systems
    """
    
    def __init__(self, num_threads: int = 4):
        self.num_threads = num_threads
        self.threads = []
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.is_running = False
    
    def start(self):
        """Start all worker threads"""
        self.is_running = True
        for i in range(self.num_threads):
            thread = threading.Thread(
                target=self._worker_loop,
                args=(i,),
                daemon=True
            )
            thread.start()
            self.threads.append(thread)
    
    def stop(self):
        """Stop all worker threads"""
        self.is_running = False
        
        # Add sentinel values to wake up threads
        for _ in range(self.num_threads):
            self.task_queue.put(None)
        
        # Wait for threads to finish
        for thread in self.threads:
            thread.join(timeout=1.0)
        
        self.threads.clear()
    
    def _worker_loop(self, worker_id: int):
        """Worker thread main loop"""
        while self.is_running:
            try:
                task = self.task_queue.get(timeout=0.1)
                if task is None:  # Sentinel value
                    break
                
                # Process task
                func, args, kwargs = task
                result = func(*args, **kwargs)
                
                # Put result in result queue
                self.result_queue.put((worker_id, result))
                
                self.task_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Worker {worker_id} error: {e}")
    
    def submit_task(self, func: Callable, *args, **kwargs) -> bool:
        """Submit a task for processing"""
        try:
            self.task_queue.put_nowait((func, args, kwargs))
            return True
        except queue.Full:
            return False
    
    def get_result(self, timeout: float = 0.1) -> Optional[tuple]:
        """Get a result from the result queue"""
        try:
            return self.result_queue.get(timeout=timeout)
        except queue.Empty:
            return None
