"""
Camera Processing Module
Demonstrates computer vision, object detection, and feature extraction
"""

import numpy as np
import cv2
from typing import List, Tuple, Dict, Any, Optional
import time
from dataclasses import dataclass
import rclpy
from sensor_msgs.msg import Image, CompressedImage
from geometry_msgs.msg import Point, Vector3
from visualization_msgs.msg import Marker, <PERSON>er<PERSON><PERSON>y
from std_msgs.msg import Header
from cv_bridge import CvBridge

from .sensor_base import SensorProcessor
from .math_utils import MathUtils


@dataclass
class DetectedFeature:
    """Represents a detected visual feature"""
    keypoint: cv2.KeyPoint
    descriptor: np.ndarray
    feature_type: str  # 'SIFT', 'ORB', 'SURF', etc.


@dataclass
class CameraObject:
    """Represents an object detected in camera image"""
    bbox: Tuple[int, int, int, int]  # x, y, width, height
    class_name: str
    confidence: float
    center_3d: Optional[Tuple[float, float, float]] = None
    distance: Optional[float] = None


class CameraProcessor(SensorProcessor):
    """
    Advanced camera processing with object detection and feature extraction
    Includes YOLO detection, feature matching, and stereo vision
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__('camera_processor', config)
        
        # Camera parameters
        self.image_width = config.get('image_width', 1920)
        self.image_height = config.get('image_height', 1080)
        self.fx = config.get('fx', 1000.0)  # Focal length x
        self.fy = config.get('fy', 1000.0)  # Focal length y
        self.cx = config.get('cx', self.image_width / 2)  # Principal point x
        self.cy = config.get('cy', self.image_height / 2)  # Principal point y
        
        # Camera matrix
        self.camera_matrix = np.array([
            [self.fx, 0, self.cx],
            [0, self.fy, self.cy],
            [0, 0, 1]
        ])
        
        # Distortion coefficients (k1, k2, p1, p2, k3)
        self.dist_coeffs = np.array(config.get('distortion', [0.0, 0.0, 0.0, 0.0, 0.0]))
        
        # Processing parameters
        self.enable_object_detection = config.get('enable_object_detection', True)
        self.enable_feature_extraction = config.get('enable_feature_extraction', True)
        self.enable_stereo = config.get('enable_stereo', False)
        
        # Feature extraction parameters
        self.feature_type = config.get('feature_type', 'ORB')  # 'SIFT', 'ORB', 'SURF'
        self.max_features = config.get('max_features', 1000)
        
        # Object detection parameters
        self.detection_threshold = config.get('detection_threshold', 0.5)
        self.nms_threshold = config.get('nms_threshold', 0.4)
        
        # Initialize CV bridge
        self.bridge = CvBridge()
        
        # Initialize feature detector
        self.feature_detector = self.create_feature_detector()
        
        # Initialize object detector (simplified YOLO-like)
        self.object_classes = [
            'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
            'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench'
        ]
        
        # Previous frame for tracking
        self.previous_frame = None
        self.previous_features = []
        
    def initialize_sensor(self):
        """Initialize camera-specific components"""
        # Publishers
        self.processed_image_pub = self.create_publisher(
            Image,
            'processed_image',
            self.qos_profile
        )
        
        self.objects_pub = self.create_publisher(
            MarkerArray,
            'camera_objects',
            self.qos_profile
        )
        
        self.features_pub = self.create_publisher(
            MarkerArray,
            'visual_features',
            self.qos_profile
        )
        
        # Subscribers
        self.image_sub = self.create_subscription(
            Image,
            'camera/image_raw',
            self.image_callback,
            self.qos_profile
        )
        
        # Stereo subscriber (if enabled)
        if self.enable_stereo:
            self.stereo_sub = self.create_subscription(
                Image,
                'camera/right/image_raw',
                self.stereo_callback,
                self.qos_profile
            )
        
        self.get_logger().info("Camera processor initialized")
    
    def image_callback(self, msg: Image):
        """Handle incoming camera images"""
        self.add_data_to_queue(msg)
    
    def stereo_callback(self, msg: Image):
        """Handle stereo camera images"""
        # Store for stereo processing
        pass
    
    def process_data(self, image_msg: Image) -> Optional[Dict[str, Any]]:
        """
        Main camera processing pipeline
        Returns processed data with detected objects and features
        """
        try:
            # Convert ROS image to OpenCV
            cv_image = self.bridge.imgmsg_to_cv2(image_msg, "bgr8")
            
            # Step 1: Image preprocessing
            processed_image = self.preprocess_image(cv_image)
            
            # Step 2: Object detection
            detected_objects = []
            if self.enable_object_detection:
                detected_objects = self.detect_objects(processed_image)
            
            # Step 3: Feature extraction
            features = []
            if self.enable_feature_extraction:
                features = self.extract_features(processed_image)
            
            # Step 4: Feature tracking
            tracked_features = self.track_features(processed_image, features)
            
            # Step 5: Depth estimation (if stereo available)
            depth_map = None
            if self.enable_stereo:
                depth_map = self.estimate_depth(processed_image)
            
            # Step 6: 3D object localization
            objects_3d = self.localize_objects_3d(detected_objects, depth_map)
            
            # Step 7: Create visualization
            visualization = self.create_visualization(
                processed_image, detected_objects, features
            )
            
            # Update previous frame data
            self.previous_frame = processed_image.copy()
            self.previous_features = features
            
            return {
                'timestamp': time.time(),
                'original_image': cv_image,
                'processed_image': processed_image,
                'visualization': visualization,
                'detected_objects': detected_objects,
                'objects_3d': objects_3d,
                'features': features,
                'tracked_features': tracked_features,
                'depth_map': depth_map,
                'header': image_msg.header
            }
            
        except Exception as e:
            self.get_logger().error(f"Error processing camera data: {e}")
            return None
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """Preprocess image for better detection and feature extraction"""
        # Undistort image
        undistorted = cv2.undistort(image, self.camera_matrix, self.dist_coeffs)
        
        # Enhance contrast
        lab = cv2.cvtColor(undistorted, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        l = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8)).apply(l)
        enhanced = cv2.merge([l, a, b])
        enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        # Noise reduction
        denoised = cv2.bilateralFilter(enhanced, 9, 75, 75)
        
        return denoised
    
    def create_feature_detector(self):
        """Create feature detector based on configuration"""
        if self.feature_type == 'SIFT':
            return cv2.SIFT_create(nfeatures=self.max_features)
        elif self.feature_type == 'ORB':
            return cv2.ORB_create(nfeatures=self.max_features)
        elif self.feature_type == 'SURF':
            return cv2.xfeatures2d.SURF_create(hessianThreshold=400)
        else:
            # Default to ORB
            return cv2.ORB_create(nfeatures=self.max_features)
    
    def detect_objects(self, image: np.ndarray) -> List[CameraObject]:
        """
        Detect objects in image using simplified YOLO-like detection
        In practice, you would use a pre-trained YOLO, SSD, or other detector
        """
        detected_objects = []
        
        # For demonstration, use simple contour detection
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Edge detection
        edges = cv2.Canny(gray, 50, 150)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            # Filter by area
            area = cv2.contourArea(contour)
            if area < 1000 or area > 50000:
                continue
            
            # Get bounding box
            x, y, w, h = cv2.boundingRect(contour)
            
            # Simple aspect ratio filtering
            aspect_ratio = w / h
            if aspect_ratio < 0.2 or aspect_ratio > 5.0:
                continue
            
            # Classify based on simple heuristics
            class_name = self.classify_object_simple(w, h, aspect_ratio)
            confidence = min(1.0, area / 10000.0)  # Simple confidence based on size
            
            detected_object = CameraObject(
                bbox=(x, y, w, h),
                class_name=class_name,
                confidence=confidence
            )
            
            detected_objects.append(detected_object)
        
        return detected_objects
    
    def classify_object_simple(self, width: int, height: int, aspect_ratio: float) -> str:
        """Simple object classification based on bounding box properties"""
        if aspect_ratio > 2.0:
            return 'vehicle'
        elif aspect_ratio < 0.7 and height > width:
            return 'person'
        elif width > 100 and height > 100:
            return 'large_object'
        else:
            return 'unknown'
    
    def extract_features(self, image: np.ndarray) -> List[DetectedFeature]:
        """Extract visual features from image"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Detect keypoints and compute descriptors
        keypoints, descriptors = self.feature_detector.detectAndCompute(gray, None)
        
        features = []
        if descriptors is not None:
            for i, (kp, desc) in enumerate(zip(keypoints, descriptors)):
                feature = DetectedFeature(
                    keypoint=kp,
                    descriptor=desc,
                    feature_type=self.feature_type
                )
                features.append(feature)
        
        return features
    
    def track_features(self, current_image: np.ndarray, 
                      current_features: List[DetectedFeature]) -> List[Tuple[DetectedFeature, DetectedFeature]]:
        """Track features between consecutive frames"""
        tracked_features = []
        
        if self.previous_frame is None or not self.previous_features:
            return tracked_features
        
        # Convert to format suitable for matching
        if not current_features or not self.previous_features:
            return tracked_features
        
        # Create matcher
        if self.feature_type == 'ORB':
            matcher = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
        else:
            matcher = cv2.BFMatcher(cv2.NORM_L2, crossCheck=True)
        
        # Extract descriptors
        prev_descriptors = np.array([f.descriptor for f in self.previous_features])
        curr_descriptors = np.array([f.descriptor for f in current_features])
        
        # Match features
        matches = matcher.match(prev_descriptors, curr_descriptors)
        
        # Filter good matches
        matches = sorted(matches, key=lambda x: x.distance)
        good_matches = matches[:min(50, len(matches))]  # Keep top 50 matches
        
        for match in good_matches:
            prev_feature = self.previous_features[match.queryIdx]
            curr_feature = current_features[match.trainIdx]
            tracked_features.append((prev_feature, curr_feature))
        
        return tracked_features
    
    def estimate_depth(self, image: np.ndarray) -> Optional[np.ndarray]:
        """Estimate depth using stereo vision (simplified)"""
        # This would require stereo camera setup
        # For demonstration, return None
        return None
    
    def localize_objects_3d(self, objects: List[CameraObject], 
                           depth_map: Optional[np.ndarray]) -> List[CameraObject]:
        """Estimate 3D positions of detected objects"""
        objects_3d = []
        
        for obj in objects:
            x, y, w, h = obj.bbox
            center_x = x + w // 2
            center_y = y + h // 2
            
            # Estimate distance using simple heuristics (in absence of depth map)
            if depth_map is not None:
                # Use depth map if available
                distance = depth_map[center_y, center_x] if depth_map.shape[0] > center_y and depth_map.shape[1] > center_x else None
            else:
                # Simple distance estimation based on object size
                # This is very approximate and would need calibration
                if obj.class_name == 'person':
                    # Assume average person height of 1.7m
                    distance = (1.7 * self.fy) / h if h > 0 else None
                elif obj.class_name == 'vehicle':
                    # Assume average vehicle height of 1.5m
                    distance = (1.5 * self.fy) / h if h > 0 else None
                else:
                    distance = None
            
            # Convert to 3D coordinates
            if distance is not None:
                # Convert pixel coordinates to 3D
                x_3d = (center_x - self.cx) * distance / self.fx
                y_3d = (center_y - self.cy) * distance / self.fy
                z_3d = distance
                
                obj_3d = CameraObject(
                    bbox=obj.bbox,
                    class_name=obj.class_name,
                    confidence=obj.confidence,
                    center_3d=(x_3d, y_3d, z_3d),
                    distance=distance
                )
                objects_3d.append(obj_3d)
            else:
                objects_3d.append(obj)
        
        return objects_3d
    
    def create_visualization(self, image: np.ndarray, objects: List[CameraObject], 
                           features: List[DetectedFeature]) -> np.ndarray:
        """Create visualization with detected objects and features"""
        vis_image = image.copy()
        
        # Draw detected objects
        for obj in objects:
            x, y, w, h = obj.bbox
            
            # Choose color based on class
            color = self.get_class_color(obj.class_name)
            
            # Draw bounding box
            cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, 2)
            
            # Draw label
            label = f"{obj.class_name}: {obj.confidence:.2f}"
            if obj.distance is not None:
                label += f" ({obj.distance:.1f}m)"
            
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(vis_image, (x, y - label_size[1] - 10), 
                         (x + label_size[0], y), color, -1)
            cv2.putText(vis_image, label, (x, y - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        
        # Draw features
        for feature in features[:100]:  # Limit to 100 features for visualization
            kp = feature.keypoint
            cv2.circle(vis_image, (int(kp.pt[0]), int(kp.pt[1])), 3, (0, 255, 0), 1)
        
        return vis_image
    
    def get_class_color(self, class_name: str) -> Tuple[int, int, int]:
        """Get color for object class"""
        colors = {
            'person': (0, 255, 0),      # Green
            'vehicle': (255, 0, 0),     # Blue
            'car': (255, 0, 0),         # Blue
            'truck': (255, 0, 255),     # Magenta
            'large_object': (0, 255, 255),  # Yellow
            'unknown': (128, 128, 128)   # Gray
        }
        return colors.get(class_name, (128, 128, 128))
    
    def publish_processed_data(self, data: Dict[str, Any]):
        """Publish processed camera data"""
        try:
            # Publish visualization image
            if 'visualization' in data:
                vis_msg = self.bridge.cv2_to_imgmsg(data['visualization'], "bgr8")
                vis_msg.header = data['header']
                self.processed_image_pub.publish(vis_msg)
            
            # Publish detected objects as markers
            if 'objects_3d' in data:
                marker_array = self.objects_to_markers(data['objects_3d'], data['header'])
                self.objects_pub.publish(marker_array)
            
            # Publish features as markers
            if 'features' in data:
                feature_markers = self.features_to_markers(data['features'], data['header'])
                self.features_pub.publish(feature_markers)
                
        except Exception as e:
            self.get_logger().error(f"Error publishing camera data: {e}")
    
    def objects_to_markers(self, objects: List[CameraObject], header: Header) -> MarkerArray:
        """Convert camera objects to visualization markers"""
        marker_array = MarkerArray()
        
        for i, obj in enumerate(objects):
            if obj.center_3d is None:
                continue
            
            marker = Marker()
            marker.header = header
            marker.ns = "camera_objects"
            marker.id = i
            marker.type = Marker.CUBE
            marker.action = Marker.ADD
            
            # Position
            marker.pose.position.x = obj.center_3d[0]
            marker.pose.position.y = obj.center_3d[1]
            marker.pose.position.z = obj.center_3d[2]
            
            # Scale based on object type
            if obj.class_name == 'person':
                marker.scale.x = 0.5
                marker.scale.y = 0.5
                marker.scale.z = 1.7
            elif obj.class_name in ['vehicle', 'car']:
                marker.scale.x = 4.0
                marker.scale.y = 2.0
                marker.scale.z = 1.5
            else:
                marker.scale.x = 1.0
                marker.scale.y = 1.0
                marker.scale.z = 1.0
            
            # Color
            color = self.get_class_color(obj.class_name)
            marker.color.r = color[2] / 255.0  # OpenCV uses BGR
            marker.color.g = color[1] / 255.0
            marker.color.b = color[0] / 255.0
            marker.color.a = 0.7
            
            marker_array.markers.append(marker)
        
        return marker_array
    
    def features_to_markers(self, features: List[DetectedFeature], header: Header) -> MarkerArray:
        """Convert visual features to visualization markers"""
        marker_array = MarkerArray()
        
        # Create a single marker for all features
        marker = Marker()
        marker.header = header
        marker.ns = "visual_features"
        marker.id = 0
        marker.type = Marker.POINTS
        marker.action = Marker.ADD
        
        marker.scale.x = 0.05
        marker.scale.y = 0.05
        marker.color.r = 0.0
        marker.color.g = 1.0
        marker.color.b = 0.0
        marker.color.a = 1.0
        
        # Add feature points (project to 3D assuming z=1)
        for feature in features[:200]:  # Limit number of features
            point = Point()
            kp = feature.keypoint
            
            # Project to 3D (assuming z=1 for visualization)
            point.x = (kp.pt[0] - self.cx) / self.fx
            point.y = (kp.pt[1] - self.cy) / self.fy
            point.z = 1.0
            
            marker.points.append(point)
        
        marker_array.markers.append(marker)
        return marker_array
