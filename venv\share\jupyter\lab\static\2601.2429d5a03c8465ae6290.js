"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[2601],{24982:(t,n,e)=>{e.d(n,{JLW:()=>Xr.A,l78:()=>g,tlR:()=>_,qrM:()=>ti.Ay,Yu4:()=>Qr.A,IA3:()=>$r.A,Wi0:()=>ri,PGM:()=>ii,OEq:()=>ui.A,y8u:()=>li.Ay,olC:()=>ai.A,IrU:()=>si.A,oDi:()=>hi.A,Q7f:()=>ci.A,cVp:()=>fi.A,lUB:()=>vi.A,Lx9:()=>pi.A,nVG:()=>di.G,uxU:()=>di.N,Xf2:()=>yi.A,GZz:()=>_i.Ay,UPb:()=>_i.Ps,dyv:()=>_i.Ko,bEH:()=>Pr.interpolateHcl,n8j:()=>qr.A,T9B:()=>r.A,jkA:()=>i.A,rLf:()=>Jr,WH:()=>Dr,m4Y:()=>Lr.A,UMr:()=>Ir.A,w7C:()=>Or.A,zt:()=>Yr,Ltv:()=>Rr,UAC:()=>bi.UA,DCK:()=>zi.DC,TUC:()=>xi.TU,Agd:()=>Ai.Ag,t6C:()=>mi.y,wXd:()=>wi.wX,ABi:()=>xi.AB,Ui6:()=>ki.Ui,rGn:()=>xi.rG,ucG:()=>gi.R,YPH:()=>xi.YP,Mol:()=>xi.Mo,PGu:()=>xi.PG,GuW:()=>xi.Gu});var r=e(21671);var i=e(44317);function o(t){return t}var u=1,a=2,s=3,l=4,c=1e-6;function f(t){return"translate("+t+",0)"}function h(t){return"translate(0,"+t+")"}function p(t){return n=>+t(n)}function v(t,n){n=Math.max(0,t.bandwidth()-n*2)/2;if(t.round())n=Math.round(n);return e=>+t(e)+n}function d(){return!this.__axis}function y(t,n){var e=[],r=null,i=null,y=6,_=6,m=3,g=typeof window!=="undefined"&&window.devicePixelRatio>1?0:.5,w=t===u||t===l?-1:1,A=t===l||t===a?"x":"y",b=t===u||t===s?f:h;function x(f){var h=r==null?n.ticks?n.ticks.apply(n,e):n.domain():r,x=i==null?n.tickFormat?n.tickFormat.apply(n,e):o:i,k=Math.max(y,0)+m,z=n.range(),M=+z[0]+g,E=+z[z.length-1]+g,T=(n.bandwidth?v:p)(n.copy(),g),C=f.selection?f.selection():f,S=C.selectAll(".domain").data([null]),N=C.selectAll(".tick").data(h,n).order(),P=N.exit(),V=N.enter().append("g").attr("class","tick"),B=N.select("line"),I=N.select("text");S=S.merge(S.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor"));N=N.merge(V);B=B.merge(V.append("line").attr("stroke","currentColor").attr(A+"2",w*y));I=I.merge(V.append("text").attr("fill","currentColor").attr(A,w*k).attr("dy",t===u?"0em":t===s?"0.71em":"0.32em"));if(f!==C){S=S.transition(f);N=N.transition(f);B=B.transition(f);I=I.transition(f);P=P.transition(f).attr("opacity",c).attr("transform",(function(t){return isFinite(t=T(t))?b(t+g):this.getAttribute("transform")}));V.attr("opacity",c).attr("transform",(function(t){var n=this.parentNode.__axis;return b((n&&isFinite(n=n(t))?n:T(t))+g)}))}P.remove();S.attr("d",t===l||t===a?_?"M"+w*_+","+M+"H"+g+"V"+E+"H"+w*_:"M"+g+","+M+"V"+E:_?"M"+M+","+w*_+"V"+g+"H"+E+"V"+w*_:"M"+M+","+g+"H"+E);N.attr("opacity",1).attr("transform",(function(t){return b(T(t)+g)}));B.attr(A+"2",w*y);I.attr(A,w*k).text(x);C.filter(d).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",t===a?"start":t===l?"end":"middle");C.each((function(){this.__axis=T}))}x.scale=function(t){return arguments.length?(n=t,x):n};x.ticks=function(){return e=Array.from(arguments),x};x.tickArguments=function(t){return arguments.length?(e=t==null?[]:Array.from(t),x):e.slice()};x.tickValues=function(t){return arguments.length?(r=t==null?null:Array.from(t),x):r&&r.slice()};x.tickFormat=function(t){return arguments.length?(i=t,x):i};x.tickSize=function(t){return arguments.length?(y=_=+t,x):y};x.tickSizeInner=function(t){return arguments.length?(y=+t,x):y};x.tickSizeOuter=function(t){return arguments.length?(_=+t,x):_};x.tickPadding=function(t){return arguments.length?(m=+t,x):m};x.offset=function(t){return arguments.length?(g=+t,x):g};return x}function _(t){return y(u,t)}function m(t){return y(a,t)}function g(t){return y(s,t)}function w(t){return y(l,t)}function A(){}function b(t){return t==null?A:function(){return this.querySelector(t)}}function x(t){if(typeof t!=="function")t=b(t);for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i){for(var o=n[i],u=o.length,a=r[i]=new Array(u),s,l,c=0;c<u;++c){if((s=o[c])&&(l=t.call(s,s.__data__,c,o))){if("__data__"in s)l.__data__=s.__data__;a[c]=l}}}return new mn(r,this._parents)}function k(t){return t==null?[]:Array.isArray(t)?t:Array.from(t)}function z(){return[]}function M(t){return t==null?z:function(){return this.querySelectorAll(t)}}function E(t){return function(){return k(t.apply(this,arguments))}}function T(t){if(typeof t==="function")t=E(t);else t=M(t);for(var n=this._groups,e=n.length,r=[],i=[],o=0;o<e;++o){for(var u=n[o],a=u.length,s,l=0;l<a;++l){if(s=u[l]){r.push(t.call(s,s.__data__,l,u));i.push(s)}}}return new mn(r,i)}function C(t){return function(){return this.matches(t)}}function S(t){return function(n){return n.matches(t)}}var N=Array.prototype.find;function P(t){return function(){return N.call(this.children,t)}}function V(){return this.firstElementChild}function B(t){return this.select(t==null?V:P(typeof t==="function"?t:S(t)))}var I=Array.prototype.filter;function D(){return Array.from(this.children)}function U(t){return function(){return I.call(this.children,t)}}function G(t){return this.selectAll(t==null?D:U(typeof t==="function"?t:S(t)))}function L(t){if(typeof t!=="function")t=C(t);for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i){for(var o=n[i],u=o.length,a=r[i]=[],s,l=0;l<u;++l){if((s=o[l])&&t.call(s,s.__data__,l,o)){a.push(s)}}}return new mn(r,this._parents)}function O(t){return new Array(t.length)}function H(){return new mn(this._enter||this._groups.map(O),this._parents)}function Y(t,n){this.ownerDocument=t.ownerDocument;this.namespaceURI=t.namespaceURI;this._next=null;this._parent=t;this.__data__=n}Y.prototype={constructor:Y,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};function R(t){return function(){return t}}function X(t,n,e,r,i,o){var u=0,a,s=n.length,l=o.length;for(;u<l;++u){if(a=n[u]){a.__data__=o[u];r[u]=a}else{e[u]=new Y(t,o[u])}}for(;u<s;++u){if(a=n[u]){i[u]=a}}}function q(t,n,e,r,i,o,u){var a,s,l=new Map,c=n.length,f=o.length,h=new Array(c),p;for(a=0;a<c;++a){if(s=n[a]){h[a]=p=u.call(s,s.__data__,a,n)+"";if(l.has(p)){i[a]=s}else{l.set(p,s)}}}for(a=0;a<f;++a){p=u.call(t,o[a],a,o)+"";if(s=l.get(p)){r[a]=s;s.__data__=o[a];l.delete(p)}else{e[a]=new Y(t,o[a])}}for(a=0;a<c;++a){if((s=n[a])&&l.get(h[a])===s){i[a]=s}}}function K(t){return t.__data__}function j(t,n){if(!arguments.length)return Array.from(this,K);var e=n?q:X,r=this._parents,i=this._groups;if(typeof t!=="function")t=R(t);for(var o=i.length,u=new Array(o),a=new Array(o),s=new Array(o),l=0;l<o;++l){var c=r[l],f=i[l],h=f.length,p=F(t.call(c,c&&c.__data__,l,r)),v=p.length,d=a[l]=new Array(v),y=u[l]=new Array(v),_=s[l]=new Array(h);e(c,f,d,y,_,p,n);for(var m=0,g=0,w,A;m<v;++m){if(w=d[m]){if(m>=g)g=m+1;while(!(A=y[g])&&++g<v);w._next=A||null}}}u=new mn(u,r);u._enter=a;u._exit=s;return u}function F(t){return typeof t==="object"&&"length"in t?t:Array.from(t)}function W(){return new mn(this._exit||this._groups.map(O),this._parents)}function Z(t,n,e){var r=this.enter(),i=this,o=this.exit();if(typeof t==="function"){r=t(r);if(r)r=r.selection()}else{r=r.append(t+"")}if(n!=null){i=n(i);if(i)i=i.selection()}if(e==null)o.remove();else e(o);return r&&i?r.merge(i).order():i}function J(t){var n=t.selection?t.selection():t;for(var e=this._groups,r=n._groups,i=e.length,o=r.length,u=Math.min(i,o),a=new Array(i),s=0;s<u;++s){for(var l=e[s],c=r[s],f=l.length,h=a[s]=new Array(f),p,v=0;v<f;++v){if(p=l[v]||c[v]){h[v]=p}}}for(;s<i;++s){a[s]=e[s]}return new mn(a,this._parents)}function Q(){for(var t=this._groups,n=-1,e=t.length;++n<e;){for(var r=t[n],i=r.length-1,o=r[i],u;--i>=0;){if(u=r[i]){if(o&&u.compareDocumentPosition(o)^4)o.parentNode.insertBefore(u,o);o=u}}}return this}function $(t){if(!t)t=tt;function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}for(var e=this._groups,r=e.length,i=new Array(r),o=0;o<r;++o){for(var u=e[o],a=u.length,s=i[o]=new Array(a),l,c=0;c<a;++c){if(l=u[c]){s[c]=l}}s.sort(n)}return new mn(i,this._parents).order()}function tt(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}function nt(){var t=arguments[0];arguments[0]=this;t.apply(null,arguments);return this}function et(){return Array.from(this)}function rt(){for(var t=this._groups,n=0,e=t.length;n<e;++n){for(var r=t[n],i=0,o=r.length;i<o;++i){var u=r[i];if(u)return u}}return null}function it(){let t=0;for(const n of this)++t;return t}function ot(){return!this.node()}function ut(t){for(var n=this._groups,e=0,r=n.length;e<r;++e){for(var i=n[e],o=0,u=i.length,a;o<u;++o){if(a=i[o])t.call(a,a.__data__,o,i)}}return this}var at="http://www.w3.org/1999/xhtml";const st={svg:"http://www.w3.org/2000/svg",xhtml:at,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function lt(t){var n=t+="",e=n.indexOf(":");if(e>=0&&(n=t.slice(0,e))!=="xmlns")t=t.slice(e+1);return st.hasOwnProperty(n)?{space:st[n],local:t}:t}function ct(t){return function(){this.removeAttribute(t)}}function ft(t){return function(){this.removeAttributeNS(t.space,t.local)}}function ht(t,n){return function(){this.setAttribute(t,n)}}function pt(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}function vt(t,n){return function(){var e=n.apply(this,arguments);if(e==null)this.removeAttribute(t);else this.setAttribute(t,e)}}function dt(t,n){return function(){var e=n.apply(this,arguments);if(e==null)this.removeAttributeNS(t.space,t.local);else this.setAttributeNS(t.space,t.local,e)}}function yt(t,n){var e=lt(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((n==null?e.local?ft:ct:typeof n==="function"?e.local?dt:vt:e.local?pt:ht)(e,n))}function _t(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function mt(t){return function(){this.style.removeProperty(t)}}function gt(t,n,e){return function(){this.style.setProperty(t,n,e)}}function wt(t,n,e){return function(){var r=n.apply(this,arguments);if(r==null)this.style.removeProperty(t);else this.style.setProperty(t,r,e)}}function At(t,n,e){return arguments.length>1?this.each((n==null?mt:typeof n==="function"?wt:gt)(t,n,e==null?"":e)):bt(this.node(),t)}function bt(t,n){return t.style.getPropertyValue(n)||_t(t).getComputedStyle(t,null).getPropertyValue(n)}function xt(t){return function(){delete this[t]}}function kt(t,n){return function(){this[t]=n}}function zt(t,n){return function(){var e=n.apply(this,arguments);if(e==null)delete this[t];else this[t]=e}}function Mt(t,n){return arguments.length>1?this.each((n==null?xt:typeof n==="function"?zt:kt)(t,n)):this.node()[t]}function Et(t){return t.trim().split(/^|\s+/)}function Tt(t){return t.classList||new Ct(t)}function Ct(t){this._node=t;this._names=Et(t.getAttribute("class")||"")}Ct.prototype={add:function(t){var n=this._names.indexOf(t);if(n<0){this._names.push(t);this._node.setAttribute("class",this._names.join(" "))}},remove:function(t){var n=this._names.indexOf(t);if(n>=0){this._names.splice(n,1);this._node.setAttribute("class",this._names.join(" "))}},contains:function(t){return this._names.indexOf(t)>=0}};function St(t,n){var e=Tt(t),r=-1,i=n.length;while(++r<i)e.add(n[r])}function Nt(t,n){var e=Tt(t),r=-1,i=n.length;while(++r<i)e.remove(n[r])}function Pt(t){return function(){St(this,t)}}function Vt(t){return function(){Nt(this,t)}}function Bt(t,n){return function(){(n.apply(this,arguments)?St:Nt)(this,t)}}function It(t,n){var e=Et(t+"");if(arguments.length<2){var r=Tt(this.node()),i=-1,o=e.length;while(++i<o)if(!r.contains(e[i]))return false;return true}return this.each((typeof n==="function"?Bt:n?Pt:Vt)(e,n))}function Dt(){this.textContent=""}function Ut(t){return function(){this.textContent=t}}function Gt(t){return function(){var n=t.apply(this,arguments);this.textContent=n==null?"":n}}function Lt(t){return arguments.length?this.each(t==null?Dt:(typeof t==="function"?Gt:Ut)(t)):this.node().textContent}function Ot(){this.innerHTML=""}function Ht(t){return function(){this.innerHTML=t}}function Yt(t){return function(){var n=t.apply(this,arguments);this.innerHTML=n==null?"":n}}function Rt(t){return arguments.length?this.each(t==null?Ot:(typeof t==="function"?Yt:Ht)(t)):this.node().innerHTML}function Xt(){if(this.nextSibling)this.parentNode.appendChild(this)}function qt(){return this.each(Xt)}function Kt(){if(this.previousSibling)this.parentNode.insertBefore(this,this.parentNode.firstChild)}function jt(){return this.each(Kt)}function Ft(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===at&&n.documentElement.namespaceURI===at?n.createElement(t):n.createElementNS(e,t)}}function Wt(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function Zt(t){var n=lt(t);return(n.local?Wt:Ft)(n)}function Jt(t){var n=typeof t==="function"?t:Zt(t);return this.select((function(){return this.appendChild(n.apply(this,arguments))}))}function Qt(){return null}function $t(t,n){var e=typeof t==="function"?t:Zt(t),r=n==null?Qt:typeof n==="function"?n:b(n);return this.select((function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)}))}function tn(){var t=this.parentNode;if(t)t.removeChild(this)}function nn(){return this.each(tn)}function en(){var t=this.cloneNode(false),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function rn(){var t=this.cloneNode(true),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function on(t){return this.select(t?rn:en)}function un(t){return arguments.length?this.property("__data__",t):this.node().__data__}function an(t){return function(n){t.call(this,n,this.__data__)}}function sn(t){return t.trim().split(/^|\s+/).map((function(t){var n="",e=t.indexOf(".");if(e>=0)n=t.slice(e+1),t=t.slice(0,e);return{type:t,name:n}}))}function ln(t){return function(){var n=this.__on;if(!n)return;for(var e=0,r=-1,i=n.length,o;e<i;++e){if(o=n[e],(!t.type||o.type===t.type)&&o.name===t.name){this.removeEventListener(o.type,o.listener,o.options)}else{n[++r]=o}}if(++r)n.length=r;else delete this.__on}}function cn(t,n,e){return function(){var r=this.__on,i,o=an(n);if(r)for(var u=0,a=r.length;u<a;++u){if((i=r[u]).type===t.type&&i.name===t.name){this.removeEventListener(i.type,i.listener,i.options);this.addEventListener(i.type,i.listener=o,i.options=e);i.value=n;return}}this.addEventListener(t.type,o,e);i={type:t.type,name:t.name,value:n,listener:o,options:e};if(!r)this.__on=[i];else r.push(i)}}function fn(t,n,e){var r=sn(t+""),i,o=r.length,u;if(arguments.length<2){var a=this.node().__on;if(a)for(var s=0,l=a.length,c;s<l;++s){for(i=0,c=a[s];i<o;++i){if((u=r[i]).type===c.type&&u.name===c.name){return c.value}}}return}a=n?cn:ln;for(i=0;i<o;++i)this.each(a(r[i],n,e));return this}function hn(t,n,e){var r=_t(t),i=r.CustomEvent;if(typeof i==="function"){i=new i(n,e)}else{i=r.document.createEvent("Event");if(e)i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail;else i.initEvent(n,false,false)}t.dispatchEvent(i)}function pn(t,n){return function(){return hn(this,t,n)}}function vn(t,n){return function(){return hn(this,t,n.apply(this,arguments))}}function dn(t,n){return this.each((typeof n==="function"?vn:pn)(t,n))}function*yn(){for(var t=this._groups,n=0,e=t.length;n<e;++n){for(var r=t[n],i=0,o=r.length,u;i<o;++i){if(u=r[i])yield u}}}var _n=[null];function mn(t,n){this._groups=t;this._parents=n}function gn(){return new mn([[document.documentElement]],_n)}function wn(){return this}mn.prototype=gn.prototype={constructor:mn,select:x,selectAll:T,selectChild:B,selectChildren:G,filter:L,data:j,enter:H,exit:W,join:Z,merge:J,selection:wn,order:Q,sort:$,call:nt,nodes:et,node:rt,size:it,empty:ot,each:ut,attr:yt,style:At,property:Mt,classed:It,text:Lt,html:Rt,raise:qt,lower:jt,append:Jt,insert:$t,remove:nn,clone:on,datum:un,on:fn,dispatch:dn,[Symbol.iterator]:yn};const An=gn;var bn=e(62996);var xn=e(14036);function kn(t,n,e){var r=new xn.M4;n=n==null?0:+n;r.restart((e=>{r.stop();t(e+n)}),n,e);return r}var zn=(0,bn.A)("start","end","cancel","interrupt");var Mn=[];var En=0;var Tn=1;var Cn=2;var Sn=3;var Nn=4;var Pn=5;var Vn=6;function Bn(t,n,e,r,i,o){var u=t.__transition;if(!u)t.__transition={};else if(e in u)return;Gn(t,e,{name:n,index:r,group:i,on:zn,tween:Mn,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:En})}function In(t,n){var e=Un(t,n);if(e.state>En)throw new Error("too late; already scheduled");return e}function Dn(t,n){var e=Un(t,n);if(e.state>Sn)throw new Error("too late; already running");return e}function Un(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw new Error("transition not found");return e}function Gn(t,n,e){var r=t.__transition,i;r[n]=e;e.timer=(0,xn.O1)(o,0,e.time);function o(t){e.state=Tn;e.timer.restart(u,e.delay,e.time);if(e.delay<=t)u(t-e.delay)}function u(o){var l,c,f,h;if(e.state!==Tn)return s();for(l in r){h=r[l];if(h.name!==e.name)continue;if(h.state===Sn)return kn(u);if(h.state===Nn){h.state=Vn;h.timer.stop();h.on.call("interrupt",t,t.__data__,h.index,h.group);delete r[l]}else if(+l<n){h.state=Vn;h.timer.stop();h.on.call("cancel",t,t.__data__,h.index,h.group);delete r[l]}}kn((function(){if(e.state===Sn){e.state=Nn;e.timer.restart(a,e.delay,e.time);a(o)}}));e.state=Cn;e.on.call("start",t,t.__data__,e.index,e.group);if(e.state!==Cn)return;e.state=Sn;i=new Array(f=e.tween.length);for(l=0,c=-1;l<f;++l){if(h=e.tween[l].value.call(t,t.__data__,e.index,e.group)){i[++c]=h}}i.length=c+1}function a(n){var r=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(s),e.state=Pn,1),o=-1,u=i.length;while(++o<u){i[o].call(t,r)}if(e.state===Pn){e.on.call("end",t,t.__data__,e.index,e.group);s()}}function s(){e.state=Vn;e.timer.stop();delete r[n];for(var i in r)return;delete t.__transition}}function Ln(t,n){var e=t.__transition,r,i,o=true,u;if(!e)return;n=n==null?null:n+"";for(u in e){if((r=e[u]).name!==n){o=false;continue}i=r.state>Cn&&r.state<Pn;r.state=Vn;r.timer.stop();r.on.call(i?"interrupt":"cancel",t,t.__data__,r.index,r.group);delete e[u]}if(o)delete t.__transition}function On(t){return this.each((function(){Ln(this,t)}))}var Hn=e(39480);function Yn(t,n){var e,r;return function(){var i=Dn(this,t),o=i.tween;if(o!==e){r=e=o;for(var u=0,a=r.length;u<a;++u){if(r[u].name===n){r=r.slice();r.splice(u,1);break}}}i.tween=r}}function Rn(t,n,e){var r,i;if(typeof e!=="function")throw new Error;return function(){var o=Dn(this,t),u=o.tween;if(u!==r){i=(r=u).slice();for(var a={name:n,value:e},s=0,l=i.length;s<l;++s){if(i[s].name===n){i[s]=a;break}}if(s===l)i.push(a)}o.tween=i}}function Xn(t,n){var e=this._id;t+="";if(arguments.length<2){var r=Un(this.node(),e).tween;for(var i=0,o=r.length,u;i<o;++i){if((u=r[i]).name===t){return u.value}}return null}return this.each((n==null?Yn:Rn)(e,t,n))}function qn(t,n,e){var r=t._id;t.each((function(){var t=Dn(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}));return function(t){return Un(t,r).value[n]}}var Kn=e(33844);var jn=e(85566);var Fn=e(79948);var Wn=e(23318);function Zn(t,n){var e;return(typeof n==="number"?jn.A:n instanceof Kn.Ay?Fn.Ay:(e=(0,Kn.Ay)(n))?(n=e,Fn.Ay):Wn.A)(t,n)}function Jn(t){return function(){this.removeAttribute(t)}}function Qn(t){return function(){this.removeAttributeNS(t.space,t.local)}}function $n(t,n,e){var r,i=e+"",o;return function(){var u=this.getAttribute(t);return u===i?null:u===r?o:o=n(r=u,e)}}function te(t,n,e){var r,i=e+"",o;return function(){var u=this.getAttributeNS(t.space,t.local);return u===i?null:u===r?o:o=n(r=u,e)}}function ne(t,n,e){var r,i,o;return function(){var u,a=e(this),s;if(a==null)return void this.removeAttribute(t);u=this.getAttribute(t);s=a+"";return u===s?null:u===r&&s===i?o:(i=s,o=n(r=u,a))}}function ee(t,n,e){var r,i,o;return function(){var u,a=e(this),s;if(a==null)return void this.removeAttributeNS(t.space,t.local);u=this.getAttributeNS(t.space,t.local);s=a+"";return u===s?null:u===r&&s===i?o:(i=s,o=n(r=u,a))}}function re(t,n){var e=lt(t),r=e==="transform"?Hn.I:Zn;return this.attrTween(t,typeof n==="function"?(e.local?ee:ne)(e,r,qn(this,"attr."+t,n)):n==null?(e.local?Qn:Jn)(e):(e.local?te:$n)(e,r,n))}function ie(t,n){return function(e){this.setAttribute(t,n.call(this,e))}}function oe(t,n){return function(e){this.setAttributeNS(t.space,t.local,n.call(this,e))}}function ue(t,n){var e,r;function i(){var i=n.apply(this,arguments);if(i!==r)e=(r=i)&&oe(t,i);return e}i._value=n;return i}function ae(t,n){var e,r;function i(){var i=n.apply(this,arguments);if(i!==r)e=(r=i)&&ie(t,i);return e}i._value=n;return i}function se(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(n==null)return this.tween(e,null);if(typeof n!=="function")throw new Error;var r=lt(t);return this.tween(e,(r.local?ue:ae)(r,n))}function le(t,n){return function(){In(this,t).delay=+n.apply(this,arguments)}}function ce(t,n){return n=+n,function(){In(this,t).delay=n}}function fe(t){var n=this._id;return arguments.length?this.each((typeof t==="function"?le:ce)(n,t)):Un(this.node(),n).delay}function he(t,n){return function(){Dn(this,t).duration=+n.apply(this,arguments)}}function pe(t,n){return n=+n,function(){Dn(this,t).duration=n}}function ve(t){var n=this._id;return arguments.length?this.each((typeof t==="function"?he:pe)(n,t)):Un(this.node(),n).duration}function de(t,n){if(typeof n!=="function")throw new Error;return function(){Dn(this,t).ease=n}}function ye(t){var n=this._id;return arguments.length?this.each(de(n,t)):Un(this.node(),n).ease}function _e(t,n){return function(){var e=n.apply(this,arguments);if(typeof e!=="function")throw new Error;Dn(this,t).ease=e}}function me(t){if(typeof t!=="function")throw new Error;return this.each(_e(this._id,t))}function ge(t){if(typeof t!=="function")t=C(t);for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i){for(var o=n[i],u=o.length,a=r[i]=[],s,l=0;l<u;++l){if((s=o[l])&&t.call(s,s.__data__,l,o)){a.push(s)}}}return new Fe(r,this._parents,this._name,this._id)}function we(t){if(t._id!==this._id)throw new Error;for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),u=new Array(r),a=0;a<o;++a){for(var s=n[a],l=e[a],c=s.length,f=u[a]=new Array(c),h,p=0;p<c;++p){if(h=s[p]||l[p]){f[p]=h}}}for(;a<r;++a){u[a]=n[a]}return new Fe(u,this._parents,this._name,this._id)}function Ae(t){return(t+"").trim().split(/^|\s+/).every((function(t){var n=t.indexOf(".");if(n>=0)t=t.slice(0,n);return!t||t==="start"}))}function be(t,n,e){var r,i,o=Ae(n)?In:Dn;return function(){var u=o(this,t),a=u.on;if(a!==r)(i=(r=a).copy()).on(n,e);u.on=i}}function xe(t,n){var e=this._id;return arguments.length<2?Un(this.node(),e).on.on(t):this.each(be(e,t,n))}function ke(t){return function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;if(n)n.removeChild(this)}}function ze(){return this.on("end.remove",ke(this._id))}function Me(t){var n=this._name,e=this._id;if(typeof t!=="function")t=b(t);for(var r=this._groups,i=r.length,o=new Array(i),u=0;u<i;++u){for(var a=r[u],s=a.length,l=o[u]=new Array(s),c,f,h=0;h<s;++h){if((c=a[h])&&(f=t.call(c,c.__data__,h,a))){if("__data__"in c)f.__data__=c.__data__;l[h]=f;Bn(l[h],n,e,h,l,Un(c,e))}}}return new Fe(o,this._parents,n,e)}function Ee(t){var n=this._name,e=this._id;if(typeof t!=="function")t=M(t);for(var r=this._groups,i=r.length,o=[],u=[],a=0;a<i;++a){for(var s=r[a],l=s.length,c,f=0;f<l;++f){if(c=s[f]){for(var h=t.call(c,c.__data__,f,s),p,v=Un(c,e),d=0,y=h.length;d<y;++d){if(p=h[d]){Bn(p,n,e,d,h,v)}}o.push(h);u.push(c)}}}return new Fe(o,u,n,e)}var Te=An.prototype.constructor;function Ce(){return new Te(this._groups,this._parents)}function Se(t,n){var e,r,i;return function(){var o=bt(this,t),u=(this.style.removeProperty(t),bt(this,t));return o===u?null:o===e&&u===r?i:i=n(e=o,r=u)}}function Ne(t){return function(){this.style.removeProperty(t)}}function Pe(t,n,e){var r,i=e+"",o;return function(){var u=bt(this,t);return u===i?null:u===r?o:o=n(r=u,e)}}function Ve(t,n,e){var r,i,o;return function(){var u=bt(this,t),a=e(this),s=a+"";if(a==null)s=a=(this.style.removeProperty(t),bt(this,t));return u===s?null:u===r&&s===i?o:(i=s,o=n(r=u,a))}}function Be(t,n){var e,r,i,o="style."+n,u="end."+o,a;return function(){var s=Dn(this,t),l=s.on,c=s.value[o]==null?a||(a=Ne(n)):undefined;if(l!==e||i!==c)(r=(e=l).copy()).on(u,i=c);s.on=r}}function Ie(t,n,e){var r=(t+="")==="transform"?Hn.T:Zn;return n==null?this.styleTween(t,Se(t,r)).on("end.style."+t,Ne(t)):typeof n==="function"?this.styleTween(t,Ve(t,r,qn(this,"style."+t,n))).each(Be(this._id,t)):this.styleTween(t,Pe(t,r,n),e).on("end.style."+t,null)}function De(t,n,e){return function(r){this.style.setProperty(t,n.call(this,r),e)}}function Ue(t,n,e){var r,i;function o(){var o=n.apply(this,arguments);if(o!==i)r=(i=o)&&De(t,o,e);return r}o._value=n;return o}function Ge(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(n==null)return this.tween(r,null);if(typeof n!=="function")throw new Error;return this.tween(r,Ue(t,n,e==null?"":e))}function Le(t){return function(){this.textContent=t}}function Oe(t){return function(){var n=t(this);this.textContent=n==null?"":n}}function He(t){return this.tween("text",typeof t==="function"?Oe(qn(this,"text",t)):Le(t==null?"":t+""))}function Ye(t){return function(n){this.textContent=t.call(this,n)}}function Re(t){var n,e;function r(){var r=t.apply(this,arguments);if(r!==e)n=(e=r)&&Ye(r);return n}r._value=t;return r}function Xe(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!=="function")throw new Error;return this.tween(n,Re(t))}function qe(){var t=this._name,n=this._id,e=Ze();for(var r=this._groups,i=r.length,o=0;o<i;++o){for(var u=r[o],a=u.length,s,l=0;l<a;++l){if(s=u[l]){var c=Un(s,n);Bn(s,t,e,l,u,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}}}return new Fe(r,this._parents,t,e)}function Ke(){var t,n,e=this,r=e._id,i=e.size();return new Promise((function(o,u){var a={value:u},s={value:function(){if(--i===0)o()}};e.each((function(){var e=Dn(this,r),i=e.on;if(i!==t){n=(t=i).copy();n._.cancel.push(a);n._.interrupt.push(a);n._.end.push(s)}e.on=n}));if(i===0)o()}))}var je=0;function Fe(t,n,e,r){this._groups=t;this._parents=n;this._name=e;this._id=r}function We(t){return An().transition(t)}function Ze(){return++je}var Je=An.prototype;Fe.prototype=We.prototype={constructor:Fe,select:Me,selectAll:Ee,selectChild:Je.selectChild,selectChildren:Je.selectChildren,filter:ge,merge:we,selection:Ce,transition:qe,call:Je.call,nodes:Je.nodes,node:Je.node,size:Je.size,empty:Je.empty,each:Je.each,on:xe,attr:re,attrTween:se,style:Ie,styleTween:Ge,text:He,textTween:Xe,remove:ze,tween:Xn,delay:fe,duration:ve,ease:ye,easeVarying:me,end:Ke,[Symbol.iterator]:Je[Symbol.iterator]};function Qe(t){return t*t*t}function $e(t){return--t*t*t+1}function tr(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}var nr={time:null,delay:0,duration:250,ease:tr};function er(t,n){var e;while(!(e=t.__transition)||!(e=e[n])){if(!(t=t.parentNode)){throw new Error(`transition ${n} not found`)}}return e}function rr(t){var n,e;if(t instanceof Fe){n=t._id,t=t._name}else{n=Ze(),(e=nr).time=(0,xn.tB)(),t=t==null?null:t+""}for(var r=this._groups,i=r.length,o=0;o<i;++o){for(var u=r[o],a=u.length,s,l=0;l<a;++l){if(s=u[l]){Bn(s,t,n,l,u,e||er(s,n))}}}return new Fe(r,this._parents,t,n)}An.prototype.interrupt=On;An.prototype.transition=rr;var ir={name:"drag"},or={name:"space"},ur={name:"handle"},ar={name:"center"};const{abs:sr,max:lr,min:cr}=Math;function fr(t){return[+t[0],+t[1]]}function hr(t){return[fr(t[0]),fr(t[1])]}var pr={name:"x",handles:["w","e"].map(Ar),input:function(t,n){return t==null?null:[[+t[0],n[0][1]],[+t[1],n[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}};var vr={name:"y",handles:["n","s"].map(Ar),input:function(t,n){return t==null?null:[[n[0][0],+t[0]],[n[1][0],+t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}};var dr={name:"xy",handles:["n","w","e","s","nw","ne","sw","se"].map(Ar),input:function(t){return t==null?null:hr(t)},output:function(t){return t}};var yr={overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"};var _r={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"};var mr={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"};var gr={overlay:+1,selection:+1,n:null,e:+1,s:null,w:-1,nw:-1,ne:+1,se:+1,sw:-1};var wr={overlay:+1,selection:+1,n:-1,e:null,s:+1,w:null,nw:-1,ne:-1,se:+1,sw:+1};function Ar(t){return{type:t}}function br(t){return!t.ctrlKey&&!t.button}function xr(){var t=this.ownerSVGElement||this;if(t.hasAttribute("viewBox")){t=t.viewBox.baseVal;return[[t.x,t.y],[t.x+t.width,t.y+t.height]]}return[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function kr(){return navigator.maxTouchPoints||"ontouchstart"in this}function zr(t){while(!t.__brush)if(!(t=t.parentNode))return;return t.__brush}function Mr(t){return t[0][0]===t[1][0]||t[0][1]===t[1][1]}function Er(t){var n=t.__brush;return n?n.dim.output(n.selection):null}function Tr(){return Nr(pr)}function Cr(){return Nr(vr)}function Sr(){return Nr(dr)}function Nr(t){var n=xr,e=br,r=kr,i=true,o=dispatch("start","brush","end"),u=6,a;function s(n){var e=n.property("__brush",d).selectAll(".overlay").data([Ar("overlay")]);e.enter().append("rect").attr("class","overlay").attr("pointer-events","all").attr("cursor",yr.overlay).merge(e).each((function(){var t=zr(this).extent;select(this).attr("x",t[0][0]).attr("y",t[0][1]).attr("width",t[1][0]-t[0][0]).attr("height",t[1][1]-t[0][1])}));n.selectAll(".selection").data([Ar("selection")]).enter().append("rect").attr("class","selection").attr("cursor",yr.selection).attr("fill","#777").attr("fill-opacity",.3).attr("stroke","#fff").attr("shape-rendering","crispEdges");var i=n.selectAll(".handle").data(t.handles,(function(t){return t.type}));i.exit().remove();i.enter().append("rect").attr("class",(function(t){return"handle handle--"+t.type})).attr("cursor",(function(t){return yr[t.type]}));n.each(l).attr("fill","none").attr("pointer-events","all").on("mousedown.brush",h).filter(r).on("touchstart.brush",h).on("touchmove.brush",p).on("touchend.brush touchcancel.brush",v).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}s.move=function(n,e,r){if(n.tween){n.on("start.brush",(function(t){c(this,arguments).beforestart().start(t)})).on("interrupt.brush end.brush",(function(t){c(this,arguments).end(t)})).tween("brush",(function(){var n=this,r=n.__brush,i=c(n,arguments),o=r.selection,u=t.input(typeof e==="function"?e.apply(this,arguments):e,r.extent),a=interpolate(o,u);function s(t){r.selection=t===1&&u===null?null:a(t);l.call(n);i.brush()}return o!==null&&u!==null?s:s(1)}))}else{n.each((function(){var n=this,i=arguments,o=n.__brush,u=t.input(typeof e==="function"?e.apply(n,i):e,o.extent),a=c(n,i).beforestart();interrupt(n);o.selection=u===null?null:u;l.call(n);a.start(r).brush(r).end(r)}))}};s.clear=function(t,n){s.move(t,null,n)};function l(){var t=select(this),n=zr(this).selection;if(n){t.selectAll(".selection").style("display",null).attr("x",n[0][0]).attr("y",n[0][1]).attr("width",n[1][0]-n[0][0]).attr("height",n[1][1]-n[0][1]);t.selectAll(".handle").style("display",null).attr("x",(function(t){return t.type[t.type.length-1]==="e"?n[1][0]-u/2:n[0][0]-u/2})).attr("y",(function(t){return t.type[0]==="s"?n[1][1]-u/2:n[0][1]-u/2})).attr("width",(function(t){return t.type==="n"||t.type==="s"?n[1][0]-n[0][0]+u:u})).attr("height",(function(t){return t.type==="e"||t.type==="w"?n[1][1]-n[0][1]+u:u}))}else{t.selectAll(".selection,.handle").style("display","none").attr("x",null).attr("y",null).attr("width",null).attr("height",null)}}function c(t,n,e){var r=t.__brush.emitter;return r&&(!e||!r.clean)?r:new f(t,n,e)}function f(t,n,e){this.that=t;this.args=n;this.state=t.__brush;this.active=0;this.clean=e}f.prototype={beforestart:function(){if(++this.active===1)this.state.emitter=this,this.starting=true;return this},start:function(t,n){if(this.starting)this.starting=false,this.emit("start",t,n);else this.emit("brush",t);return this},brush:function(t,n){this.emit("brush",t,n);return this},end:function(t,n){if(--this.active===0)delete this.state.emitter,this.emit("end",t,n);return this},emit:function(n,e,r){var i=select(this.that).datum();o.call(n,this.that,new BrushEvent(n,{sourceEvent:e,target:s,selection:t.output(this.state.selection),mode:r,dispatch:o}),i)}};function h(n){if(a&&!n.touches)return;if(!e.apply(this,arguments))return;var r=this,o=n.target.__data__.type,u=(i&&n.metaKey?o="overlay":o)==="selection"?ir:i&&n.altKey?ar:ur,s=t===vr?null:gr[o],f=t===pr?null:wr[o],h=zr(r),p=h.extent,v=h.selection,d=p[0][0],y,_,m=p[0][1],g,w,A=p[1][0],b,x,k=p[1][1],z,M,E=0,T=0,C,S=s&&f&&i&&n.shiftKey,N,P,V=Array.from(n.touches||[n],(t=>{const n=t.identifier;t=pointer(t,r);t.point0=t.slice();t.identifier=n;return t}));interrupt(r);var B=c(r,arguments,true).beforestart();if(o==="overlay"){if(v)C=true;const e=[V[0],V[1]||V[0]];h.selection=v=[[y=t===vr?d:cr(e[0][0],e[1][0]),g=t===pr?m:cr(e[0][1],e[1][1])],[b=t===vr?A:lr(e[0][0],e[1][0]),z=t===pr?k:lr(e[0][1],e[1][1])]];if(V.length>1)L(n)}else{y=v[0][0];g=v[0][1];b=v[1][0];z=v[1][1]}_=y;w=g;x=b;M=z;var I=select(r).attr("pointer-events","none");var D=I.selectAll(".overlay").attr("cursor",yr[o]);if(n.touches){B.moved=G;B.ended=O}else{var U=select(n.view).on("mousemove.brush",G,true).on("mouseup.brush",O,true);if(i)U.on("keydown.brush",H,true).on("keyup.brush",Y,true);dragDisable(n.view)}l.call(r);B.start(n,u.name);function G(t){for(const n of t.changedTouches||[t]){for(const t of V)if(t.identifier===n.identifier)t.cur=pointer(n,r)}if(S&&!N&&!P&&V.length===1){const t=V[0];if(sr(t.cur[0]-t[0])>sr(t.cur[1]-t[1]))P=true;else N=true}for(const n of V)if(n.cur)n[0]=n.cur[0],n[1]=n.cur[1];C=true;noevent(t);L(t)}function L(t){const n=V[0],e=n.point0;var i;E=n[0]-e[0];T=n[1]-e[1];switch(u){case or:case ir:{if(s)E=lr(d-y,cr(A-b,E)),_=y+E,x=b+E;if(f)T=lr(m-g,cr(k-z,T)),w=g+T,M=z+T;break}case ur:{if(V[1]){if(s)_=lr(d,cr(A,V[0][0])),x=lr(d,cr(A,V[1][0])),s=1;if(f)w=lr(m,cr(k,V[0][1])),M=lr(m,cr(k,V[1][1])),f=1}else{if(s<0)E=lr(d-y,cr(A-y,E)),_=y+E,x=b;else if(s>0)E=lr(d-b,cr(A-b,E)),_=y,x=b+E;if(f<0)T=lr(m-g,cr(k-g,T)),w=g+T,M=z;else if(f>0)T=lr(m-z,cr(k-z,T)),w=g,M=z+T}break}case ar:{if(s)_=lr(d,cr(A,y-E*s)),x=lr(d,cr(A,b+E*s));if(f)w=lr(m,cr(k,g-T*f)),M=lr(m,cr(k,z+T*f));break}}if(x<_){s*=-1;i=y,y=b,b=i;i=_,_=x,x=i;if(o in _r)D.attr("cursor",yr[o=_r[o]])}if(M<w){f*=-1;i=g,g=z,z=i;i=w,w=M,M=i;if(o in mr)D.attr("cursor",yr[o=mr[o]])}if(h.selection)v=h.selection;if(N)_=v[0][0],x=v[1][0];if(P)w=v[0][1],M=v[1][1];if(v[0][0]!==_||v[0][1]!==w||v[1][0]!==x||v[1][1]!==M){h.selection=[[_,w],[x,M]];l.call(r);B.brush(t,u.name)}}function O(t){nopropagation(t);if(t.touches){if(t.touches.length)return;if(a)clearTimeout(a);a=setTimeout((function(){a=null}),500)}else{dragEnable(t.view,C);U.on("keydown.brush keyup.brush mousemove.brush mouseup.brush",null)}I.attr("pointer-events","all");D.attr("cursor",yr.overlay);if(h.selection)v=h.selection;if(Mr(v))h.selection=null,l.call(r);B.end(t,u.name)}function H(t){switch(t.keyCode){case 16:{S=s&&f;break}case 18:{if(u===ur){if(s)b=x-E*s,y=_+E*s;if(f)z=M-T*f,g=w+T*f;u=ar;L(t)}break}case 32:{if(u===ur||u===ar){if(s<0)b=x-E;else if(s>0)y=_-E;if(f<0)z=M-T;else if(f>0)g=w-T;u=or;D.attr("cursor",yr.selection);L(t)}break}default:return}noevent(t)}function Y(t){switch(t.keyCode){case 16:{if(S){N=P=S=false;L(t)}break}case 18:{if(u===ar){if(s<0)b=x;else if(s>0)y=_;if(f<0)z=M;else if(f>0)g=w;u=ur;L(t)}break}case 32:{if(u===or){if(t.altKey){if(s)b=x-E*s,y=_+E*s;if(f)z=M-T*f,g=w+T*f;u=ar}else{if(s<0)b=x;else if(s>0)y=_;if(f<0)z=M;else if(f>0)g=w;u=ur}D.attr("cursor",yr[o]);L(t)}break}default:return}noevent(t)}}function p(t){c(this,arguments).moved(t)}function v(t){c(this,arguments).ended(t)}function d(){var e=this.__brush||{selection:null};e.extent=hr(n.apply(this,arguments));e.dim=t;return e}s.extent=function(t){return arguments.length?(n=typeof t==="function"?t:constant(hr(t)),s):n};s.filter=function(t){return arguments.length?(e=typeof t==="function"?t:constant(!!t),s):e};s.touchable=function(t){return arguments.length?(r=typeof t==="function"?t:constant(!!t),s):r};s.handleSize=function(t){return arguments.length?(u=+t,s):u};s.keyModifiers=function(t){return arguments.length?(i=!!t,s):i};s.on=function(){var t=o.on.apply(o,arguments);return t===o?s:t};return s}var Pr=e(67360);var Vr=e(18312);var Br=e(25758);var Ir=e(16527);function Dr(){var t=(0,Ir.A)().unknown(undefined),n=t.domain,e=t.range,r=0,i=1,o,u,a=false,s=0,l=0,c=.5;delete t.unknown;function f(){var t=n().length,f=i<r,h=f?i:r,p=f?r:i;o=(p-h)/Math.max(1,t-s+l*2);if(a)o=Math.floor(o);h+=(p-h-o*(t-s))*c;u=o*(1-s);if(a)h=Math.round(h),u=Math.round(u);var v=(0,Vr.A)(t).map((function(t){return h+o*t}));return e(f?v.reverse():v)}t.domain=function(t){return arguments.length?(n(t),f()):n()};t.range=function(t){return arguments.length?([r,i]=t,r=+r,i=+i,f()):[r,i]};t.rangeRound=function(t){return[r,i]=t,r=+r,i=+i,a=true,f()};t.bandwidth=function(){return u};t.step=function(){return o};t.round=function(t){return arguments.length?(a=!!t,f()):a};t.padding=function(t){return arguments.length?(s=Math.min(1,l=+t),f()):s};t.paddingInner=function(t){return arguments.length?(s=Math.min(1,t),f()):s};t.paddingOuter=function(t){return arguments.length?(l=+t,f()):l};t.align=function(t){return arguments.length?(c=Math.max(0,Math.min(1,t)),f()):c};t.copy=function(){return Dr(n(),[r,i]).round(a).paddingInner(s).paddingOuter(l).align(c)};return Br.C.apply(f(),arguments)}function Ur(t){var n=t.copy;t.padding=t.paddingOuter;delete t.paddingInner;delete t.paddingOuter;t.copy=function(){return Ur(n())};return t}function Gr(){return Ur(Dr.apply(null,arguments).paddingInner(1))}var Lr=e(20481);var Or=e(74725);var Hr=e(58177);const Yr=(0,Hr.A)("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab");function Rr(t){return typeof t==="string"?new mn([[document.querySelector(t)]],[document.documentElement]):new mn([[t]],_n)}var Xr=e(16681);var qr=e(58679);var Kr=e(12736);var jr=e(84653);function Fr(t,n){return n<t?-1:n>t?1:n>=t?0:NaN}function Wr(t){return t}var Zr=e(98247);function Jr(){var t=Wr,n=Fr,e=null,r=(0,jr.A)(0),i=(0,jr.A)(Zr.FA),o=(0,jr.A)(0);function u(u){var a,s=(u=(0,Kr.A)(u)).length,l,c,f=0,h=new Array(s),p=new Array(s),v=+r.apply(this,arguments),d=Math.min(Zr.FA,Math.max(-Zr.FA,i.apply(this,arguments)-v)),y,_=Math.min(Math.abs(d)/s,o.apply(this,arguments)),m=_*(d<0?-1:1),g;for(a=0;a<s;++a){if((g=p[h[a]=a]=+t(u[a],a,u))>0){f+=g}}if(n!=null)h.sort((function(t,e){return n(p[t],p[e])}));else if(e!=null)h.sort((function(t,n){return e(u[t],u[n])}));for(a=0,c=f?(d-s*m)/f:0;a<s;++a,v=y){l=h[a],g=p[l],y=v+(g>0?g*c:0)+m,p[l]={data:u[l],index:a,value:g,startAngle:v,endAngle:y,padAngle:_}}return p}u.value=function(n){return arguments.length?(t=typeof n==="function"?n:(0,jr.A)(+n),u):t};u.sortValues=function(t){return arguments.length?(n=t,e=null,u):n};u.sort=function(t){return arguments.length?(e=t,n=null,u):e};u.startAngle=function(t){return arguments.length?(r=typeof t==="function"?t:(0,jr.A)(+t),u):r};u.endAngle=function(t){return arguments.length?(i=typeof t==="function"?t:(0,jr.A)(+t),u):i};u.padAngle=function(t){return arguments.length?(o=typeof t==="function"?t:(0,jr.A)(+t),u):o};return u}var Qr=e(60075);var $r=e(69683);var ti=e(24363);class ni{constructor(t,n){this._context=t;this._x=n}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){if(this._line||this._line!==0&&this._point===1)this._context.closePath();this._line=1-this._line}point(t,n){t=+t,n=+n;switch(this._point){case 0:{this._point=1;if(this._line)this._context.lineTo(t,n);else this._context.moveTo(t,n);break}case 1:this._point=2;default:{if(this._x)this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,n,t,n);else this._context.bezierCurveTo(this._x0,this._y0=(this._y0+n)/2,t,this._y0,t,n);break}}this._x0=t,this._y0=n}}class ei{constructor(t){this._context=t}lineStart(){this._point=0}lineEnd(){}point(t,n){t=+t,n=+n;if(this._point===0){this._point=1}else{const e=pointRadial(this._x0,this._y0);const r=pointRadial(this._x0,this._y0=(this._y0+n)/2);const i=pointRadial(t,this._y0);const o=pointRadial(t,n);this._context.moveTo(...e);this._context.bezierCurveTo(...r,...i,...o)}this._x0=t,this._y0=n}}function ri(t){return new ni(t,true)}function ii(t){return new ni(t,false)}function oi(t){return new ei(t)}var ui=e(54545);var ai=e(13893);var si=e(46457);var li=e(43793);var ci=e(25633);var fi=e(13309);var hi=e(76413);var pi=e(43272);var vi=e(71228);var di=e(67694);var yi=e(29944);var _i=e(79011);var mi=e(26530);var gi=e(61147);var wi=e(23383);var Ai=e(9017);var bi=e(20293);var xi=e(61779);var ki=e(77849);var zi=e(82692);function Mi(t,n,e){this.k=t;this.x=n;this.y=e}Mi.prototype={constructor:Mi,scale:function(t){return t===1?this:new Mi(this.k*t,this.x,this.y)},translate:function(t,n){return t===0&n===0?this:new Mi(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var Ei=new Mi(1,0,0);Ti.prototype=Mi.prototype;function Ti(t){while(!t.__zoom)if(!(t=t.parentNode))return Ei;return t.__zoom}function Ci(t){return(!t.ctrlKey||t.type==="wheel")&&!t.button}function Si(){var t=this;if(t instanceof SVGElement){t=t.ownerSVGElement||t;if(t.hasAttribute("viewBox")){t=t.viewBox.baseVal;return[[t.x,t.y],[t.x+t.width,t.y+t.height]]}return[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}return[[0,0],[t.clientWidth,t.clientHeight]]}function Ni(){return this.__zoom||identity}function Pi(t){return-t.deltaY*(t.deltaMode===1?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function Vi(){return navigator.maxTouchPoints||"ontouchstart"in this}function Bi(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],u=t.invertY(n[1][1])-e[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),u>o?(o+u)/2:Math.min(0,o)||Math.max(0,u))}function Ii(){var t=Ci,n=Si,e=Bi,r=Pi,i=Vi,o=[0,Infinity],u=[[-Infinity,-Infinity],[Infinity,Infinity]],a=250,s=interpolateZoom,l=dispatch("start","zoom","end"),c,f,h,p=500,v=150,d=0,y=10;function _(t){t.property("__zoom",Ni).on("wheel.zoom",k,{passive:false}).on("mousedown.zoom",z).on("dblclick.zoom",M).filter(i).on("touchstart.zoom",E).on("touchmove.zoom",T).on("touchend.zoom touchcancel.zoom",C).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}_.transform=function(t,n,e,r){var i=t.selection?t.selection():t;i.property("__zoom",Ni);if(t!==i){A(t,n,e,r)}else{i.interrupt().each((function(){b(this,arguments).event(r).start().zoom(null,typeof n==="function"?n.apply(this,arguments):n).end()}))}};_.scaleBy=function(t,n,e,r){_.scaleTo(t,(function(){var t=this.__zoom.k,e=typeof n==="function"?n.apply(this,arguments):n;return t*e}),e,r)};_.scaleTo=function(t,r,i,o){_.transform(t,(function(){var t=n.apply(this,arguments),o=this.__zoom,a=i==null?w(t):typeof i==="function"?i.apply(this,arguments):i,s=o.invert(a),l=typeof r==="function"?r.apply(this,arguments):r;return e(g(m(o,l),a,s),t,u)}),i,o)};_.translateBy=function(t,r,i,o){_.transform(t,(function(){return e(this.__zoom.translate(typeof r==="function"?r.apply(this,arguments):r,typeof i==="function"?i.apply(this,arguments):i),n.apply(this,arguments),u)}),null,o)};_.translateTo=function(t,r,i,o,a){_.transform(t,(function(){var t=n.apply(this,arguments),a=this.__zoom,s=o==null?w(t):typeof o==="function"?o.apply(this,arguments):o;return e(identity.translate(s[0],s[1]).scale(a.k).translate(typeof r==="function"?-r.apply(this,arguments):-r,typeof i==="function"?-i.apply(this,arguments):-i),t,u)}),o,a)};function m(t,n){n=Math.max(o[0],Math.min(o[1],n));return n===t.k?t:new Transform(n,t.x,t.y)}function g(t,n,e){var r=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return r===t.x&&i===t.y?t:new Transform(t.k,r,i)}function w(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function A(t,e,r,i){t.on("start.zoom",(function(){b(this,arguments).event(i).start()})).on("interrupt.zoom end.zoom",(function(){b(this,arguments).event(i).end()})).tween("zoom",(function(){var t=this,o=arguments,u=b(t,o).event(i),a=n.apply(t,o),l=r==null?w(a):typeof r==="function"?r.apply(t,o):r,c=Math.max(a[1][0]-a[0][0],a[1][1]-a[0][1]),f=t.__zoom,h=typeof e==="function"?e.apply(t,o):e,p=s(f.invert(l).concat(c/f.k),h.invert(l).concat(c/h.k));return function(t){if(t===1)t=h;else{var n=p(t),e=c/n[2];t=new Transform(e,l[0]-n[0]*e,l[1]-n[1]*e)}u.zoom(null,t)}}))}function b(t,n,e){return!e&&t.__zooming||new x(t,n)}function x(t,e){this.that=t;this.args=e;this.active=0;this.sourceEvent=null;this.extent=n.apply(t,e);this.taps=0}x.prototype={event:function(t){if(t)this.sourceEvent=t;return this},start:function(){if(++this.active===1){this.that.__zooming=this;this.emit("start")}return this},zoom:function(t,n){if(this.mouse&&t!=="mouse")this.mouse[1]=n.invert(this.mouse[0]);if(this.touch0&&t!=="touch")this.touch0[1]=n.invert(this.touch0[0]);if(this.touch1&&t!=="touch")this.touch1[1]=n.invert(this.touch1[0]);this.that.__zoom=n;this.emit("zoom");return this},end:function(){if(--this.active===0){delete this.that.__zooming;this.emit("end")}return this},emit:function(t){var n=select(this.that).datum();l.call(t,this.that,new ZoomEvent(t,{sourceEvent:this.sourceEvent,target:_,type:t,transform:this.that.__zoom,dispatch:l}),n)}};function k(n,...i){if(!t.apply(this,arguments))return;var a=b(this,i).event(n),s=this.__zoom,l=Math.max(o[0],Math.min(o[1],s.k*Math.pow(2,r.apply(this,arguments)))),c=pointer(n);if(a.wheel){if(a.mouse[0][0]!==c[0]||a.mouse[0][1]!==c[1]){a.mouse[1]=s.invert(a.mouse[0]=c)}clearTimeout(a.wheel)}else if(s.k===l)return;else{a.mouse=[c,s.invert(c)];interrupt(this);a.start()}noevent(n);a.wheel=setTimeout(f,v);a.zoom("mouse",e(g(m(s,l),a.mouse[0],a.mouse[1]),a.extent,u));function f(){a.wheel=null;a.end()}}function z(n,...r){if(h||!t.apply(this,arguments))return;var i=n.currentTarget,o=b(this,r,true).event(n),a=select(n.view).on("mousemove.zoom",f,true).on("mouseup.zoom",p,true),s=pointer(n,i),l=n.clientX,c=n.clientY;dragDisable(n.view);nopropagation(n);o.mouse=[s,this.__zoom.invert(s)];interrupt(this);o.start();function f(t){noevent(t);if(!o.moved){var n=t.clientX-l,r=t.clientY-c;o.moved=n*n+r*r>d}o.event(t).zoom("mouse",e(g(o.that.__zoom,o.mouse[0]=pointer(t,i),o.mouse[1]),o.extent,u))}function p(t){a.on("mousemove.zoom mouseup.zoom",null);dragEnable(t.view,o.moved);noevent(t);o.event(t).end()}}function M(r,...i){if(!t.apply(this,arguments))return;var o=this.__zoom,s=pointer(r.changedTouches?r.changedTouches[0]:r,this),l=o.invert(s),c=o.k*(r.shiftKey?.5:2),f=e(g(m(o,c),s,l),n.apply(this,i),u);noevent(r);if(a>0)select(this).transition().duration(a).call(A,f,s,r);else select(this).call(_.transform,f,s,r)}function E(n,...e){if(!t.apply(this,arguments))return;var r=n.touches,i=r.length,o=b(this,e,n.changedTouches.length===i).event(n),u,a,s,l;nopropagation(n);for(a=0;a<i;++a){s=r[a],l=pointer(s,this);l=[l,this.__zoom.invert(l),s.identifier];if(!o.touch0)o.touch0=l,u=true,o.taps=1+!!c;else if(!o.touch1&&o.touch0[2]!==l[2])o.touch1=l,o.taps=0}if(c)c=clearTimeout(c);if(u){if(o.taps<2)f=l[0],c=setTimeout((function(){c=null}),p);interrupt(this);o.start()}}function T(t,...n){if(!this.__zooming)return;var r=b(this,n).event(t),i=t.changedTouches,o=i.length,a,s,l,c;noevent(t);for(a=0;a<o;++a){s=i[a],l=pointer(s,this);if(r.touch0&&r.touch0[2]===s.identifier)r.touch0[0]=l;else if(r.touch1&&r.touch1[2]===s.identifier)r.touch1[0]=l}s=r.that.__zoom;if(r.touch1){var f=r.touch0[0],h=r.touch0[1],p=r.touch1[0],v=r.touch1[1],d=(d=p[0]-f[0])*d+(d=p[1]-f[1])*d,y=(y=v[0]-h[0])*y+(y=v[1]-h[1])*y;s=m(s,Math.sqrt(d/y));l=[(f[0]+p[0])/2,(f[1]+p[1])/2];c=[(h[0]+v[0])/2,(h[1]+v[1])/2]}else if(r.touch0)l=r.touch0[0],c=r.touch0[1];else return;r.zoom("touch",e(g(s,l,c),r.extent,u))}function C(t,...n){if(!this.__zooming)return;var e=b(this,n).event(t),r=t.changedTouches,i=r.length,o,u;nopropagation(t);if(h)clearTimeout(h);h=setTimeout((function(){h=null}),p);for(o=0;o<i;++o){u=r[o];if(e.touch0&&e.touch0[2]===u.identifier)delete e.touch0;else if(e.touch1&&e.touch1[2]===u.identifier)delete e.touch1}if(e.touch1&&!e.touch0)e.touch0=e.touch1,delete e.touch1;if(e.touch0)e.touch0[1]=this.__zoom.invert(e.touch0[0]);else{e.end();if(e.taps===2){u=pointer(u,this);if(Math.hypot(f[0]-u[0],f[1]-u[1])<y){var a=select(this).on("dblclick.zoom");if(a)a.apply(this,arguments)}}}}_.wheelDelta=function(t){return arguments.length?(r=typeof t==="function"?t:constant(+t),_):r};_.filter=function(n){return arguments.length?(t=typeof n==="function"?n:constant(!!n),_):t};_.touchable=function(t){return arguments.length?(i=typeof t==="function"?t:constant(!!t),_):i};_.extent=function(t){return arguments.length?(n=typeof t==="function"?t:constant([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),_):n};_.scaleExtent=function(t){return arguments.length?(o[0]=+t[0],o[1]=+t[1],_):[o[0],o[1]]};_.translateExtent=function(t){return arguments.length?(u[0][0]=+t[0][0],u[1][0]=+t[1][0],u[0][1]=+t[0][1],u[1][1]=+t[1][1],_):[[u[0][0],u[0][1]],[u[1][0],u[1][1]]]};_.constrain=function(t){return arguments.length?(e=t,_):e};_.duration=function(t){return arguments.length?(a=+t,_):a};_.interpolate=function(t){return arguments.length?(s=t,_):s};_.on=function(){var t=l.on.apply(l,arguments);return t===l?_:t};_.clickDistance=function(t){return arguments.length?(d=(t=+t)*t,_):Math.sqrt(d)};_.tapDistance=function(t){return arguments.length?(y=+t,_):y};return _}}}]);