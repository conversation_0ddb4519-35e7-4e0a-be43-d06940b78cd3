{"jupyter.lab.setting-icon": "ui-components:code-check", "jupyter.lab.setting-icon-label": "Language integration", "jupyter.lab.transform": true, "title": "Language Servers (Experimental)", "description": "Language Server Protocol settings.", "type": "object", "definitions": {"languageServer": {"type": "object", "default": {"configuration": {}, "rank": 50}, "properties": {"configuration": {"title": "Language Server Configurations", "description": "Configuration to be sent to language server over LSP when initialized: see the specific language server's documentation for more", "type": "object", "default": {}, "patternProperties": {".*": {"type": ["number", "string", "boolean", "object", "array"]}}, "additionalProperties": true}, "rank": {"title": "Rank of the server", "description": "When multiple servers match specific document/language, the server with the highest rank will be used", "type": "number", "default": 50, "minimum": 1}}}}, "properties": {"activate": {"title": "Activate", "description": "Enable or disable the language server services.", "enum": ["off", "on"], "default": "off"}, "languageServers": {"title": "Language Server", "description": "Language-server specific configuration, keyed by implementation", "type": "object", "default": {}, "patternProperties": {".*": {"$ref": "#/definitions/languageServer"}}}, "setTrace": {"title": "Ask servers to send trace notifications", "enum": ["off", "messages", "verbose"], "default": "off", "description": "Whether to ask server to send logs with execution trace (for debugging). Accepted values are: \"off\", \"messages\", \"verbose\". Servers are allowed to ignore this request."}, "logAllCommunication": {"title": "Log communication", "type": "boolean", "default": false, "description": "Enable or disable the logging feature of the language servers."}}}