# LiDAR Processing Configuration
# Configuration for real-time LiDAR processing and object detection

# Processing parameters
processing_rate: 30.0          # Processing frequency in Hz
buffer_size: 100               # Maximum buffer size for incoming data
enable_threading: true         # Enable multithreaded processing

# Range filtering
max_range: 100.0              # Maximum detection range in meters
min_range: 0.5                # Minimum detection range in meters

# Ground segmentation
ground_threshold: 0.2         # Distance threshold for ground plane detection in meters
ransac_iterations: 100        # Maximum RANSAC iterations for ground plane fitting
min_ground_inliers: 1000      # Minimum inliers required for valid ground plane

# Object clustering
cluster_tolerance: 0.5        # Clustering distance tolerance in meters
min_cluster_size: 10          # Minimum points per cluster
max_cluster_size: 5000        # Maximum points per cluster

# Voxel grid downsampling
voxel_size: 0.1              # Voxel grid size in meters
use_voxel_grid: true         # Enable voxel grid downsampling

# Region of Interest (ROI) filtering
use_roi_filtering: true      # Enable ROI filtering
roi_x_min: -10.0            # ROI minimum X coordinate
roi_x_max: 50.0             # ROI maximum X coordinate
roi_y_min: -20.0            # ROI minimum Y coordinate
roi_y_max: 20.0             # ROI maximum Y coordinate
roi_z_min: -3.0             # ROI minimum Z coordinate
roi_z_max: 5.0              # ROI maximum Z coordinate

# Object classification thresholds
person_height_min: 1.5       # Minimum height for person classification
person_height_max: 2.0       # Maximum height for person classification
vehicle_length_min: 3.0      # Minimum length for vehicle classification
vehicle_width_min: 1.5       # Minimum width for vehicle classification
vehicle_height_min: 1.0      # Minimum height for vehicle classification

# Performance optimization
use_gpu_acceleration: false  # Enable GPU acceleration (requires CUDA)
max_threads: 4              # Maximum number of processing threads

# Visualization
publish_ground_points: true  # Publish ground points for visualization
publish_object_points: true  # Publish object points for visualization
publish_clusters: true       # Publish individual clusters
publish_bounding_boxes: true # Publish object bounding boxes

# Coordinate frames
lidar_frame: "lidar_frame"   # LiDAR coordinate frame
base_frame: "base_link"      # Vehicle base coordinate frame
