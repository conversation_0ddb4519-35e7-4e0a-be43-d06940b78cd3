{"version": 3, "file": "1618.da67fb30732c49b969ba.js?v=da67fb30732c49b969ba", "mappings": ";;;;;;;;;;AAAA;AACA;AACA,kBAAkB,kBAAkB;AACpC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,MAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,+CAA+C;AAC/C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,4BAA4B,UAAU;AACtC;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,GAAG;;AAEH;AACA,6BAA6B;AAC7B,oBAAoB,oBAAoB,yBAAyB;AACjE,oBAAoB,uBAAuB;AAC3C;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/swift.js"], "sourcesContent": ["function wordSet(words) {\n  var set = {}\n  for (var i = 0; i < words.length; i++) set[words[i]] = true\n  return set\n}\n\nvar keywords = wordSet([\"_\",\"var\",\"let\",\"actor\",\"class\",\"enum\",\"extension\",\"import\",\"protocol\",\"struct\",\"func\",\"typealias\",\"associatedtype\",\n                        \"open\",\"public\",\"internal\",\"fileprivate\",\"private\",\"deinit\",\"init\",\"new\",\"override\",\"self\",\"subscript\",\"super\",\n                        \"convenience\",\"dynamic\",\"final\",\"indirect\",\"lazy\",\"required\",\"static\",\"unowned\",\"unowned(safe)\",\"unowned(unsafe)\",\"weak\",\"as\",\"is\",\n                        \"break\",\"case\",\"continue\",\"default\",\"else\",\"fallthrough\",\"for\",\"guard\",\"if\",\"in\",\"repeat\",\"switch\",\"where\",\"while\",\n                        \"defer\",\"return\",\"inout\",\"mutating\",\"nonmutating\",\"isolated\",\"nonisolated\",\"catch\",\"do\",\"rethrows\",\"throw\",\"throws\",\"async\",\"await\",\"try\",\"didSet\",\"get\",\"set\",\"willSet\",\n                        \"assignment\",\"associativity\",\"infix\",\"left\",\"none\",\"operator\",\"postfix\",\"precedence\",\"precedencegroup\",\"prefix\",\"right\",\n                        \"Any\",\"AnyObject\",\"Type\",\"dynamicType\",\"Self\",\"Protocol\",\"__COLUMN__\",\"__FILE__\",\"__FUNCTION__\",\"__LINE__\"])\nvar definingKeywords = wordSet([\"var\",\"let\",\"actor\",\"class\",\"enum\",\"extension\",\"import\",\"protocol\",\"struct\",\"func\",\"typealias\",\"associatedtype\",\"for\"])\nvar atoms = wordSet([\"true\",\"false\",\"nil\",\"self\",\"super\",\"_\"])\nvar types = wordSet([\"Array\",\"Bool\",\"Character\",\"Dictionary\",\"Double\",\"Float\",\"Int\",\"Int8\",\"Int16\",\"Int32\",\"Int64\",\"Never\",\"Optional\",\"Set\",\"String\",\n                     \"UInt8\",\"UInt16\",\"UInt32\",\"UInt64\",\"Void\"])\nvar operators = \"+-/*%=|&<>~^?!\"\nvar punc = \":;,.(){}[]\"\nvar binary = /^\\-?0b[01][01_]*/\nvar octal = /^\\-?0o[0-7][0-7_]*/\nvar hexadecimal = /^\\-?0x[\\dA-Fa-f][\\dA-Fa-f_]*(?:(?:\\.[\\dA-Fa-f][\\dA-Fa-f_]*)?[Pp]\\-?\\d[\\d_]*)?/\nvar decimal = /^\\-?\\d[\\d_]*(?:\\.\\d[\\d_]*)?(?:[Ee]\\-?\\d[\\d_]*)?/\nvar identifier = /^\\$\\d+|(`?)[_A-Za-z][_A-Za-z$0-9]*\\1/\nvar property = /^\\.(?:\\$\\d+|(`?)[_A-Za-z][_A-Za-z$0-9]*\\1)/\nvar instruction = /^\\#[A-Za-z]+/\nvar attribute = /^@(?:\\$\\d+|(`?)[_A-Za-z][_A-Za-z$0-9]*\\1)/\n//var regexp = /^\\/(?!\\s)(?:\\/\\/)?(?:\\\\.|[^\\/])+\\//\n\nfunction tokenBase(stream, state, prev) {\n  if (stream.sol()) state.indented = stream.indentation()\n  if (stream.eatSpace()) return null\n\n  var ch = stream.peek()\n  if (ch == \"/\") {\n    if (stream.match(\"//\")) {\n      stream.skipToEnd()\n      return \"comment\"\n    }\n    if (stream.match(\"/*\")) {\n      state.tokenize.push(tokenComment)\n      return tokenComment(stream, state)\n    }\n  }\n  if (stream.match(instruction)) return \"builtin\"\n  if (stream.match(attribute)) return \"attribute\"\n  if (stream.match(binary)) return \"number\"\n  if (stream.match(octal)) return \"number\"\n  if (stream.match(hexadecimal)) return \"number\"\n  if (stream.match(decimal)) return \"number\"\n  if (stream.match(property)) return \"property\"\n  if (operators.indexOf(ch) > -1) {\n    stream.next()\n    return \"operator\"\n  }\n  if (punc.indexOf(ch) > -1) {\n    stream.next()\n    stream.match(\"..\")\n    return \"punctuation\"\n  }\n  var stringMatch\n  if (stringMatch = stream.match(/(\"\"\"|\"|')/)) {\n    var tokenize = tokenString.bind(null, stringMatch[0])\n    state.tokenize.push(tokenize)\n    return tokenize(stream, state)\n  }\n\n  if (stream.match(identifier)) {\n    var ident = stream.current()\n    if (types.hasOwnProperty(ident)) return \"type\"\n    if (atoms.hasOwnProperty(ident)) return \"atom\"\n    if (keywords.hasOwnProperty(ident)) {\n      if (definingKeywords.hasOwnProperty(ident))\n        state.prev = \"define\"\n      return \"keyword\"\n    }\n    if (prev == \"define\") return \"def\"\n    return \"variable\"\n  }\n\n  stream.next()\n  return null\n}\n\nfunction tokenUntilClosingParen() {\n  var depth = 0\n  return function(stream, state, prev) {\n    var inner = tokenBase(stream, state, prev)\n    if (inner == \"punctuation\") {\n      if (stream.current() == \"(\") ++depth\n      else if (stream.current() == \")\") {\n        if (depth == 0) {\n          stream.backUp(1)\n          state.tokenize.pop()\n          return state.tokenize[state.tokenize.length - 1](stream, state)\n        }\n        else --depth\n      }\n    }\n    return inner\n  }\n}\n\nfunction tokenString(openQuote, stream, state) {\n  var singleLine = openQuote.length == 1\n  var ch, escaped = false\n  while (ch = stream.peek()) {\n    if (escaped) {\n      stream.next()\n      if (ch == \"(\") {\n        state.tokenize.push(tokenUntilClosingParen())\n        return \"string\"\n      }\n      escaped = false\n    } else if (stream.match(openQuote)) {\n      state.tokenize.pop()\n      return \"string\"\n    } else {\n      stream.next()\n      escaped = ch == \"\\\\\"\n    }\n  }\n  if (singleLine) {\n    state.tokenize.pop()\n  }\n  return \"string\"\n}\n\nfunction tokenComment(stream, state) {\n  var ch\n  while (ch = stream.next()) {\n    if (ch === \"/\" && stream.eat(\"*\")) {\n      state.tokenize.push(tokenComment)\n    } else if (ch === \"*\" && stream.eat(\"/\")) {\n      state.tokenize.pop()\n      break\n    }\n  }\n  return \"comment\"\n}\n\nfunction Context(prev, align, indented) {\n  this.prev = prev\n  this.align = align\n  this.indented = indented\n}\n\nfunction pushContext(state, stream) {\n  var align = stream.match(/^\\s*($|\\/[\\/\\*]|[)}\\]])/, false) ? null : stream.column() + 1\n  state.context = new Context(state.context, align, state.indented)\n}\n\nfunction popContext(state) {\n  if (state.context) {\n    state.indented = state.context.indented\n    state.context = state.context.prev\n  }\n}\n\nexport const swift = {\n  name: \"swift\",\n  startState: function() {\n    return {\n      prev: null,\n      context: null,\n      indented: 0,\n      tokenize: []\n    }\n  },\n\n  token: function(stream, state) {\n    var prev = state.prev\n    state.prev = null\n    var tokenize = state.tokenize[state.tokenize.length - 1] || tokenBase\n    var style = tokenize(stream, state, prev)\n    if (!style || style == \"comment\") state.prev = prev\n    else if (!state.prev) state.prev = style\n\n    if (style == \"punctuation\") {\n      var bracket = /[\\(\\[\\{]|([\\]\\)\\}])/.exec(stream.current())\n      if (bracket) (bracket[1] ? popContext : pushContext)(state, stream)\n    }\n\n    return style\n  },\n\n  indent: function(state, textAfter, iCx) {\n    var cx = state.context\n    if (!cx) return 0\n    var closing = /^[\\]\\}\\)]/.test(textAfter)\n    if (cx.align != null) return cx.align - (closing ? 1 : 0)\n    return cx.indented + (closing ? 0 : iCx.unit)\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[\\)\\}\\]]$/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"]}\n  }\n}\n"], "names": [], "sourceRoot": ""}