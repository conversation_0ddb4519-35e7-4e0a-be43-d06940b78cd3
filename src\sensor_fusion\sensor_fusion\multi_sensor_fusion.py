"""
Multi-Sensor Fusion Module
Demonstrates advanced sensor fusion techniques including Kalman filtering,
particle filtering, and weighted fusion algorithms
"""

import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import time
from dataclasses import dataclass, field
from enum import Enum
import threading
import queue
from scipy.spatial.distance import cdist
from scipy.optimize import linear_sum_assignment
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Point, Pose, Twist, PoseWithCovariance
from visualization_msgs.msg import Marker, MarkerArray
from std_msgs.msg import Header, ColorRGBA

from sensor_processing.math_utils import MathUtils


class SensorType(Enum):
    """Enumeration of sensor types"""
    LIDAR = "lidar"
    RADAR = "radar"
    CAMERA = "camera"
    IMU = "imu"
    GPS = "gps"


@dataclass
class SensorMeasurement:
    """Represents a measurement from any sensor"""
    sensor_type: SensorType
    timestamp: float
    position: Optional[np.ndarray] = None  # [x, y, z]
    velocity: Optional[np.ndarray] = None  # [vx, vy, vz]
    covariance: Optional[np.ndarray] = None
    confidence: float = 1.0
    sensor_id: str = ""
    raw_data: Any = None


@dataclass
class FusedObject:
    """Represents a fused object from multiple sensors"""
    object_id: int
    position: np.ndarray  # [x, y, z]
    velocity: np.ndarray  # [vx, vy, vz]
    acceleration: np.ndarray  # [ax, ay, az]
    covariance: np.ndarray  # 9x9 covariance matrix
    last_update: float
    sensor_contributions: Dict[SensorType, float] = field(default_factory=dict)
    track_history: List[np.ndarray] = field(default_factory=list)
    confidence: float = 1.0
    object_type: str = "unknown"


class KalmanFilter:
    """
    Extended Kalman Filter for object tracking
    State vector: [x, y, z, vx, vy, vz, ax, ay, az]
    """
    
    def __init__(self, dt: float = 0.1):
        self.dt = dt
        self.state_dim = 9  # position, velocity, acceleration
        self.measurement_dim = 3  # position measurements
        
        # State vector: [x, y, z, vx, vy, vz, ax, ay, az]
        self.state = np.zeros(self.state_dim)
        
        # State covariance matrix
        self.P = np.eye(self.state_dim) * 1000.0
        
        # State transition matrix (constant acceleration model)
        self.F = np.eye(self.state_dim)
        # Position = position + velocity*dt + 0.5*acceleration*dt^2
        self.F[0:3, 3:6] = np.eye(3) * dt
        self.F[0:3, 6:9] = np.eye(3) * 0.5 * dt**2
        # Velocity = velocity + acceleration*dt
        self.F[3:6, 6:9] = np.eye(3) * dt
        
        # Process noise covariance
        self.Q = np.eye(self.state_dim) * 0.1
        
        # Measurement matrix (we observe position)
        self.H = np.zeros((self.measurement_dim, self.state_dim))
        self.H[0:3, 0:3] = np.eye(3)
        
        # Measurement noise covariance
        self.R = np.eye(self.measurement_dim) * 1.0
    
    def predict(self):
        """Prediction step of Kalman filter"""
        self.state, self.P = MathUtils.kalman_filter_predict(
            self.state, self.P, self.F, self.Q
        )
    
    def update(self, measurement: np.ndarray, measurement_covariance: Optional[np.ndarray] = None):
        """Update step of Kalman filter"""
        if measurement_covariance is not None:
            R = measurement_covariance
        else:
            R = self.R
        
        self.state, self.P = MathUtils.kalman_filter_update(
            self.state, self.P, measurement, self.H, R
        )
    
    def get_position(self) -> np.ndarray:
        """Get current position estimate"""
        return self.state[0:3]
    
    def get_velocity(self) -> np.ndarray:
        """Get current velocity estimate"""
        return self.state[3:6]
    
    def get_acceleration(self) -> np.ndarray:
        """Get current acceleration estimate"""
        return self.state[6:9]


class MultiSensorFusion(Node):
    """
    Advanced multi-sensor fusion system
    Combines LiDAR, radar, and camera data for robust object tracking
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__('multi_sensor_fusion')
        
        # Configuration
        self.config = config
        self.fusion_rate = config.get('fusion_rate', 20.0)  # Hz
        self.max_association_distance = config.get('max_association_distance', 5.0)  # meters
        self.track_timeout = config.get('track_timeout', 2.0)  # seconds
        self.min_sensor_agreement = config.get('min_sensor_agreement', 2)
        
        # Sensor weights for fusion
        self.sensor_weights = {
            SensorType.LIDAR: config.get('lidar_weight', 0.4),
            SensorType.RADAR: config.get('radar_weight', 0.3),
            SensorType.CAMERA: config.get('camera_weight', 0.3)
        }
        
        # Tracking state
        self.tracked_objects: Dict[int, FusedObject] = {}
        self.next_object_id = 0
        self.measurement_buffer: Dict[SensorType, queue.Queue] = {
            sensor_type: queue.Queue(maxsize=100) for sensor_type in SensorType
        }
        
        # Kalman filters for each tracked object
        self.kalman_filters: Dict[int, KalmanFilter] = {}
        
        # Threading for real-time processing
        self.fusion_thread = None
        self.is_running = False
        
        # Publishers
        self.fused_objects_pub = self.create_publisher(
            MarkerArray,
            'fused_objects',
            10
        )
        
        self.tracks_pub = self.create_publisher(
            MarkerArray,
            'object_tracks',
            10
        )
        
        # Subscribers for different sensor types
        self.setup_subscribers()
        
        # Start fusion thread
        self.start_fusion_thread()
        
        self.get_logger().info("Multi-sensor fusion initialized")
    
    def setup_subscribers(self):
        """Setup subscribers for different sensor data"""
        # These would subscribe to actual sensor topics
        # For demonstration, we'll create placeholder subscribers
        pass
    
    def add_measurement(self, measurement: SensorMeasurement):
        """Add a sensor measurement to the fusion system"""
        try:
            self.measurement_buffer[measurement.sensor_type].put_nowait(measurement)
        except queue.Full:
            self.get_logger().warn(f"Measurement buffer full for {measurement.sensor_type}")
    
    def start_fusion_thread(self):
        """Start the fusion processing thread"""
        self.is_running = True
        self.fusion_thread = threading.Thread(target=self._fusion_loop, daemon=True)
        self.fusion_thread.start()
    
    def stop_fusion_thread(self):
        """Stop the fusion processing thread"""
        self.is_running = False
        if self.fusion_thread and self.fusion_thread.is_alive():
            self.fusion_thread.join(timeout=1.0)
    
    def _fusion_loop(self):
        """Main fusion processing loop"""
        rate = 1.0 / self.fusion_rate
        
        while self.is_running:
            start_time = time.perf_counter()
            
            try:
                # Collect measurements from all sensors
                measurements = self._collect_measurements()
                
                # Predict all tracks
                self._predict_tracks()
                
                # Associate measurements with tracks
                associations = self._associate_measurements(measurements)
                
                # Update tracks with associated measurements
                self._update_tracks(associations)
                
                # Create new tracks for unassociated measurements
                self._create_new_tracks(associations, measurements)
                
                # Remove old tracks
                self._remove_old_tracks()
                
                # Publish results
                self._publish_results()
                
            except Exception as e:
                self.get_logger().error(f"Error in fusion loop: {e}")
            
            # Rate limiting
            processing_time = time.perf_counter() - start_time
            sleep_time = max(0, rate - processing_time)
            time.sleep(sleep_time)
    
    def _collect_measurements(self) -> List[SensorMeasurement]:
        """Collect measurements from all sensor buffers"""
        measurements = []
        current_time = time.time()
        
        for sensor_type, buffer in self.measurement_buffer.items():
            while not buffer.empty():
                try:
                    measurement = buffer.get_nowait()
                    
                    # Only process recent measurements
                    if current_time - measurement.timestamp < 0.5:
                        measurements.append(measurement)
                    
                except queue.Empty:
                    break
        
        return measurements
    
    def _predict_tracks(self):
        """Predict all tracked objects to current time"""
        for object_id, kalman_filter in self.kalman_filters.items():
            kalman_filter.predict()
            
            # Update tracked object with prediction
            if object_id in self.tracked_objects:
                obj = self.tracked_objects[object_id]
                obj.position = kalman_filter.get_position()
                obj.velocity = kalman_filter.get_velocity()
                obj.acceleration = kalman_filter.get_acceleration()
                obj.covariance = kalman_filter.P
    
    def _associate_measurements(self, measurements: List[SensorMeasurement]) -> Dict[int, List[SensorMeasurement]]:
        """Associate measurements with existing tracks using Hungarian algorithm"""
        if not measurements or not self.tracked_objects:
            return {}
        
        # Create cost matrix
        track_ids = list(self.tracked_objects.keys())
        measurement_positions = []
        track_positions = []
        
        for measurement in measurements:
            if measurement.position is not None:
                measurement_positions.append(measurement.position)
            else:
                measurement_positions.append(np.array([np.inf, np.inf, np.inf]))
        
        for track_id in track_ids:
            track_positions.append(self.tracked_objects[track_id].position)
        
        if not measurement_positions or not track_positions:
            return {}
        
        # Calculate distance matrix
        measurement_positions = np.array(measurement_positions)
        track_positions = np.array(track_positions)
        
        distance_matrix = cdist(measurement_positions, track_positions)
        
        # Apply distance threshold
        distance_matrix[distance_matrix > self.max_association_distance] = np.inf
        
        # Solve assignment problem
        measurement_indices, track_indices = linear_sum_assignment(distance_matrix)
        
        # Create associations
        associations = {}
        for m_idx, t_idx in zip(measurement_indices, track_indices):
            if distance_matrix[m_idx, t_idx] < self.max_association_distance:
                track_id = track_ids[t_idx]
                if track_id not in associations:
                    associations[track_id] = []
                associations[track_id].append(measurements[m_idx])
        
        return associations
    
    def _update_tracks(self, associations: Dict[int, List[SensorMeasurement]]):
        """Update tracks with associated measurements"""
        for track_id, measurements in associations.items():
            if track_id not in self.kalman_filters:
                continue
            
            kalman_filter = self.kalman_filters[track_id]
            tracked_object = self.tracked_objects[track_id]
            
            # Fuse multiple measurements
            fused_measurement, fused_covariance = self._fuse_measurements(measurements)
            
            if fused_measurement is not None:
                # Update Kalman filter
                kalman_filter.update(fused_measurement, fused_covariance)
                
                # Update tracked object
                tracked_object.position = kalman_filter.get_position()
                tracked_object.velocity = kalman_filter.get_velocity()
                tracked_object.acceleration = kalman_filter.get_acceleration()
                tracked_object.covariance = kalman_filter.P
                tracked_object.last_update = time.time()
                
                # Update sensor contributions
                for measurement in measurements:
                    sensor_type = measurement.sensor_type
                    weight = self.sensor_weights.get(sensor_type, 0.1)
                    tracked_object.sensor_contributions[sensor_type] = weight
                
                # Update track history
                tracked_object.track_history.append(tracked_object.position.copy())
                if len(tracked_object.track_history) > 50:  # Keep last 50 positions
                    tracked_object.track_history.pop(0)
    
    def _fuse_measurements(self, measurements: List[SensorMeasurement]) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """Fuse multiple measurements using weighted average"""
        if not measurements:
            return None, None
        
        valid_measurements = [m for m in measurements if m.position is not None]
        if not valid_measurements:
            return None, None
        
        # Calculate weights
        weights = []
        positions = []
        covariances = []
        
        for measurement in valid_measurements:
            weight = self.sensor_weights.get(measurement.sensor_type, 0.1)
            weight *= measurement.confidence
            
            weights.append(weight)
            positions.append(measurement.position)
            
            if measurement.covariance is not None:
                covariances.append(measurement.covariance)
            else:
                covariances.append(np.eye(3) * 1.0)  # Default covariance
        
        # Normalize weights
        weights = np.array(weights)
        weights = weights / np.sum(weights)
        
        # Weighted average of positions
        fused_position = np.average(positions, axis=0, weights=weights)
        
        # Weighted average of covariances
        fused_covariance = np.average(covariances, axis=0, weights=weights)
        
        return fused_position, fused_covariance
    
    def _create_new_tracks(self, associations: Dict[int, List[SensorMeasurement]], 
                          all_measurements: List[SensorMeasurement]):
        """Create new tracks for unassociated measurements"""
        # Find unassociated measurements
        associated_measurements = set()
        for measurements in associations.values():
            for measurement in measurements:
                associated_measurements.add(id(measurement))
        
        unassociated = [m for m in all_measurements if id(m) not in associated_measurements]
        
        # Group nearby unassociated measurements
        if len(unassociated) < self.min_sensor_agreement:
            return
        
        # Simple clustering of unassociated measurements
        clusters = self._cluster_measurements(unassociated)
        
        for cluster in clusters:
            if len(cluster) >= self.min_sensor_agreement:
                self._create_track_from_measurements(cluster)
    
    def _cluster_measurements(self, measurements: List[SensorMeasurement]) -> List[List[SensorMeasurement]]:
        """Cluster measurements that are close together"""
        if not measurements:
            return []
        
        clusters = []
        used = set()
        
        for i, measurement in enumerate(measurements):
            if i in used or measurement.position is None:
                continue
            
            cluster = [measurement]
            used.add(i)
            
            for j, other_measurement in enumerate(measurements):
                if j in used or j == i or other_measurement.position is None:
                    continue
                
                distance = np.linalg.norm(measurement.position - other_measurement.position)
                if distance < self.max_association_distance:
                    cluster.append(other_measurement)
                    used.add(j)
            
            clusters.append(cluster)
        
        return clusters
    
    def _create_track_from_measurements(self, measurements: List[SensorMeasurement]):
        """Create a new track from a cluster of measurements"""
        # Fuse measurements to get initial state
        fused_position, fused_covariance = self._fuse_measurements(measurements)
        
        if fused_position is None:
            return
        
        # Create new tracked object
        object_id = self.next_object_id
        self.next_object_id += 1
        
        tracked_object = FusedObject(
            object_id=object_id,
            position=fused_position,
            velocity=np.zeros(3),
            acceleration=np.zeros(3),
            covariance=fused_covariance if fused_covariance is not None else np.eye(9),
            last_update=time.time(),
            confidence=np.mean([m.confidence for m in measurements])
        )
        
        # Initialize Kalman filter
        kalman_filter = KalmanFilter()
        kalman_filter.state[0:3] = fused_position
        if fused_covariance is not None:
            kalman_filter.P[0:3, 0:3] = fused_covariance
        
        self.tracked_objects[object_id] = tracked_object
        self.kalman_filters[object_id] = kalman_filter
        
        self.get_logger().info(f"Created new track {object_id} at position {fused_position}")
    
    def _remove_old_tracks(self):
        """Remove tracks that haven't been updated recently"""
        current_time = time.time()
        to_remove = []
        
        for object_id, tracked_object in self.tracked_objects.items():
            if current_time - tracked_object.last_update > self.track_timeout:
                to_remove.append(object_id)
        
        for object_id in to_remove:
            del self.tracked_objects[object_id]
            if object_id in self.kalman_filters:
                del self.kalman_filters[object_id]
            self.get_logger().info(f"Removed old track {object_id}")
    
    def _publish_results(self):
        """Publish fused tracking results"""
        try:
            # Publish fused objects
            objects_marker_array = self._create_objects_markers()
            self.fused_objects_pub.publish(objects_marker_array)
            
            # Publish tracks
            tracks_marker_array = self._create_tracks_markers()
            self.tracks_pub.publish(tracks_marker_array)
            
        except Exception as e:
            self.get_logger().error(f"Error publishing results: {e}")
    
    def _create_objects_markers(self) -> MarkerArray:
        """Create visualization markers for fused objects"""
        marker_array = MarkerArray()
        
        for object_id, tracked_object in self.tracked_objects.items():
            marker = Marker()
            marker.header.frame_id = "map"
            marker.header.stamp = self.get_clock().now().to_msg()
            marker.ns = "fused_objects"
            marker.id = object_id
            marker.type = Marker.SPHERE
            marker.action = Marker.ADD
            
            # Position
            marker.pose.position.x = tracked_object.position[0]
            marker.pose.position.y = tracked_object.position[1]
            marker.pose.position.z = tracked_object.position[2]
            
            # Scale based on confidence
            scale = 0.5 + tracked_object.confidence * 0.5
            marker.scale.x = scale
            marker.scale.y = scale
            marker.scale.z = scale
            
            # Color based on sensor contributions
            if SensorType.LIDAR in tracked_object.sensor_contributions:
                marker.color.r = 1.0
            if SensorType.RADAR in tracked_object.sensor_contributions:
                marker.color.g = 1.0
            if SensorType.CAMERA in tracked_object.sensor_contributions:
                marker.color.b = 1.0
            marker.color.a = 0.8
            
            marker_array.markers.append(marker)
        
        return marker_array
    
    def _create_tracks_markers(self) -> MarkerArray:
        """Create visualization markers for object tracks"""
        marker_array = MarkerArray()
        
        for object_id, tracked_object in self.tracked_objects.items():
            if len(tracked_object.track_history) < 2:
                continue
            
            marker = Marker()
            marker.header.frame_id = "map"
            marker.header.stamp = self.get_clock().now().to_msg()
            marker.ns = "object_tracks"
            marker.id = object_id
            marker.type = Marker.LINE_STRIP
            marker.action = Marker.ADD
            
            marker.scale.x = 0.1  # Line width
            marker.color.r = 0.0
            marker.color.g = 1.0
            marker.color.b = 1.0
            marker.color.a = 0.6
            
            # Add track points
            for position in tracked_object.track_history:
                point = Point()
                point.x = position[0]
                point.y = position[1]
                point.z = position[2]
                marker.points.append(point)
            
            marker_array.markers.append(marker)
        
        return marker_array
    
    def get_tracked_objects(self) -> Dict[int, FusedObject]:
        """Get current tracked objects"""
        return self.tracked_objects.copy()
    
    def destroy_node(self):
        """Clean shutdown"""
        self.stop_fusion_thread()
        super().destroy_node()
