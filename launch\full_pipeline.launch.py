"""
Full Perception Pipeline Launch File
Launches all components of the perception system including:
- Sensor data generators
- Individual sensor processors
- Sensor fusion
- Communication protocols
- Visualization
"""

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, ExecuteProcess, TimerAction
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
import os


def generate_launch_description():
    """Generate launch description for full perception pipeline"""
    
    # Declare launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='Use simulation time'
    )
    
    enable_rviz_arg = DeclareLaunchArgument(
        'enable_rviz',
        default_value='true',
        description='Enable RViz visualization'
    )
    
    enable_synthetic_data_arg = DeclareLaunchArgument(
        'enable_synthetic_data',
        default_value='true',
        description='Enable synthetic sensor data generation'
    )
    
    config_file_arg = DeclareLaunchArgument(
        'config_file',
        default_value=PathJoinSubstitution([
            FindPackageShare('perception_engineering'),
            'config',
            'perception_system.yaml'
        ]),
        description='Path to configuration file'
    )
    
    # Get launch configurations
    use_sim_time = LaunchConfiguration('use_sim_time')
    enable_rviz = LaunchConfiguration('enable_rviz')
    enable_synthetic_data = LaunchConfiguration('enable_synthetic_data')
    config_file = LaunchConfiguration('config_file')
    
    # Sensor data generator (synthetic data for testing)
    sensor_data_generator = Node(
        package='sensor_processing',
        executable='sensor_data_generator.py',
        name='sensor_data_generator',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time,
        }],
        condition=lambda context: context.perform_substitution(enable_synthetic_data) == 'true'
    )
    
    # LiDAR processor
    lidar_processor = Node(
        package='sensor_processing',
        executable='lidar_processor.py',
        name='lidar_processor',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time,
        }],
        remappings=[
            ('raw_pointcloud', '/raw_pointcloud'),
            ('processed_pointcloud', '/lidar/processed_pointcloud'),
            ('detected_objects', '/lidar/detected_objects'),
        ]
    )
    
    # Radar processor
    radar_processor = Node(
        package='sensor_processing',
        executable='radar_processor.py',
        name='radar_processor',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time,
        }],
        remappings=[
            ('raw_radar_data', '/raw_radar_data'),
            ('radar_targets', '/radar/targets'),
            ('range_doppler_map', '/radar/range_doppler_map'),
        ]
    )
    
    # Camera processor
    camera_processor = Node(
        package='sensor_processing',
        executable='camera_processor.py',
        name='camera_processor',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time,
        }],
        remappings=[
            ('camera/image_raw', '/camera/image_raw'),
            ('processed_image', '/camera/processed_image'),
            ('camera_objects', '/camera/objects'),
            ('visual_features', '/camera/features'),
        ]
    )
    
    # Multi-sensor fusion
    sensor_fusion = Node(
        package='sensor_fusion',
        executable='multi_sensor_fusion.py',
        name='multi_sensor_fusion',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time,
        }],
        remappings=[
            ('fused_objects', '/fusion/objects'),
            ('object_tracks', '/fusion/tracks'),
        ]
    )
    
    # Object tracking
    object_tracker = Node(
        package='tracking',
        executable='kalman_tracker.py',
        name='object_tracker',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time,
        }],
        remappings=[
            ('tracked_objects', '/tracking/objects'),
            ('predicted_trajectories', '/tracking/trajectories'),
        ]
    )
    
    # Communication manager
    communication_manager = Node(
        package='communication',
        executable='communication_manager.py',
        name='communication_manager',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time,
        }]
    )
    
    # Performance monitor
    performance_monitor = Node(
        package='monitoring',
        executable='performance_monitor.py',
        name='performance_monitor',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time,
        }]
    )
    
    # Static transform publishers for sensor frames
    static_transforms = [
        # LiDAR to base_link
        Node(
            package='tf2_ros',
            executable='static_transform_publisher',
            name='lidar_to_base_tf',
            arguments=['0', '0', '2.0', '0', '0', '0', 'base_link', 'lidar_frame']
        ),
        
        # Radar to base_link
        Node(
            package='tf2_ros',
            executable='static_transform_publisher',
            name='radar_to_base_tf',
            arguments=['3.0', '0', '1.0', '0', '0', '0', 'base_link', 'radar_frame']
        ),
        
        # Camera to base_link
        Node(
            package='tf2_ros',
            executable='static_transform_publisher',
            name='camera_to_base_tf',
            arguments=['2.0', '0', '1.5', '0', '0', '0', 'base_link', 'camera_frame']
        ),
        
        # Map to base_link (for visualization)
        Node(
            package='tf2_ros',
            executable='static_transform_publisher',
            name='map_to_base_tf',
            arguments=['0', '0', '0', '0', '0', '0', 'map', 'base_link']
        ),
    ]
    
    # RViz for visualization
    rviz_config_file = PathJoinSubstitution([
        FindPackageShare('perception_engineering'),
        'rviz',
        'perception_pipeline.rviz'
    ])
    
    rviz_node = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2',
        arguments=['-d', rviz_config_file],
        output='screen',
        condition=lambda context: context.perform_substitution(enable_rviz) == 'true'
    )
    
    # Delayed start for some nodes to ensure proper initialization order
    delayed_nodes = [
        TimerAction(
            period=2.0,
            actions=[sensor_fusion]
        ),
        TimerAction(
            period=3.0,
            actions=[object_tracker]
        ),
        TimerAction(
            period=4.0,
            actions=[communication_manager]
        ),
        TimerAction(
            period=5.0,
            actions=[rviz_node]
        ),
    ]
    
    # Create launch description
    launch_description = LaunchDescription([
        # Launch arguments
        use_sim_time_arg,
        enable_rviz_arg,
        enable_synthetic_data_arg,
        config_file_arg,
        
        # Immediate start nodes
        sensor_data_generator,
        lidar_processor,
        radar_processor,
        camera_processor,
        performance_monitor,
        
        # Static transforms
        *static_transforms,
        
        # Delayed start nodes
        *delayed_nodes,
    ])
    
    return launch_description


# Additional utility functions for launch file
def get_package_share_directory(package_name):
    """Get the share directory of a package"""
    try:
        from ament_index_python.packages import get_package_share_directory as get_pkg_share
        return get_pkg_share(package_name)
    except ImportError:
        # Fallback for development
        return os.path.join(os.getcwd(), 'install', package_name, 'share', package_name)


def create_parameter_file_path(package_name, relative_path):
    """Create full path to parameter file"""
    package_share = get_package_share_directory(package_name)
    return os.path.join(package_share, relative_path)


# Example usage and testing functions
if __name__ == '__main__':
    """
    This section can be used for testing the launch file configuration
    """
    print("Perception Engineering Launch File")
    print("==================================")
    print()
    print("This launch file starts the complete perception pipeline including:")
    print("- Sensor data generation (synthetic)")
    print("- LiDAR processing with object detection")
    print("- Radar processing with CFAR detection")
    print("- Camera processing with computer vision")
    print("- Multi-sensor fusion")
    print("- Object tracking")
    print("- Communication protocols")
    print("- Performance monitoring")
    print("- Visualization in RViz")
    print()
    print("Usage:")
    print("ros2 launch perception_engineering full_pipeline.launch.py")
    print()
    print("Optional arguments:")
    print("- use_sim_time:=true/false")
    print("- enable_rviz:=true/false")
    print("- enable_synthetic_data:=true/false")
    print("- config_file:=/path/to/config.yaml")
    print()
    print("Example with arguments:")
    print("ros2 launch perception_engineering full_pipeline.launch.py use_sim_time:=true enable_rviz:=true")
