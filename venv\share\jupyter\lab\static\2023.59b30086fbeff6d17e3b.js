"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[2023],{27574:(e,t,s)=>{s.d(t,{A:()=>a});var i=s(57991);var r=s(63221);const n=(e,t)=>i.A.lang.round(r.A.parse(e)[t]);const a=n},15051:(e,t,s)=>{s.d(t,{A:()=>n,P:()=>a});var i=s(75905);var r=s(24982);var n=(0,i.K2)(((e,t)=>{let s;if(t==="sandbox"){s=(0,r.Ltv)("#i"+e)}const i=t==="sandbox"?(0,r.Ltv)(s.nodes()[0].contentDocument.body):(0,r.Ltv)("body");const n=i.select(`[id="${e}"]`);return n}),"getDiagramElement");var a=(0,i.K2)(((e,t,s,r)=>{e.attr("class",s);const{width:n,height:a,x:l,y:c}=u(e,t);(0,i.a$)(e,a,n,r);const h=o(l,c,n,a,t);e.attr("viewBox",h);i.Rm.debug(`viewBox configured: ${h} with padding: ${t}`)}),"setupViewPortForSVG");var u=(0,i.K2)(((e,t)=>{const s=e.node()?.getBBox()||{width:0,height:0,x:0,y:0};return{width:s.width+t*2,height:s.height+t*2,x:s.x,y:s.y}}),"calculateDimensionsWithPadding");var o=(0,i.K2)(((e,t,s,i,r)=>`${e-r} ${t-r} ${s} ${i}`),"createViewBox")},52023:(e,t,s)=>{s.d(t,{diagram:()=>_});var i=s(97366);var r=s(15051);var n=s(94065);var a=s(33416);var u=s(94746);var o=s(20778);var l=s(57590);var c=s(68232);var h=s(76261);var d=s(96049);var p=s(75905);var f=s(24982);var b=s(27574);var k=s(3635);var g="flowchart-";var A=class{constructor(){this.vertexCounter=0;this.config=(0,p.D7)();this.vertices=new Map;this.edges=[];this.classes=new Map;this.subGraphs=[];this.subGraphLookup=new Map;this.tooltips=new Map;this.subCount=0;this.firstGraphFlag=true;this.secCount=-1;this.posCrossRef=[];this.funs=[];this.setAccTitle=p.SV;this.setAccDescription=p.EI;this.setDiagramTitle=p.ke;this.getAccTitle=p.iN;this.getAccDescription=p.m7;this.getDiagramTitle=p.ab;this.funs.push(this.setupToolTips.bind(this));this.addVertex=this.addVertex.bind(this);this.firstGraph=this.firstGraph.bind(this);this.setDirection=this.setDirection.bind(this);this.addSubGraph=this.addSubGraph.bind(this);this.addLink=this.addLink.bind(this);this.setLink=this.setLink.bind(this);this.updateLink=this.updateLink.bind(this);this.addClass=this.addClass.bind(this);this.setClass=this.setClass.bind(this);this.destructLink=this.destructLink.bind(this);this.setClickEvent=this.setClickEvent.bind(this);this.setTooltip=this.setTooltip.bind(this);this.updateLinkInterpolate=this.updateLinkInterpolate.bind(this);this.setClickFun=this.setClickFun.bind(this);this.bindFunctions=this.bindFunctions.bind(this);this.lex={firstGraph:this.firstGraph.bind(this)};this.clear();this.setGen("gen-2")}static{(0,p.K2)(this,"FlowDB")}sanitizeText(e){return p.Y2.sanitizeText(e,this.config)}lookUpDomId(e){for(const t of this.vertices.values()){if(t.id===e){return t.domId}}return e}addVertex(e,t,s,r,n,a,u={},l){if(!e||e.trim().length===0){return}let c;if(l!==void 0){let e;if(!l.includes("\n")){e="{\n"+l+"\n}"}else{e=l+"\n"}c=(0,i.H)(e,{schema:i.r})}const h=this.edges.find((t=>t.id===e));if(h){const e=c;if(e?.animate!==void 0){h.animate=e.animate}if(e?.animation!==void 0){h.animation=e.animation}return}let d;let f=this.vertices.get(e);if(f===void 0){f={id:e,labelType:"text",domId:g+e+"-"+this.vertexCounter,styles:[],classes:[]};this.vertices.set(e,f)}this.vertexCounter++;if(t!==void 0){this.config=(0,p.D7)();d=this.sanitizeText(t.text.trim());f.labelType=t.type;if(d.startsWith('"')&&d.endsWith('"')){d=d.substring(1,d.length-1)}f.text=d}else{if(f.text===void 0){f.text=e}}if(s!==void 0){f.type=s}if(r!==void 0&&r!==null){r.forEach((e=>{f.styles.push(e)}))}if(n!==void 0&&n!==null){n.forEach((e=>{f.classes.push(e)}))}if(a!==void 0){f.dir=a}if(f.props===void 0){f.props=u}else if(u!==void 0){Object.assign(f.props,u)}if(c!==void 0){if(c.shape){if(c.shape!==c.shape.toLowerCase()||c.shape.includes("_")){throw new Error(`No such shape: ${c.shape}. Shape names should be lowercase.`)}else if(!(0,o.aP)(c.shape)){throw new Error(`No such shape: ${c.shape}.`)}f.type=c?.shape}if(c?.label){f.text=c?.label}if(c?.icon){f.icon=c?.icon;if(!c.label?.trim()&&f.text===e){f.text=""}}if(c?.form){f.form=c?.form}if(c?.pos){f.pos=c?.pos}if(c?.img){f.img=c?.img;if(!c.label?.trim()&&f.text===e){f.text=""}}if(c?.constraint){f.constraint=c.constraint}if(c.w){f.assetWidth=Number(c.w)}if(c.h){f.assetHeight=Number(c.h)}}}addSingleLink(e,t,s,i){const r=e;const n=t;const a={start:r,end:n,type:void 0,text:"",labelType:"text",classes:[],isUserDefinedId:false,interpolate:this.edges.defaultInterpolate};p.Rm.info("abc78 Got edge...",a);const u=s.text;if(u!==void 0){a.text=this.sanitizeText(u.text.trim());if(a.text.startsWith('"')&&a.text.endsWith('"')){a.text=a.text.substring(1,a.text.length-1)}a.labelType=u.type}if(s!==void 0){a.type=s.type;a.stroke=s.stroke;a.length=s.length>10?10:s.length}if(i&&!this.edges.some((e=>e.id===i))){a.id=i;a.isUserDefinedId=true}else{const e=this.edges.filter((e=>e.start===a.start&&e.end===a.end));if(e.length===0){a.id=(0,d.rY)(a.start,a.end,{counter:0,prefix:"L"})}else{a.id=(0,d.rY)(a.start,a.end,{counter:e.length+1,prefix:"L"})}}if(this.edges.length<(this.config.maxEdges??500)){p.Rm.info("Pushing edge...");this.edges.push(a)}else{throw new Error(`Edge limit exceeded. ${this.edges.length} edges found, but the limit is ${this.config.maxEdges}.\n\nInitialize mermaid with maxEdges set to a higher number to allow more edges.\nYou cannot set this config via configuration inside the diagram as it is a secure config.\nYou have to call mermaid.initialize.`)}}isLinkData(e){return e!==null&&typeof e==="object"&&"id"in e&&typeof e.id==="string"}addLink(e,t,s){const i=this.isLinkData(s)?s.id.replace("@",""):void 0;p.Rm.info("addLink",e,t,i);for(const r of e){for(const n of t){const a=r===e[e.length-1];const u=n===t[0];if(a&&u){this.addSingleLink(r,n,s,i)}else{this.addSingleLink(r,n,s,void 0)}}}}updateLinkInterpolate(e,t){e.forEach((e=>{if(e==="default"){this.edges.defaultInterpolate=t}else{this.edges[e].interpolate=t}}))}updateLink(e,t){e.forEach((e=>{if(typeof e==="number"&&e>=this.edges.length){throw new Error(`The index ${e} for linkStyle is out of bounds. Valid indices for linkStyle are between 0 and ${this.edges.length-1}. (Help: Ensure that the index is within the range of existing edges.)`)}if(e==="default"){this.edges.defaultStyle=t}else{this.edges[e].style=t;if((this.edges[e]?.style?.length??0)>0&&!this.edges[e]?.style?.some((e=>e?.startsWith("fill")))){this.edges[e]?.style?.push("fill:none")}}}))}addClass(e,t){const s=t.join().replace(/\\,/g,"§§§").replace(/,/g,";").replace(/§§§/g,",").split(";");e.split(",").forEach((e=>{let t=this.classes.get(e);if(t===void 0){t={id:e,styles:[],textStyles:[]};this.classes.set(e,t)}if(s!==void 0&&s!==null){s.forEach((e=>{if(/color/.exec(e)){const s=e.replace("fill","bgFill");t.textStyles.push(s)}t.styles.push(e)}))}}))}setDirection(e){this.direction=e;if(/.*</.exec(this.direction)){this.direction="RL"}if(/.*\^/.exec(this.direction)){this.direction="BT"}if(/.*>/.exec(this.direction)){this.direction="LR"}if(/.*v/.exec(this.direction)){this.direction="TB"}if(this.direction==="TD"){this.direction="TB"}}setClass(e,t){for(const s of e.split(",")){const e=this.vertices.get(s);if(e){e.classes.push(t)}const i=this.edges.find((e=>e.id===s));if(i){i.classes.push(t)}const r=this.subGraphLookup.get(s);if(r){r.classes.push(t)}}}setTooltip(e,t){if(t===void 0){return}t=this.sanitizeText(t);for(const s of e.split(",")){this.tooltips.set(this.version==="gen-1"?this.lookUpDomId(s):s,t)}}setClickFun(e,t,s){const i=this.lookUpDomId(e);if((0,p.D7)().securityLevel!=="loose"){return}if(t===void 0){return}let r=[];if(typeof s==="string"){r=s.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let e=0;e<r.length;e++){let t=r[e].trim();if(t.startsWith('"')&&t.endsWith('"')){t=t.substr(1,t.length-2)}r[e]=t}}if(r.length===0){r.push(e)}const n=this.vertices.get(e);if(n){n.haveCallback=true;this.funs.push((()=>{const e=document.querySelector(`[id="${i}"]`);if(e!==null){e.addEventListener("click",(()=>{d._K.runFunc(t,...r)}),false)}}))}}setLink(e,t,s){e.split(",").forEach((e=>{const i=this.vertices.get(e);if(i!==void 0){i.link=d._K.formatUrl(t,this.config);i.linkTarget=s}}));this.setClass(e,"clickable")}getTooltip(e){return this.tooltips.get(e)}setClickEvent(e,t,s){e.split(",").forEach((e=>{this.setClickFun(e,t,s)}));this.setClass(e,"clickable")}bindFunctions(e){this.funs.forEach((t=>{t(e)}))}getDirection(){return this.direction?.trim()}getVertices(){return this.vertices}getEdges(){return this.edges}getClasses(){return this.classes}setupToolTips(e){let t=(0,f.Ltv)(".mermaidTooltip");if((t._groups||t)[0][0]===null){t=(0,f.Ltv)("body").append("div").attr("class","mermaidTooltip").style("opacity",0)}const s=(0,f.Ltv)(e).select("svg");const i=s.selectAll("g.node");i.on("mouseover",(e=>{const s=(0,f.Ltv)(e.currentTarget);const i=s.attr("title");if(i===null){return}const r=e.currentTarget?.getBoundingClientRect();t.transition().duration(200).style("opacity",".9");t.text(s.attr("title")).style("left",window.scrollX+r.left+(r.right-r.left)/2+"px").style("top",window.scrollY+r.bottom+"px");t.html(t.html().replace(/&lt;br\/&gt;/g,"<br/>"));s.classed("hover",true)})).on("mouseout",(e=>{t.transition().duration(500).style("opacity",0);const s=(0,f.Ltv)(e.currentTarget);s.classed("hover",false)}))}clear(e="gen-2"){this.vertices=new Map;this.classes=new Map;this.edges=[];this.funs=[this.setupToolTips.bind(this)];this.subGraphs=[];this.subGraphLookup=new Map;this.subCount=0;this.tooltips=new Map;this.firstGraphFlag=true;this.version=e;this.config=(0,p.D7)();(0,p.IU)()}setGen(e){this.version=e||"gen-2"}defaultStyle(){return"fill:#ffa;stroke: #f66; stroke-width: 3px; stroke-dasharray: 5, 5;fill:#ffa;stroke: #666;"}addSubGraph(e,t,s){let i=e.text.trim();let r=s.text;if(e===s&&/\s/.exec(s.text)){i=void 0}const n=(0,p.K2)((e=>{const t={boolean:{},number:{},string:{}};const s=[];let i;const r=e.filter((function(e){const r=typeof e;if(e.stmt&&e.stmt==="dir"){i=e.value;return false}if(e.trim()===""){return false}if(r in t){return t[r].hasOwnProperty(e)?false:t[r][e]=true}else{return s.includes(e)?false:s.push(e)}}));return{nodeList:r,dir:i}}),"uniq");const{nodeList:a,dir:u}=n(t.flat());if(this.version==="gen-1"){for(let e=0;e<a.length;e++){a[e]=this.lookUpDomId(a[e])}}i=i??"subGraph"+this.subCount;r=r||"";r=this.sanitizeText(r);this.subCount=this.subCount+1;const o={id:i,nodes:a,title:r.trim(),classes:[],dir:u,labelType:s.type};p.Rm.info("Adding",o.id,o.nodes,o.dir);o.nodes=this.makeUniq(o,this.subGraphs).nodes;this.subGraphs.push(o);this.subGraphLookup.set(i,o);return i}getPosForId(e){for(const[t,s]of this.subGraphs.entries()){if(s.id===e){return t}}return-1}indexNodes2(e,t){const s=this.subGraphs[t].nodes;this.secCount=this.secCount+1;if(this.secCount>2e3){return{result:false,count:0}}this.posCrossRef[this.secCount]=t;if(this.subGraphs[t].id===e){return{result:true,count:0}}let i=0;let r=1;while(i<s.length){const t=this.getPosForId(s[i]);if(t>=0){const s=this.indexNodes2(e,t);if(s.result){return{result:true,count:r+s.count}}else{r=r+s.count}}i=i+1}return{result:false,count:r}}getDepthFirstPos(e){return this.posCrossRef[e]}indexNodes(){this.secCount=-1;if(this.subGraphs.length>0){this.indexNodes2("none",this.subGraphs.length-1)}}getSubGraphs(){return this.subGraphs}firstGraph(){if(this.firstGraphFlag){this.firstGraphFlag=false;return true}return false}destructStartLink(e){let t=e.trim();let s="arrow_open";switch(t[0]){case"<":s="arrow_point";t=t.slice(1);break;case"x":s="arrow_cross";t=t.slice(1);break;case"o":s="arrow_circle";t=t.slice(1);break}let i="normal";if(t.includes("=")){i="thick"}if(t.includes(".")){i="dotted"}return{type:s,stroke:i}}countChar(e,t){const s=t.length;let i=0;for(let r=0;r<s;++r){if(t[r]===e){++i}}return i}destructEndLink(e){const t=e.trim();let s=t.slice(0,-1);let i="arrow_open";switch(t.slice(-1)){case"x":i="arrow_cross";if(t.startsWith("x")){i="double_"+i;s=s.slice(1)}break;case">":i="arrow_point";if(t.startsWith("<")){i="double_"+i;s=s.slice(1)}break;case"o":i="arrow_circle";if(t.startsWith("o")){i="double_"+i;s=s.slice(1)}break}let r="normal";let n=s.length-1;if(s.startsWith("=")){r="thick"}if(s.startsWith("~")){r="invisible"}const a=this.countChar(".",s);if(a){r="dotted";n=a}return{type:i,stroke:r,length:n}}destructLink(e,t){const s=this.destructEndLink(e);let i;if(t){i=this.destructStartLink(t);if(i.stroke!==s.stroke){return{type:"INVALID",stroke:"INVALID"}}if(i.type==="arrow_open"){i.type=s.type}else{if(i.type!==s.type){return{type:"INVALID",stroke:"INVALID"}}i.type="double_"+i.type}if(i.type==="double_arrow"){i.type="double_arrow_point"}i.length=s.length;return i}return s}exists(e,t){for(const s of e){if(s.nodes.includes(t)){return true}}return false}makeUniq(e,t){const s=[];e.nodes.forEach(((i,r)=>{if(!this.exists(t,i)){s.push(e.nodes[r])}}));return{nodes:s}}getTypeFromVertex(e){if(e.img){return"imageSquare"}if(e.icon){if(e.form==="circle"){return"iconCircle"}if(e.form==="square"){return"iconSquare"}if(e.form==="rounded"){return"iconRounded"}return"icon"}switch(e.type){case"square":case void 0:return"squareRect";case"round":return"roundedRect";case"ellipse":return"ellipse";default:return e.type}}findNode(e,t){return e.find((e=>e.id===t))}destructEdgeType(e){let t="none";let s="arrow_point";switch(e){case"arrow_point":case"arrow_circle":case"arrow_cross":s=e;break;case"double_arrow_point":case"double_arrow_circle":case"double_arrow_cross":t=e.replace("double_","");s=t;break}return{arrowTypeStart:t,arrowTypeEnd:s}}addNodeFromVertex(e,t,s,i,r,n){const a=s.get(e.id);const u=i.get(e.id)??false;const o=this.findNode(t,e.id);if(o){o.cssStyles=e.styles;o.cssCompiledStyles=this.getCompiledStyles(e.classes);o.cssClasses=e.classes.join(" ")}else{const s={id:e.id,label:e.text,labelStyle:"",parentId:a,padding:r.flowchart?.padding||8,cssStyles:e.styles,cssCompiledStyles:this.getCompiledStyles(["default","node",...e.classes]),cssClasses:"default "+e.classes.join(" "),dir:e.dir,domId:e.domId,look:n,link:e.link,linkTarget:e.linkTarget,tooltip:this.getTooltip(e.id),icon:e.icon,pos:e.pos,img:e.img,assetWidth:e.assetWidth,assetHeight:e.assetHeight,constraint:e.constraint};if(u){t.push({...s,isGroup:true,shape:"rect"})}else{t.push({...s,isGroup:false,shape:this.getTypeFromVertex(e)})}}}getCompiledStyles(e){let t=[];for(const s of e){const e=this.classes.get(s);if(e?.styles){t=[...t,...e.styles??[]].map((e=>e.trim()))}if(e?.textStyles){t=[...t,...e.textStyles??[]].map((e=>e.trim()))}}return t}getData(){const e=(0,p.D7)();const t=[];const s=[];const i=this.getSubGraphs();const r=new Map;const n=new Map;for(let o=i.length-1;o>=0;o--){const e=i[o];if(e.nodes.length>0){n.set(e.id,true)}for(const t of e.nodes){r.set(t,e.id)}}for(let o=i.length-1;o>=0;o--){const s=i[o];t.push({id:s.id,label:s.title,labelStyle:"",parentId:r.get(s.id),padding:8,cssCompiledStyles:this.getCompiledStyles(s.classes),cssClasses:s.classes.join(" "),shape:"rect",dir:s.dir,isGroup:true,look:e.look})}const a=this.getVertices();a.forEach((s=>{this.addNodeFromVertex(s,t,r,n,e,e.look||"classic")}));const u=this.getEdges();u.forEach(((t,i)=>{const{arrowTypeStart:r,arrowTypeEnd:n}=this.destructEdgeType(t.type);const a=[...u.defaultStyle??[]];if(t.style){a.push(...t.style)}const o={id:(0,d.rY)(t.start,t.end,{counter:i,prefix:"L"},t.id),isUserDefinedId:t.isUserDefinedId,start:t.start,end:t.end,type:t.type??"normal",label:t.text,labelpos:"c",thickness:t.stroke,minlen:t.length,classes:t?.stroke==="invisible"?"":"edge-thickness-normal edge-pattern-solid flowchart-link",arrowTypeStart:t?.stroke==="invisible"||t?.type==="arrow_open"?"none":r,arrowTypeEnd:t?.stroke==="invisible"||t?.type==="arrow_open"?"none":n,arrowheadStyle:"fill: #333",cssCompiledStyles:this.getCompiledStyles(t.classes),labelStyle:a,style:a,pattern:t.stroke,look:e.look,animate:t.animate,animation:t.animation,curve:t.interpolate||this.edges.defaultInterpolate||e.flowchart?.curve};s.push(o)}));return{nodes:t,edges:s,other:{},config:e}}defaultConfig(){return p.ME.flowchart}};var y=(0,p.K2)((function(e,t){return t.db.getClasses()}),"getClasses");var m=(0,p.K2)((async function(e,t,s,i){p.Rm.info("REF0:");p.Rm.info("Drawing state diagram (v2)",t);const{securityLevel:a,flowchart:u,layout:o}=(0,p.D7)();let l;if(a==="sandbox"){l=(0,f.Ltv)("#i"+t)}const c=a==="sandbox"?l.nodes()[0].contentDocument:document;p.Rm.debug("Before getData: ");const h=i.db.getData();p.Rm.debug("Data: ",h);const b=(0,r.A)(t,a);const k=i.db.getDirection();h.type=i.type;h.layoutAlgorithm=(0,n.q7)(o);if(h.layoutAlgorithm==="dagre"&&o==="elk"){p.Rm.warn("flowchart-elk was moved to an external package in Mermaid v11. Please refer [release notes](https://github.com/mermaid-js/mermaid/releases/tag/v11.0.0) for more details. This diagram will be rendered using `dagre` layout as a fallback.")}h.direction=k;h.nodeSpacing=u?.nodeSpacing||50;h.rankSpacing=u?.rankSpacing||50;h.markers=["point","circle","cross"];h.diagramId=t;p.Rm.debug("REF1:",h);await(0,n.XX)(h,b);const g=h.config.flowchart?.diagramPadding??8;d._K.insertTitle(b,"flowchartTitleText",u?.titleTopMargin||0,i.db.getDiagramTitle());(0,r.P)(b,g,"flowchart",u?.useMaxWidth||false);for(const r of h.nodes){const e=(0,f.Ltv)(`#${t} [id="${r.id}"]`);if(!e||!r.link){continue}const s=c.createElementNS("http://www.w3.org/2000/svg","a");s.setAttributeNS("http://www.w3.org/2000/svg","class",r.cssClasses);s.setAttributeNS("http://www.w3.org/2000/svg","rel","noopener");if(a==="sandbox"){s.setAttributeNS("http://www.w3.org/2000/svg","target","_top")}else if(r.linkTarget){s.setAttributeNS("http://www.w3.org/2000/svg","target",r.linkTarget)}const i=e.insert((function(){return s}),":first-child");const n=e.select(".label-container");if(n){i.append((function(){return n.node()}))}const u=e.select(".label");if(u){i.append((function(){return u.node()}))}}}),"draw");var E={getClasses:y,draw:m};var x=function(){var e=(0,p.K2)((function(e,t,s,i){for(s=s||{},i=e.length;i--;s[e[i]]=t);return s}),"o"),t=[1,4],s=[1,3],i=[1,5],r=[1,8,9,10,11,27,34,36,38,44,60,84,85,86,87,88,89,102,105,106,109,111,114,115,116,121,122,123,124],n=[2,2],a=[1,13],u=[1,14],o=[1,15],l=[1,16],c=[1,23],h=[1,25],d=[1,26],f=[1,27],b=[1,49],k=[1,48],g=[1,29],A=[1,30],y=[1,31],m=[1,32],E=[1,33],x=[1,44],C=[1,46],D=[1,42],S=[1,47],T=[1,43],v=[1,50],F=[1,45],_=[1,51],B=[1,52],w=[1,34],L=[1,35],$=[1,36],I=[1,37],R=[1,57],N=[1,8,9,10,11,27,32,34,36,38,44,60,84,85,86,87,88,89,102,105,106,109,111,114,115,116,121,122,123,124],P=[1,61],G=[1,60],K=[1,62],O=[8,9,11,75,77,78],V=[1,78],M=[1,91],U=[1,96],W=[1,95],j=[1,92],Y=[1,88],z=[1,94],X=[1,90],H=[1,97],q=[1,93],Q=[1,98],Z=[1,89],J=[8,9,10,11,40,75,77,78],ee=[8,9,10,11,40,46,75,77,78],te=[8,9,10,11,29,40,44,46,48,50,52,54,56,58,60,63,65,67,68,70,75,77,78,89,102,105,106,109,111,114,115,116],se=[8,9,11,44,60,75,77,78,89,102,105,106,109,111,114,115,116],ie=[44,60,89,102,105,106,109,111,114,115,116],re=[1,121],ne=[1,122],ae=[1,124],ue=[1,123],oe=[44,60,62,74,89,102,105,106,109,111,114,115,116],le=[1,133],ce=[1,147],he=[1,148],de=[1,149],pe=[1,150],fe=[1,135],be=[1,137],ke=[1,141],ge=[1,142],Ae=[1,143],ye=[1,144],me=[1,145],Ee=[1,146],xe=[1,151],Ce=[1,152],De=[1,131],Se=[1,132],Te=[1,139],ve=[1,134],Fe=[1,138],_e=[1,136],Be=[8,9,10,11,27,32,34,36,38,44,60,84,85,86,87,88,89,102,105,106,109,111,114,115,116,121,122,123,124],we=[1,154],Le=[1,156],$e=[8,9,11],Ie=[8,9,10,11,14,44,60,89,105,106,109,111,114,115,116],Re=[1,176],Ne=[1,172],Pe=[1,173],Ge=[1,177],Ke=[1,174],Oe=[1,175],Ve=[77,116,119],Me=[8,9,10,11,12,14,27,29,32,44,60,75,84,85,86,87,88,89,90,105,109,111,114,115,116],Ue=[10,106],We=[31,49,51,53,55,57,62,64,66,67,69,71,116,117,118],je=[1,247],Ye=[1,245],ze=[1,249],Xe=[1,243],He=[1,244],qe=[1,246],Qe=[1,248],Ze=[1,250],Je=[1,268],et=[8,9,11,106],tt=[8,9,10,11,60,84,105,106,109,110,111,112];var st={trace:(0,p.K2)((function e(){}),"trace"),yy:{},symbols_:{error:2,start:3,graphConfig:4,document:5,line:6,statement:7,SEMI:8,NEWLINE:9,SPACE:10,EOF:11,GRAPH:12,NODIR:13,DIR:14,FirstStmtSeparator:15,ending:16,endToken:17,spaceList:18,spaceListNewline:19,vertexStatement:20,separator:21,styleStatement:22,linkStyleStatement:23,classDefStatement:24,classStatement:25,clickStatement:26,subgraph:27,textNoTags:28,SQS:29,text:30,SQE:31,end:32,direction:33,acc_title:34,acc_title_value:35,acc_descr:36,acc_descr_value:37,acc_descr_multiline_value:38,shapeData:39,SHAPE_DATA:40,link:41,node:42,styledVertex:43,AMP:44,vertex:45,STYLE_SEPARATOR:46,idString:47,DOUBLECIRCLESTART:48,DOUBLECIRCLEEND:49,PS:50,PE:51,"(-":52,"-)":53,STADIUMSTART:54,STADIUMEND:55,SUBROUTINESTART:56,SUBROUTINEEND:57,VERTEX_WITH_PROPS_START:58,"NODE_STRING[field]":59,COLON:60,"NODE_STRING[value]":61,PIPE:62,CYLINDERSTART:63,CYLINDEREND:64,DIAMOND_START:65,DIAMOND_STOP:66,TAGEND:67,TRAPSTART:68,TRAPEND:69,INVTRAPSTART:70,INVTRAPEND:71,linkStatement:72,arrowText:73,TESTSTR:74,START_LINK:75,edgeText:76,LINK:77,LINK_ID:78,edgeTextToken:79,STR:80,MD_STR:81,textToken:82,keywords:83,STYLE:84,LINKSTYLE:85,CLASSDEF:86,CLASS:87,CLICK:88,DOWN:89,UP:90,textNoTagsToken:91,stylesOpt:92,"idString[vertex]":93,"idString[class]":94,CALLBACKNAME:95,CALLBACKARGS:96,HREF:97,LINK_TARGET:98,"STR[link]":99,"STR[tooltip]":100,alphaNum:101,DEFAULT:102,numList:103,INTERPOLATE:104,NUM:105,COMMA:106,style:107,styleComponent:108,NODE_STRING:109,UNIT:110,BRKT:111,PCT:112,idStringToken:113,MINUS:114,MULT:115,UNICODE_TEXT:116,TEXT:117,TAGSTART:118,EDGE_TEXT:119,alphaNumToken:120,direction_tb:121,direction_bt:122,direction_rl:123,direction_lr:124,$accept:0,$end:1},terminals_:{2:"error",8:"SEMI",9:"NEWLINE",10:"SPACE",11:"EOF",12:"GRAPH",13:"NODIR",14:"DIR",27:"subgraph",29:"SQS",31:"SQE",32:"end",34:"acc_title",35:"acc_title_value",36:"acc_descr",37:"acc_descr_value",38:"acc_descr_multiline_value",40:"SHAPE_DATA",44:"AMP",46:"STYLE_SEPARATOR",48:"DOUBLECIRCLESTART",49:"DOUBLECIRCLEEND",50:"PS",51:"PE",52:"(-",53:"-)",54:"STADIUMSTART",55:"STADIUMEND",56:"SUBROUTINESTART",57:"SUBROUTINEEND",58:"VERTEX_WITH_PROPS_START",59:"NODE_STRING[field]",60:"COLON",61:"NODE_STRING[value]",62:"PIPE",63:"CYLINDERSTART",64:"CYLINDEREND",65:"DIAMOND_START",66:"DIAMOND_STOP",67:"TAGEND",68:"TRAPSTART",69:"TRAPEND",70:"INVTRAPSTART",71:"INVTRAPEND",74:"TESTSTR",75:"START_LINK",77:"LINK",78:"LINK_ID",80:"STR",81:"MD_STR",84:"STYLE",85:"LINKSTYLE",86:"CLASSDEF",87:"CLASS",88:"CLICK",89:"DOWN",90:"UP",93:"idString[vertex]",94:"idString[class]",95:"CALLBACKNAME",96:"CALLBACKARGS",97:"HREF",98:"LINK_TARGET",99:"STR[link]",100:"STR[tooltip]",102:"DEFAULT",104:"INTERPOLATE",105:"NUM",106:"COMMA",109:"NODE_STRING",110:"UNIT",111:"BRKT",112:"PCT",114:"MINUS",115:"MULT",116:"UNICODE_TEXT",117:"TEXT",118:"TAGSTART",119:"EDGE_TEXT",121:"direction_tb",122:"direction_bt",123:"direction_rl",124:"direction_lr"},productions_:[0,[3,2],[5,0],[5,2],[6,1],[6,1],[6,1],[6,1],[6,1],[4,2],[4,2],[4,2],[4,3],[16,2],[16,1],[17,1],[17,1],[17,1],[15,1],[15,1],[15,2],[19,2],[19,2],[19,1],[19,1],[18,2],[18,1],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,9],[7,6],[7,4],[7,1],[7,2],[7,2],[7,1],[21,1],[21,1],[21,1],[39,2],[39,1],[20,4],[20,3],[20,4],[20,2],[20,2],[20,1],[42,1],[42,6],[42,5],[43,1],[43,3],[45,4],[45,4],[45,6],[45,4],[45,4],[45,4],[45,8],[45,4],[45,4],[45,4],[45,6],[45,4],[45,4],[45,4],[45,4],[45,4],[45,1],[41,2],[41,3],[41,3],[41,1],[41,3],[41,4],[76,1],[76,2],[76,1],[76,1],[72,1],[72,2],[73,3],[30,1],[30,2],[30,1],[30,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[28,1],[28,2],[28,1],[28,1],[24,5],[25,5],[26,2],[26,4],[26,3],[26,5],[26,3],[26,5],[26,5],[26,7],[26,2],[26,4],[26,2],[26,4],[26,4],[26,6],[22,5],[23,5],[23,5],[23,9],[23,9],[23,7],[23,7],[103,1],[103,3],[92,1],[92,3],[107,1],[107,2],[108,1],[108,1],[108,1],[108,1],[108,1],[108,1],[108,1],[108,1],[113,1],[113,1],[113,1],[113,1],[113,1],[113,1],[113,1],[113,1],[113,1],[113,1],[113,1],[82,1],[82,1],[82,1],[82,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[79,1],[79,1],[120,1],[120,1],[120,1],[120,1],[120,1],[120,1],[120,1],[120,1],[120,1],[120,1],[120,1],[47,1],[47,2],[101,1],[101,2],[33,1],[33,1],[33,1],[33,1]],performAction:(0,p.K2)((function e(t,s,i,r,n,a,u){var o=a.length-1;switch(n){case 2:this.$=[];break;case 3:if(!Array.isArray(a[o])||a[o].length>0){a[o-1].push(a[o])}this.$=a[o-1];break;case 4:case 183:this.$=a[o];break;case 11:r.setDirection("TB");this.$="TB";break;case 12:r.setDirection(a[o-1]);this.$=a[o-1];break;case 27:this.$=a[o-1].nodes;break;case 28:case 29:case 30:case 31:case 32:this.$=[];break;case 33:this.$=r.addSubGraph(a[o-6],a[o-1],a[o-4]);break;case 34:this.$=r.addSubGraph(a[o-3],a[o-1],a[o-3]);break;case 35:this.$=r.addSubGraph(void 0,a[o-1],void 0);break;case 37:this.$=a[o].trim();r.setAccTitle(this.$);break;case 38:case 39:this.$=a[o].trim();r.setAccDescription(this.$);break;case 43:this.$=a[o-1]+a[o];break;case 44:this.$=a[o];break;case 45:r.addVertex(a[o-1][a[o-1].length-1],void 0,void 0,void 0,void 0,void 0,void 0,a[o]);r.addLink(a[o-3].stmt,a[o-1],a[o-2]);this.$={stmt:a[o-1],nodes:a[o-1].concat(a[o-3].nodes)};break;case 46:r.addLink(a[o-2].stmt,a[o],a[o-1]);this.$={stmt:a[o],nodes:a[o].concat(a[o-2].nodes)};break;case 47:r.addLink(a[o-3].stmt,a[o-1],a[o-2]);this.$={stmt:a[o-1],nodes:a[o-1].concat(a[o-3].nodes)};break;case 48:this.$={stmt:a[o-1],nodes:a[o-1]};break;case 49:r.addVertex(a[o-1][a[o-1].length-1],void 0,void 0,void 0,void 0,void 0,void 0,a[o]);this.$={stmt:a[o-1],nodes:a[o-1],shapeData:a[o]};break;case 50:this.$={stmt:a[o],nodes:a[o]};break;case 51:this.$=[a[o]];break;case 52:r.addVertex(a[o-5][a[o-5].length-1],void 0,void 0,void 0,void 0,void 0,void 0,a[o-4]);this.$=a[o-5].concat(a[o]);break;case 53:this.$=a[o-4].concat(a[o]);break;case 54:this.$=a[o];break;case 55:this.$=a[o-2];r.setClass(a[o-2],a[o]);break;case 56:this.$=a[o-3];r.addVertex(a[o-3],a[o-1],"square");break;case 57:this.$=a[o-3];r.addVertex(a[o-3],a[o-1],"doublecircle");break;case 58:this.$=a[o-5];r.addVertex(a[o-5],a[o-2],"circle");break;case 59:this.$=a[o-3];r.addVertex(a[o-3],a[o-1],"ellipse");break;case 60:this.$=a[o-3];r.addVertex(a[o-3],a[o-1],"stadium");break;case 61:this.$=a[o-3];r.addVertex(a[o-3],a[o-1],"subroutine");break;case 62:this.$=a[o-7];r.addVertex(a[o-7],a[o-1],"rect",void 0,void 0,void 0,Object.fromEntries([[a[o-5],a[o-3]]]));break;case 63:this.$=a[o-3];r.addVertex(a[o-3],a[o-1],"cylinder");break;case 64:this.$=a[o-3];r.addVertex(a[o-3],a[o-1],"round");break;case 65:this.$=a[o-3];r.addVertex(a[o-3],a[o-1],"diamond");break;case 66:this.$=a[o-5];r.addVertex(a[o-5],a[o-2],"hexagon");break;case 67:this.$=a[o-3];r.addVertex(a[o-3],a[o-1],"odd");break;case 68:this.$=a[o-3];r.addVertex(a[o-3],a[o-1],"trapezoid");break;case 69:this.$=a[o-3];r.addVertex(a[o-3],a[o-1],"inv_trapezoid");break;case 70:this.$=a[o-3];r.addVertex(a[o-3],a[o-1],"lean_right");break;case 71:this.$=a[o-3];r.addVertex(a[o-3],a[o-1],"lean_left");break;case 72:this.$=a[o];r.addVertex(a[o]);break;case 73:a[o-1].text=a[o];this.$=a[o-1];break;case 74:case 75:a[o-2].text=a[o-1];this.$=a[o-2];break;case 76:this.$=a[o];break;case 77:var l=r.destructLink(a[o],a[o-2]);this.$={type:l.type,stroke:l.stroke,length:l.length,text:a[o-1]};break;case 78:var l=r.destructLink(a[o],a[o-2]);this.$={type:l.type,stroke:l.stroke,length:l.length,text:a[o-1],id:a[o-3]};break;case 79:this.$={text:a[o],type:"text"};break;case 80:this.$={text:a[o-1].text+""+a[o],type:a[o-1].type};break;case 81:this.$={text:a[o],type:"string"};break;case 82:this.$={text:a[o],type:"markdown"};break;case 83:var l=r.destructLink(a[o]);this.$={type:l.type,stroke:l.stroke,length:l.length};break;case 84:var l=r.destructLink(a[o]);this.$={type:l.type,stroke:l.stroke,length:l.length,id:a[o-1]};break;case 85:this.$=a[o-1];break;case 86:this.$={text:a[o],type:"text"};break;case 87:this.$={text:a[o-1].text+""+a[o],type:a[o-1].type};break;case 88:this.$={text:a[o],type:"string"};break;case 89:case 104:this.$={text:a[o],type:"markdown"};break;case 101:this.$={text:a[o],type:"text"};break;case 102:this.$={text:a[o-1].text+""+a[o],type:a[o-1].type};break;case 103:this.$={text:a[o],type:"text"};break;case 105:this.$=a[o-4];r.addClass(a[o-2],a[o]);break;case 106:this.$=a[o-4];r.setClass(a[o-2],a[o]);break;case 107:case 115:this.$=a[o-1];r.setClickEvent(a[o-1],a[o]);break;case 108:case 116:this.$=a[o-3];r.setClickEvent(a[o-3],a[o-2]);r.setTooltip(a[o-3],a[o]);break;case 109:this.$=a[o-2];r.setClickEvent(a[o-2],a[o-1],a[o]);break;case 110:this.$=a[o-4];r.setClickEvent(a[o-4],a[o-3],a[o-2]);r.setTooltip(a[o-4],a[o]);break;case 111:this.$=a[o-2];r.setLink(a[o-2],a[o]);break;case 112:this.$=a[o-4];r.setLink(a[o-4],a[o-2]);r.setTooltip(a[o-4],a[o]);break;case 113:this.$=a[o-4];r.setLink(a[o-4],a[o-2],a[o]);break;case 114:this.$=a[o-6];r.setLink(a[o-6],a[o-4],a[o]);r.setTooltip(a[o-6],a[o-2]);break;case 117:this.$=a[o-1];r.setLink(a[o-1],a[o]);break;case 118:this.$=a[o-3];r.setLink(a[o-3],a[o-2]);r.setTooltip(a[o-3],a[o]);break;case 119:this.$=a[o-3];r.setLink(a[o-3],a[o-2],a[o]);break;case 120:this.$=a[o-5];r.setLink(a[o-5],a[o-4],a[o]);r.setTooltip(a[o-5],a[o-2]);break;case 121:this.$=a[o-4];r.addVertex(a[o-2],void 0,void 0,a[o]);break;case 122:this.$=a[o-4];r.updateLink([a[o-2]],a[o]);break;case 123:this.$=a[o-4];r.updateLink(a[o-2],a[o]);break;case 124:this.$=a[o-8];r.updateLinkInterpolate([a[o-6]],a[o-2]);r.updateLink([a[o-6]],a[o]);break;case 125:this.$=a[o-8];r.updateLinkInterpolate(a[o-6],a[o-2]);r.updateLink(a[o-6],a[o]);break;case 126:this.$=a[o-6];r.updateLinkInterpolate([a[o-4]],a[o]);break;case 127:this.$=a[o-6];r.updateLinkInterpolate(a[o-4],a[o]);break;case 128:case 130:this.$=[a[o]];break;case 129:case 131:a[o-2].push(a[o]);this.$=a[o-2];break;case 133:this.$=a[o-1]+a[o];break;case 181:this.$=a[o];break;case 182:this.$=a[o-1]+""+a[o];break;case 184:this.$=a[o-1]+""+a[o];break;case 185:this.$={stmt:"dir",value:"TB"};break;case 186:this.$={stmt:"dir",value:"BT"};break;case 187:this.$={stmt:"dir",value:"RL"};break;case 188:this.$={stmt:"dir",value:"LR"};break}}),"anonymous"),table:[{3:1,4:2,9:t,10:s,12:i},{1:[3]},e(r,n,{5:6}),{4:7,9:t,10:s,12:i},{4:8,9:t,10:s,12:i},{13:[1,9],14:[1,10]},{1:[2,1],6:11,7:12,8:a,9:u,10:o,11:l,20:17,22:18,23:19,24:20,25:21,26:22,27:c,33:24,34:h,36:d,38:f,42:28,43:38,44:b,45:39,47:40,60:k,84:g,85:A,86:y,87:m,88:E,89:x,102:C,105:D,106:S,109:T,111:v,113:41,114:F,115:_,116:B,121:w,122:L,123:$,124:I},e(r,[2,9]),e(r,[2,10]),e(r,[2,11]),{8:[1,54],9:[1,55],10:R,15:53,18:56},e(N,[2,3]),e(N,[2,4]),e(N,[2,5]),e(N,[2,6]),e(N,[2,7]),e(N,[2,8]),{8:P,9:G,11:K,21:58,41:59,72:63,75:[1,64],77:[1,66],78:[1,65]},{8:P,9:G,11:K,21:67},{8:P,9:G,11:K,21:68},{8:P,9:G,11:K,21:69},{8:P,9:G,11:K,21:70},{8:P,9:G,11:K,21:71},{8:P,9:G,10:[1,72],11:K,21:73},e(N,[2,36]),{35:[1,74]},{37:[1,75]},e(N,[2,39]),e(O,[2,50],{18:76,39:77,10:R,40:V}),{10:[1,79]},{10:[1,80]},{10:[1,81]},{10:[1,82]},{14:M,44:U,60:W,80:[1,86],89:j,95:[1,83],97:[1,84],101:85,105:Y,106:z,109:X,111:H,114:q,115:Q,116:Z,120:87},e(N,[2,185]),e(N,[2,186]),e(N,[2,187]),e(N,[2,188]),e(J,[2,51]),e(J,[2,54],{46:[1,99]}),e(ee,[2,72],{113:112,29:[1,100],44:b,48:[1,101],50:[1,102],52:[1,103],54:[1,104],56:[1,105],58:[1,106],60:k,63:[1,107],65:[1,108],67:[1,109],68:[1,110],70:[1,111],89:x,102:C,105:D,106:S,109:T,111:v,114:F,115:_,116:B}),e(te,[2,181]),e(te,[2,142]),e(te,[2,143]),e(te,[2,144]),e(te,[2,145]),e(te,[2,146]),e(te,[2,147]),e(te,[2,148]),e(te,[2,149]),e(te,[2,150]),e(te,[2,151]),e(te,[2,152]),e(r,[2,12]),e(r,[2,18]),e(r,[2,19]),{9:[1,113]},e(se,[2,26],{18:114,10:R}),e(N,[2,27]),{42:115,43:38,44:b,45:39,47:40,60:k,89:x,102:C,105:D,106:S,109:T,111:v,113:41,114:F,115:_,116:B},e(N,[2,40]),e(N,[2,41]),e(N,[2,42]),e(ie,[2,76],{73:116,62:[1,118],74:[1,117]}),{76:119,79:120,80:re,81:ne,116:ae,119:ue},{75:[1,125],77:[1,126]},e(oe,[2,83]),e(N,[2,28]),e(N,[2,29]),e(N,[2,30]),e(N,[2,31]),e(N,[2,32]),{10:le,12:ce,14:he,27:de,28:127,32:pe,44:fe,60:be,75:ke,80:[1,129],81:[1,130],83:140,84:ge,85:Ae,86:ye,87:me,88:Ee,89:xe,90:Ce,91:128,105:De,109:Se,111:Te,114:ve,115:Fe,116:_e},e(Be,n,{5:153}),e(N,[2,37]),e(N,[2,38]),e(O,[2,48],{44:we}),e(O,[2,49],{18:155,10:R,40:Le}),e(J,[2,44]),{44:b,47:157,60:k,89:x,102:C,105:D,106:S,109:T,111:v,113:41,114:F,115:_,116:B},{102:[1,158],103:159,105:[1,160]},{44:b,47:161,60:k,89:x,102:C,105:D,106:S,109:T,111:v,113:41,114:F,115:_,116:B},{44:b,47:162,60:k,89:x,102:C,105:D,106:S,109:T,111:v,113:41,114:F,115:_,116:B},e($e,[2,107],{10:[1,163],96:[1,164]}),{80:[1,165]},e($e,[2,115],{120:167,10:[1,166],14:M,44:U,60:W,89:j,105:Y,106:z,109:X,111:H,114:q,115:Q,116:Z}),e($e,[2,117],{10:[1,168]}),e(Ie,[2,183]),e(Ie,[2,170]),e(Ie,[2,171]),e(Ie,[2,172]),e(Ie,[2,173]),e(Ie,[2,174]),e(Ie,[2,175]),e(Ie,[2,176]),e(Ie,[2,177]),e(Ie,[2,178]),e(Ie,[2,179]),e(Ie,[2,180]),{44:b,47:169,60:k,89:x,102:C,105:D,106:S,109:T,111:v,113:41,114:F,115:_,116:B},{30:170,67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},{30:178,67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},{30:180,50:[1,179],67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},{30:181,67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},{30:182,67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},{30:183,67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},{109:[1,184]},{30:185,67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},{30:186,65:[1,187],67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},{30:188,67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},{30:189,67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},{30:190,67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},e(te,[2,182]),e(r,[2,20]),e(se,[2,25]),e(O,[2,46],{39:191,18:192,10:R,40:V}),e(ie,[2,73],{10:[1,193]}),{10:[1,194]},{30:195,67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},{77:[1,196],79:197,116:ae,119:ue},e(Ve,[2,79]),e(Ve,[2,81]),e(Ve,[2,82]),e(Ve,[2,168]),e(Ve,[2,169]),{76:198,79:120,80:re,81:ne,116:ae,119:ue},e(oe,[2,84]),{8:P,9:G,10:le,11:K,12:ce,14:he,21:200,27:de,29:[1,199],32:pe,44:fe,60:be,75:ke,83:140,84:ge,85:Ae,86:ye,87:me,88:Ee,89:xe,90:Ce,91:201,105:De,109:Se,111:Te,114:ve,115:Fe,116:_e},e(Me,[2,101]),e(Me,[2,103]),e(Me,[2,104]),e(Me,[2,157]),e(Me,[2,158]),e(Me,[2,159]),e(Me,[2,160]),e(Me,[2,161]),e(Me,[2,162]),e(Me,[2,163]),e(Me,[2,164]),e(Me,[2,165]),e(Me,[2,166]),e(Me,[2,167]),e(Me,[2,90]),e(Me,[2,91]),e(Me,[2,92]),e(Me,[2,93]),e(Me,[2,94]),e(Me,[2,95]),e(Me,[2,96]),e(Me,[2,97]),e(Me,[2,98]),e(Me,[2,99]),e(Me,[2,100]),{6:11,7:12,8:a,9:u,10:o,11:l,20:17,22:18,23:19,24:20,25:21,26:22,27:c,32:[1,202],33:24,34:h,36:d,38:f,42:28,43:38,44:b,45:39,47:40,60:k,84:g,85:A,86:y,87:m,88:E,89:x,102:C,105:D,106:S,109:T,111:v,113:41,114:F,115:_,116:B,121:w,122:L,123:$,124:I},{10:R,18:203},{44:[1,204]},e(J,[2,43]),{10:[1,205],44:b,60:k,89:x,102:C,105:D,106:S,109:T,111:v,113:112,114:F,115:_,116:B},{10:[1,206]},{10:[1,207],106:[1,208]},e(Ue,[2,128]),{10:[1,209],44:b,60:k,89:x,102:C,105:D,106:S,109:T,111:v,113:112,114:F,115:_,116:B},{10:[1,210],44:b,60:k,89:x,102:C,105:D,106:S,109:T,111:v,113:112,114:F,115:_,116:B},{80:[1,211]},e($e,[2,109],{10:[1,212]}),e($e,[2,111],{10:[1,213]}),{80:[1,214]},e(Ie,[2,184]),{80:[1,215],98:[1,216]},e(J,[2,55],{113:112,44:b,60:k,89:x,102:C,105:D,106:S,109:T,111:v,114:F,115:_,116:B}),{31:[1,217],67:Re,82:218,116:Ge,117:Ke,118:Oe},e(We,[2,86]),e(We,[2,88]),e(We,[2,89]),e(We,[2,153]),e(We,[2,154]),e(We,[2,155]),e(We,[2,156]),{49:[1,219],67:Re,82:218,116:Ge,117:Ke,118:Oe},{30:220,67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},{51:[1,221],67:Re,82:218,116:Ge,117:Ke,118:Oe},{53:[1,222],67:Re,82:218,116:Ge,117:Ke,118:Oe},{55:[1,223],67:Re,82:218,116:Ge,117:Ke,118:Oe},{57:[1,224],67:Re,82:218,116:Ge,117:Ke,118:Oe},{60:[1,225]},{64:[1,226],67:Re,82:218,116:Ge,117:Ke,118:Oe},{66:[1,227],67:Re,82:218,116:Ge,117:Ke,118:Oe},{30:228,67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},{31:[1,229],67:Re,82:218,116:Ge,117:Ke,118:Oe},{67:Re,69:[1,230],71:[1,231],82:218,116:Ge,117:Ke,118:Oe},{67:Re,69:[1,233],71:[1,232],82:218,116:Ge,117:Ke,118:Oe},e(O,[2,45],{18:155,10:R,40:Le}),e(O,[2,47],{44:we}),e(ie,[2,75]),e(ie,[2,74]),{62:[1,234],67:Re,82:218,116:Ge,117:Ke,118:Oe},e(ie,[2,77]),e(Ve,[2,80]),{77:[1,235],79:197,116:ae,119:ue},{30:236,67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},e(Be,n,{5:237}),e(Me,[2,102]),e(N,[2,35]),{43:238,44:b,45:39,47:40,60:k,89:x,102:C,105:D,106:S,109:T,111:v,113:41,114:F,115:_,116:B},{10:R,18:239},{10:je,60:Ye,84:ze,92:240,105:Xe,107:241,108:242,109:He,110:qe,111:Qe,112:Ze},{10:je,60:Ye,84:ze,92:251,104:[1,252],105:Xe,107:241,108:242,109:He,110:qe,111:Qe,112:Ze},{10:je,60:Ye,84:ze,92:253,104:[1,254],105:Xe,107:241,108:242,109:He,110:qe,111:Qe,112:Ze},{105:[1,255]},{10:je,60:Ye,84:ze,92:256,105:Xe,107:241,108:242,109:He,110:qe,111:Qe,112:Ze},{44:b,47:257,60:k,89:x,102:C,105:D,106:S,109:T,111:v,113:41,114:F,115:_,116:B},e($e,[2,108]),{80:[1,258]},{80:[1,259],98:[1,260]},e($e,[2,116]),e($e,[2,118],{10:[1,261]}),e($e,[2,119]),e(ee,[2,56]),e(We,[2,87]),e(ee,[2,57]),{51:[1,262],67:Re,82:218,116:Ge,117:Ke,118:Oe},e(ee,[2,64]),e(ee,[2,59]),e(ee,[2,60]),e(ee,[2,61]),{109:[1,263]},e(ee,[2,63]),e(ee,[2,65]),{66:[1,264],67:Re,82:218,116:Ge,117:Ke,118:Oe},e(ee,[2,67]),e(ee,[2,68]),e(ee,[2,70]),e(ee,[2,69]),e(ee,[2,71]),e([10,44,60,89,102,105,106,109,111,114,115,116],[2,85]),e(ie,[2,78]),{31:[1,265],67:Re,82:218,116:Ge,117:Ke,118:Oe},{6:11,7:12,8:a,9:u,10:o,11:l,20:17,22:18,23:19,24:20,25:21,26:22,27:c,32:[1,266],33:24,34:h,36:d,38:f,42:28,43:38,44:b,45:39,47:40,60:k,84:g,85:A,86:y,87:m,88:E,89:x,102:C,105:D,106:S,109:T,111:v,113:41,114:F,115:_,116:B,121:w,122:L,123:$,124:I},e(J,[2,53]),{43:267,44:b,45:39,47:40,60:k,89:x,102:C,105:D,106:S,109:T,111:v,113:41,114:F,115:_,116:B},e($e,[2,121],{106:Je}),e(et,[2,130],{108:269,10:je,60:Ye,84:ze,105:Xe,109:He,110:qe,111:Qe,112:Ze}),e(tt,[2,132]),e(tt,[2,134]),e(tt,[2,135]),e(tt,[2,136]),e(tt,[2,137]),e(tt,[2,138]),e(tt,[2,139]),e(tt,[2,140]),e(tt,[2,141]),e($e,[2,122],{106:Je}),{10:[1,270]},e($e,[2,123],{106:Je}),{10:[1,271]},e(Ue,[2,129]),e($e,[2,105],{106:Je}),e($e,[2,106],{113:112,44:b,60:k,89:x,102:C,105:D,106:S,109:T,111:v,114:F,115:_,116:B}),e($e,[2,110]),e($e,[2,112],{10:[1,272]}),e($e,[2,113]),{98:[1,273]},{51:[1,274]},{62:[1,275]},{66:[1,276]},{8:P,9:G,11:K,21:277},e(N,[2,34]),e(J,[2,52]),{10:je,60:Ye,84:ze,105:Xe,107:278,108:242,109:He,110:qe,111:Qe,112:Ze},e(tt,[2,133]),{14:M,44:U,60:W,89:j,101:279,105:Y,106:z,109:X,111:H,114:q,115:Q,116:Z,120:87},{14:M,44:U,60:W,89:j,101:280,105:Y,106:z,109:X,111:H,114:q,115:Q,116:Z,120:87},{98:[1,281]},e($e,[2,120]),e(ee,[2,58]),{30:282,67:Re,80:Ne,81:Pe,82:171,116:Ge,117:Ke,118:Oe},e(ee,[2,66]),e(Be,n,{5:283}),e(et,[2,131],{108:269,10:je,60:Ye,84:ze,105:Xe,109:He,110:qe,111:Qe,112:Ze}),e($e,[2,126],{120:167,10:[1,284],14:M,44:U,60:W,89:j,105:Y,106:z,109:X,111:H,114:q,115:Q,116:Z}),e($e,[2,127],{120:167,10:[1,285],14:M,44:U,60:W,89:j,105:Y,106:z,109:X,111:H,114:q,115:Q,116:Z}),e($e,[2,114]),{31:[1,286],67:Re,82:218,116:Ge,117:Ke,118:Oe},{6:11,7:12,8:a,9:u,10:o,11:l,20:17,22:18,23:19,24:20,25:21,26:22,27:c,32:[1,287],33:24,34:h,36:d,38:f,42:28,43:38,44:b,45:39,47:40,60:k,84:g,85:A,86:y,87:m,88:E,89:x,102:C,105:D,106:S,109:T,111:v,113:41,114:F,115:_,116:B,121:w,122:L,123:$,124:I},{10:je,60:Ye,84:ze,92:288,105:Xe,107:241,108:242,109:He,110:qe,111:Qe,112:Ze},{10:je,60:Ye,84:ze,92:289,105:Xe,107:241,108:242,109:He,110:qe,111:Qe,112:Ze},e(ee,[2,62]),e(N,[2,33]),e($e,[2,124],{106:Je}),e($e,[2,125],{106:Je})],defaultActions:{},parseError:(0,p.K2)((function e(t,s){if(s.recoverable){this.trace(t)}else{var i=new Error(t);i.hash=s;throw i}}),"parseError"),parse:(0,p.K2)((function e(t){var s=this,i=[0],r=[],n=[null],a=[],u=this.table,o="",l=0,c=0,h=0,d=2,f=1;var b=a.slice.call(arguments,1);var k=Object.create(this.lexer);var g={yy:{}};for(var A in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,A)){g.yy[A]=this.yy[A]}}k.setInput(t,g.yy);g.yy.lexer=k;g.yy.parser=this;if(typeof k.yylloc=="undefined"){k.yylloc={}}var y=k.yylloc;a.push(y);var m=k.options&&k.options.ranges;if(typeof g.yy.parseError==="function"){this.parseError=g.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function E(e){i.length=i.length-2*e;n.length=n.length-e;a.length=a.length-e}(0,p.K2)(E,"popStack");function x(){var e;e=r.pop()||k.lex()||f;if(typeof e!=="number"){if(e instanceof Array){r=e;e=r.pop()}e=s.symbols_[e]||e}return e}(0,p.K2)(x,"lex");var C,D,S,T,v,F,_={},B,w,L,$;while(true){S=i[i.length-1];if(this.defaultActions[S]){T=this.defaultActions[S]}else{if(C===null||typeof C=="undefined"){C=x()}T=u[S]&&u[S][C]}if(typeof T==="undefined"||!T.length||!T[0]){var I="";$=[];for(B in u[S]){if(this.terminals_[B]&&B>d){$.push("'"+this.terminals_[B]+"'")}}if(k.showPosition){I="Parse error on line "+(l+1)+":\n"+k.showPosition()+"\nExpecting "+$.join(", ")+", got '"+(this.terminals_[C]||C)+"'"}else{I="Parse error on line "+(l+1)+": Unexpected "+(C==f?"end of input":"'"+(this.terminals_[C]||C)+"'")}this.parseError(I,{text:k.match,token:this.terminals_[C]||C,line:k.yylineno,loc:y,expected:$})}if(T[0]instanceof Array&&T.length>1){throw new Error("Parse Error: multiple actions possible at state: "+S+", token: "+C)}switch(T[0]){case 1:i.push(C);n.push(k.yytext);a.push(k.yylloc);i.push(T[1]);C=null;if(!D){c=k.yyleng;o=k.yytext;l=k.yylineno;y=k.yylloc;if(h>0){h--}}else{C=D;D=null}break;case 2:w=this.productions_[T[1]][1];_.$=n[n.length-w];_._$={first_line:a[a.length-(w||1)].first_line,last_line:a[a.length-1].last_line,first_column:a[a.length-(w||1)].first_column,last_column:a[a.length-1].last_column};if(m){_._$.range=[a[a.length-(w||1)].range[0],a[a.length-1].range[1]]}F=this.performAction.apply(_,[o,c,l,g.yy,T[1],n,a].concat(b));if(typeof F!=="undefined"){return F}if(w){i=i.slice(0,-1*w*2);n=n.slice(0,-1*w);a=a.slice(0,-1*w)}i.push(this.productions_[T[1]][0]);n.push(_.$);a.push(_._$);L=u[i[i.length-2]][i[i.length-1]];i.push(L);break;case 3:return true}}return true}),"parse")};var it=function(){var e={EOF:1,parseError:(0,p.K2)((function e(t,s){if(this.yy.parser){this.yy.parser.parseError(t,s)}else{throw new Error(t)}}),"parseError"),setInput:(0,p.K2)((function(e,t){this.yy=t||this.yy||{};this._input=e;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this}),"setInput"),input:(0,p.K2)((function(){var e=this._input[0];this.yytext+=e;this.yyleng++;this.offset++;this.match+=e;this.matched+=e;var t=e.match(/(?:\r\n?|\n).*/g);if(t){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return e}),"input"),unput:(0,p.K2)((function(e){var t=e.length;var s=e.split(/(?:\r\n?|\n)/g);this._input=e+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-t);this.offset-=t;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(s.length-1){this.yylineno-=s.length-1}var r=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===i.length?this.yylloc.first_column:0)+i[i.length-s.length].length-s[0].length:this.yylloc.first_column-t};if(this.options.ranges){this.yylloc.range=[r[0],r[0]+this.yyleng-t]}this.yyleng=this.yytext.length;return this}),"unput"),more:(0,p.K2)((function(){this._more=true;return this}),"more"),reject:(0,p.K2)((function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this}),"reject"),less:(0,p.K2)((function(e){this.unput(this.match.slice(e))}),"less"),pastInput:(0,p.K2)((function(){var e=this.matched.substr(0,this.matched.length-this.match.length);return(e.length>20?"...":"")+e.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,p.K2)((function(){var e=this.match;if(e.length<20){e+=this._input.substr(0,20-e.length)}return(e.substr(0,20)+(e.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,p.K2)((function(){var e=this.pastInput();var t=new Array(e.length+1).join("-");return e+this.upcomingInput()+"\n"+t+"^"}),"showPosition"),test_match:(0,p.K2)((function(e,t){var s,i,r;if(this.options.backtrack_lexer){r={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){r.yylloc.range=this.yylloc.range.slice(0)}}i=e[0].match(/(?:\r\n?|\n).*/g);if(i){this.yylineno+=i.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+e[0].length};this.yytext+=e[0];this.match+=e[0];this.matches=e;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(e[0].length);this.matched+=e[0];s=this.performAction.call(this,this.yy,this,t,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(s){return s}else if(this._backtrack){for(var n in r){this[n]=r[n]}return false}return false}),"test_match"),next:(0,p.K2)((function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var e,t,s,i;if(!this._more){this.yytext="";this.match=""}var r=this._currentRules();for(var n=0;n<r.length;n++){s=this._input.match(this.rules[r[n]]);if(s&&(!t||s[0].length>t[0].length)){t=s;i=n;if(this.options.backtrack_lexer){e=this.test_match(s,r[n]);if(e!==false){return e}else if(this._backtrack){t=false;continue}else{return false}}else if(!this.options.flex){break}}}if(t){e=this.test_match(t,r[i]);if(e!==false){return e}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}}),"next"),lex:(0,p.K2)((function e(){var t=this.next();if(t){return t}else{return this.lex()}}),"lex"),begin:(0,p.K2)((function e(t){this.conditionStack.push(t)}),"begin"),popState:(0,p.K2)((function e(){var t=this.conditionStack.length-1;if(t>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}}),"popState"),_currentRules:(0,p.K2)((function e(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}}),"_currentRules"),topState:(0,p.K2)((function e(t){t=this.conditionStack.length-1-Math.abs(t||0);if(t>=0){return this.conditionStack[t]}else{return"INITIAL"}}),"topState"),pushState:(0,p.K2)((function e(t){this.begin(t)}),"pushState"),stateStackSize:(0,p.K2)((function e(){return this.conditionStack.length}),"stateStackSize"),options:{},performAction:(0,p.K2)((function e(t,s,i,r){var n=r;switch(i){case 0:this.begin("acc_title");return 34;break;case 1:this.popState();return"acc_title_value";break;case 2:this.begin("acc_descr");return 36;break;case 3:this.popState();return"acc_descr_value";break;case 4:this.begin("acc_descr_multiline");break;case 5:this.popState();break;case 6:return"acc_descr_multiline_value";break;case 7:this.pushState("shapeData");s.yytext="";return 40;break;case 8:this.pushState("shapeDataStr");return 40;break;case 9:this.popState();return 40;break;case 10:const e=/\n\s*/g;s.yytext=s.yytext.replace(e,"<br/>");return 40;break;case 11:return 40;break;case 12:this.popState();break;case 13:this.begin("callbackname");break;case 14:this.popState();break;case 15:this.popState();this.begin("callbackargs");break;case 16:return 95;break;case 17:this.popState();break;case 18:return 96;break;case 19:return"MD_STR";break;case 20:this.popState();break;case 21:this.begin("md_string");break;case 22:return"STR";break;case 23:this.popState();break;case 24:this.pushState("string");break;case 25:return 84;break;case 26:return 102;break;case 27:return 85;break;case 28:return 104;break;case 29:return 86;break;case 30:return 87;break;case 31:return 97;break;case 32:this.begin("click");break;case 33:this.popState();break;case 34:return 88;break;case 35:if(t.lex.firstGraph()){this.begin("dir")}return 12;break;case 36:if(t.lex.firstGraph()){this.begin("dir")}return 12;break;case 37:if(t.lex.firstGraph()){this.begin("dir")}return 12;break;case 38:return 27;break;case 39:return 32;break;case 40:return 98;break;case 41:return 98;break;case 42:return 98;break;case 43:return 98;break;case 44:this.popState();return 13;break;case 45:this.popState();return 14;break;case 46:this.popState();return 14;break;case 47:this.popState();return 14;break;case 48:this.popState();return 14;break;case 49:this.popState();return 14;break;case 50:this.popState();return 14;break;case 51:this.popState();return 14;break;case 52:this.popState();return 14;break;case 53:this.popState();return 14;break;case 54:this.popState();return 14;break;case 55:return 121;break;case 56:return 122;break;case 57:return 123;break;case 58:return 124;break;case 59:return 78;break;case 60:return 105;break;case 61:return 111;break;case 62:return 46;break;case 63:return 60;break;case 64:return 44;break;case 65:return 8;break;case 66:return 106;break;case 67:return 115;break;case 68:this.popState();return 77;break;case 69:this.pushState("edgeText");return 75;break;case 70:return 119;break;case 71:this.popState();return 77;break;case 72:this.pushState("thickEdgeText");return 75;break;case 73:return 119;break;case 74:this.popState();return 77;break;case 75:this.pushState("dottedEdgeText");return 75;break;case 76:return 119;break;case 77:return 77;break;case 78:this.popState();return 53;break;case 79:return"TEXT";break;case 80:this.pushState("ellipseText");return 52;break;case 81:this.popState();return 55;break;case 82:this.pushState("text");return 54;break;case 83:this.popState();return 57;break;case 84:this.pushState("text");return 56;break;case 85:return 58;break;case 86:this.pushState("text");return 67;break;case 87:this.popState();return 64;break;case 88:this.pushState("text");return 63;break;case 89:this.popState();return 49;break;case 90:this.pushState("text");return 48;break;case 91:this.popState();return 69;break;case 92:this.popState();return 71;break;case 93:return 117;break;case 94:this.pushState("trapText");return 68;break;case 95:this.pushState("trapText");return 70;break;case 96:return 118;break;case 97:return 67;break;case 98:return 90;break;case 99:return"SEP";break;case 100:return 89;break;case 101:return 115;break;case 102:return 111;break;case 103:return 44;break;case 104:return 109;break;case 105:return 114;break;case 106:return 116;break;case 107:this.popState();return 62;break;case 108:this.pushState("text");return 62;break;case 109:this.popState();return 51;break;case 110:this.pushState("text");return 50;break;case 111:this.popState();return 31;break;case 112:this.pushState("text");return 29;break;case 113:this.popState();return 66;break;case 114:this.pushState("text");return 65;break;case 115:return"TEXT";break;case 116:return"QUOTE";break;case 117:return 9;break;case 118:return 10;break;case 119:return 11;break}}),"anonymous"),rules:[/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:@\{)/,/^(?:["])/,/^(?:["])/,/^(?:[^\"]+)/,/^(?:[^}^"]+)/,/^(?:\})/,/^(?:call[\s]+)/,/^(?:\([\s]*\))/,/^(?:\()/,/^(?:[^(]*)/,/^(?:\))/,/^(?:[^)]*)/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["][`])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:["])/,/^(?:style\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\b)/,/^(?:class\b)/,/^(?:href[\s])/,/^(?:click[\s]+)/,/^(?:[\s\n])/,/^(?:[^\s\n]*)/,/^(?:flowchart-elk\b)/,/^(?:graph\b)/,/^(?:flowchart\b)/,/^(?:subgraph\b)/,/^(?:end\b\s*)/,/^(?:_self\b)/,/^(?:_blank\b)/,/^(?:_parent\b)/,/^(?:_top\b)/,/^(?:(\r?\n)*\s*\n)/,/^(?:\s*LR\b)/,/^(?:\s*RL\b)/,/^(?:\s*TB\b)/,/^(?:\s*BT\b)/,/^(?:\s*TD\b)/,/^(?:\s*BR\b)/,/^(?:\s*<)/,/^(?:\s*>)/,/^(?:\s*\^)/,/^(?:\s*v\b)/,/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:[^\s\"]+@(?=[^\{\"]))/,/^(?:[0-9]+)/,/^(?:#)/,/^(?::::)/,/^(?::)/,/^(?:&)/,/^(?:;)/,/^(?:,)/,/^(?:\*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:[^-]|-(?!-)+)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:[^=]|=(?!))/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:[^\.]|\.(?!))/,/^(?:\s*~~[\~]+\s*)/,/^(?:[-/\)][\)])/,/^(?:[^\(\)\[\]\{\}]|!\)+)/,/^(?:\(-)/,/^(?:\]\))/,/^(?:\(\[)/,/^(?:\]\])/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:>)/,/^(?:\)\])/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\(\(\()/,/^(?:[\\(?=\])][\]])/,/^(?:\/(?=\])\])/,/^(?:\/(?!\])|\\(?!\])|[^\\\[\]\(\)\{\}\/]+)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:<)/,/^(?:>)/,/^(?:\^)/,/^(?:\\\|)/,/^(?:v\b)/,/^(?:\*)/,/^(?:#)/,/^(?:&)/,/^(?:([A-Za-z0-9!"\#$%&'*+\.`?\\_\/]|-(?=[^\>\-\.])|(?!))+)/,/^(?:-)/,/^(?:[\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6]|[\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377]|[\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5]|[\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA]|[\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE]|[\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA]|[\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0]|[\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977]|[\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2]|[\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A]|[\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39]|[\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8]|[\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C]|[\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C]|[\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99]|[\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0]|[\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D]|[\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3]|[\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10]|[\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1]|[\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81]|[\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3]|[\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6]|[\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A]|[\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081]|[\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D]|[\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0]|[\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310]|[\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C]|[\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711]|[\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7]|[\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C]|[\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16]|[\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF]|[\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC]|[\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D]|[\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D]|[\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3]|[\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F]|[\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128]|[\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184]|[\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3]|[\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6]|[\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE]|[\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C]|[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D]|[\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC]|[\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B]|[\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788]|[\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805]|[\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB]|[\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28]|[\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5]|[\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4]|[\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|[\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D]|[\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36]|[\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D]|[\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC]|[\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF]|[\uFFD2-\uFFD7\uFFDA-\uFFDC])/,/^(?:\|)/,/^(?:\|)/,/^(?:\))/,/^(?:\()/,/^(?:\])/,/^(?:\[)/,/^(?:(\}))/,/^(?:\{)/,/^(?:[^\[\]\(\)\{\}\|\"]+)/,/^(?:")/,/^(?:(\r?\n)+)/,/^(?:\s)/,/^(?:$)/],conditions:{shapeDataEndBracket:{rules:[21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},shapeDataStr:{rules:[9,10,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},shapeData:{rules:[8,11,12,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},callbackargs:{rules:[17,18,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},callbackname:{rules:[14,15,16,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},href:{rules:[21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},click:{rules:[21,24,33,34,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},dottedEdgeText:{rules:[21,24,74,76,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},thickEdgeText:{rules:[21,24,71,73,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},edgeText:{rules:[21,24,68,70,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},trapText:{rules:[21,24,77,80,82,84,88,90,91,92,93,94,95,108,110,112,114],inclusive:false},ellipseText:{rules:[21,24,77,78,79,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},text:{rules:[21,24,77,80,81,82,83,84,87,88,89,90,94,95,107,108,109,110,111,112,113,114,115],inclusive:false},vertex:{rules:[21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},dir:{rules:[21,24,44,45,46,47,48,49,50,51,52,53,54,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},acc_descr_multiline:{rules:[5,6,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},acc_descr:{rules:[3,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},acc_title:{rules:[1,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},md_string:{rules:[19,20,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},string:{rules:[21,22,23,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:false},INITIAL:{rules:[0,2,4,7,13,21,24,25,26,27,28,29,30,31,32,35,36,37,38,39,40,41,42,43,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,71,72,74,75,77,80,82,84,85,86,88,90,94,95,96,97,98,99,100,101,102,103,104,105,106,108,110,112,114,116,117,118,119],inclusive:true}}};return e}();st.lexer=it;function rt(){this.yy={}}(0,p.K2)(rt,"Parser");rt.prototype=st;st.Parser=rt;return new rt}();x.parser=x;var C=x;var D=Object.assign({},C);D.parse=e=>{const t=e.replace(/}\s*\n/g,"}\n");return C.parse(t)};var S=D;var T=(0,p.K2)(((e,t)=>{const s=b.A;const i=s(e,"r");const r=s(e,"g");const n=s(e,"b");return k.A(i,r,n,t)}),"fade");var v=(0,p.K2)((e=>`.label {\n    font-family: ${e.fontFamily};\n    color: ${e.nodeTextColor||e.textColor};\n  }\n  .cluster-label text {\n    fill: ${e.titleColor};\n  }\n  .cluster-label span {\n    color: ${e.titleColor};\n  }\n  .cluster-label span p {\n    background-color: transparent;\n  }\n\n  .label text,span {\n    fill: ${e.nodeTextColor||e.textColor};\n    color: ${e.nodeTextColor||e.textColor};\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${e.mainBkg};\n    stroke: ${e.nodeBorder};\n    stroke-width: 1px;\n  }\n  .rough-node .label text , .node .label text, .image-shape .label, .icon-shape .label {\n    text-anchor: middle;\n  }\n  // .flowchart-label .text-outer-tspan {\n  //   text-anchor: middle;\n  // }\n  // .flowchart-label .text-inner-tspan {\n  //   text-anchor: start;\n  // }\n\n  .node .katex path {\n    fill: #000;\n    stroke: #000;\n    stroke-width: 1px;\n  }\n\n  .rough-node .label,.node .label, .image-shape .label, .icon-shape .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n\n  .root .anchor path {\n    fill: ${e.lineColor} !important;\n    stroke-width: 0;\n    stroke: ${e.lineColor};\n  }\n\n  .arrowheadPath {\n    fill: ${e.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${e.lineColor};\n    stroke-width: 2.0px;\n  }\n\n  .flowchart-link {\n    stroke: ${e.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${e.edgeLabelBackground};\n    p {\n      background-color: ${e.edgeLabelBackground};\n    }\n    rect {\n      opacity: 0.5;\n      background-color: ${e.edgeLabelBackground};\n      fill: ${e.edgeLabelBackground};\n    }\n    text-align: center;\n  }\n\n  /* For html labels only */\n  .labelBkg {\n    background-color: ${T(e.edgeLabelBackground,.5)};\n    // background-color:\n  }\n\n  .cluster rect {\n    fill: ${e.clusterBkg};\n    stroke: ${e.clusterBorder};\n    stroke-width: 1px;\n  }\n\n  .cluster text {\n    fill: ${e.titleColor};\n  }\n\n  .cluster span {\n    color: ${e.titleColor};\n  }\n  /* .cluster div {\n    color: ${e.titleColor};\n  } */\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: ${e.fontFamily};\n    font-size: 12px;\n    background: ${e.tertiaryColor};\n    border: 1px solid ${e.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .flowchartTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${e.textColor};\n  }\n\n  rect.text {\n    fill: none;\n    stroke-width: 0;\n  }\n\n  .icon-shape, .image-shape {\n    background-color: ${e.edgeLabelBackground};\n    p {\n      background-color: ${e.edgeLabelBackground};\n      padding: 2px;\n    }\n    rect {\n      opacity: 0.5;\n      background-color: ${e.edgeLabelBackground};\n      fill: ${e.edgeLabelBackground};\n    }\n    text-align: center;\n  }\n`),"getStyles");var F=v;var _={parser:S,get db(){return new A},renderer:E,styles:F,init:(0,p.K2)((e=>{if(!e.flowchart){e.flowchart={}}if(e.layout){(0,p.XV)({layout:e.layout})}e.flowchart.arrowMarkerAbsolute=e.arrowMarkerAbsolute;(0,p.XV)({flowchart:{arrowMarkerAbsolute:e.arrowMarkerAbsolute}})}),"init")}}}]);