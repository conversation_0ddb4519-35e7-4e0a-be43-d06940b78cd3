@echo off
REM Perception Engineering Environment Setup Script for Windows
REM Sets up complete development environment for perception engineering

echo ==========================================
echo Perception Engineering Environment Setup
echo ==========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo [INFO] Python found
python --version

REM Check if pip is available
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] pip is not available
    echo Please ensure pip is installed with Python
    pause
    exit /b 1
)

echo [INFO] pip found
pip --version

REM Create virtual environment
echo [INFO] Creating Python virtual environment...
python -m venv venv
if %errorlevel% neq 0 (
    echo [ERROR] Failed to create virtual environment
    pause
    exit /b 1
)

REM Activate virtual environment
echo [INFO] Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo [INFO] Upgrading pip...
python -m pip install --upgrade pip setuptools wheel

REM Install core Python packages
echo [INFO] Installing core Python packages...
pip install numpy scipy matplotlib opencv-python scikit-learn scikit-image pandas seaborn jupyter ipython pytest pytest-cov black flake8 mypy

REM Install additional scientific packages
echo [INFO] Installing additional scientific packages...
pip install sympy numba cython h5py pillow

REM Install communication libraries
echo [INFO] Installing communication libraries...
pip install pyserial

REM Try to install python-can (may fail on Windows without proper setup)
pip install python-can
if %errorlevel% neq 0 (
    echo [WARNING] python-can installation failed - CAN support may be limited
)

REM Install cantools
pip install cantools

REM Create directories
echo [INFO] Creating project directories...
if not exist "data\test" mkdir data\test
if not exist "logs" mkdir logs
if not exist "build" mkdir build

REM Copy test configuration
echo [INFO] Setting up test configuration...
if exist "config\perception_system.yaml" (
    copy "config\perception_system.yaml" "data\test\test_config.yaml"
)

REM Create batch files for easy development
echo [INFO] Creating development shortcuts...

REM Create activation script
echo @echo off > activate_env.bat
echo echo Activating Perception Engineering Environment... >> activate_env.bat
echo call venv\Scripts\activate.bat >> activate_env.bat
echo echo Environment activated! >> activate_env.bat
echo echo. >> activate_env.bat
echo echo Available commands: >> activate_env.bat
echo echo   python scripts/test_communication.py  - Test communication protocols >> activate_env.bat
echo echo   python -m pytest                      - Run all tests >> activate_env.bat
echo echo   jupyter notebook                      - Start Jupyter notebook >> activate_env.bat
echo cmd /k >> activate_env.bat

REM Create test script
echo @echo off > run_tests.bat
echo call venv\Scripts\activate.bat >> run_tests.bat
echo python scripts\test_communication.py >> run_tests.bat
echo pause >> run_tests.bat

REM Create Jupyter launcher
echo @echo off > start_jupyter.bat
echo call venv\Scripts\activate.bat >> start_jupyter.bat
echo jupyter notebook >> start_jupyter.bat

echo.
echo ==========================================
echo Setup completed successfully!
echo ==========================================
echo.
echo Next steps:
echo 1. Run 'activate_env.bat' to activate the environment
echo 2. Run 'run_tests.bat' to test the installation
echo 3. Run 'start_jupyter.bat' to start Jupyter notebook
echo.
echo For ROS2 development:
echo 1. Install ROS2 for Windows from:
echo    https://docs.ros.org/en/humble/Installation/Windows-Install-Binary.html
echo 2. Follow the ROS2 Windows setup guide
echo.
echo Development files created:
echo - activate_env.bat     : Activate development environment
echo - run_tests.bat        : Run communication tests
echo - start_jupyter.bat    : Start Jupyter notebook
echo.
echo [SUCCESS] Environment setup complete! 🎉

pause
