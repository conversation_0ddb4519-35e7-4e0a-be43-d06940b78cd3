{"name": "@jupyterlab/apputils-extension", "version": "4.4.3", "description": "JupyterLab - Application Utilities Extension", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/*.d.ts", "lib/*.js.map", "lib/*.js", "style/*.css", "style/images/*.svg", "schema/*.json", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "watch": "tsc -b --watch"}, "dependencies": {"@jupyterlab/application": "^4.4.3", "@jupyterlab/apputils": "^4.5.3", "@jupyterlab/coreutils": "^6.4.3", "@jupyterlab/docregistry": "^4.4.3", "@jupyterlab/mainmenu": "^4.4.3", "@jupyterlab/rendermime-interfaces": "^3.12.3", "@jupyterlab/services": "^7.4.3", "@jupyterlab/settingregistry": "^4.4.3", "@jupyterlab/statedb": "^4.4.3", "@jupyterlab/statusbar": "^4.4.3", "@jupyterlab/translation": "^4.4.3", "@jupyterlab/ui-components": "^4.4.3", "@jupyterlab/workspaces": "^4.4.3", "@lumino/algorithm": "^2.0.3", "@lumino/commands": "^2.3.2", "@lumino/coreutils": "^2.2.1", "@lumino/disposable": "^2.1.4", "@lumino/domutils": "^2.0.3", "@lumino/polling": "^2.1.4", "@lumino/widgets": "^2.7.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-toastify": "^9.0.8"}, "devDependencies": {"rimraf": "~5.0.5", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}